import { Title, Text, Badge } from 'rizzui';

interface DividerProps {
    text?: string;
    color?: string;
    thickness?: string;
    className?: string;
}

const Divider: React.FC<DividerProps> = ({
                                             text = '',
                                             color = 'gray-300',
                                             thickness = '1px',
                                             className = '',
                                         }) => {
    return (
        <div className={`relative flex items-center ${className}`}>
            <div className={`flex-grow border-t border-${color}`} style={{ borderTopWidth: thickness }}></div>
            {text && (
                <span className={`mx-4 text-${color} text-sm font-medium`}>{text}</span>
            )}
            <div className={`flex-grow border-t border-${color}`} style={{ borderTopWidth: thickness }}></div>
        </div>
    );
};
export default function ExpandedRow(record: any) {
    if (!record) {
        return <Text>No current driver data associated with this car.</Text>;
    }

    return (
        <div className="grid grid-cols-1 divide-y px-4 py-5 dark:bg-gray-800 shadow-lg rounded-lg">
            <article key={record.id} className="flex flex-col space-y-4">
                <div className="flex items-start space-x-4">
                    <img
                        src={record.car?.image}
                        alt={`${record.car?.brand} ${record.car?.model}`}
                        className="w-32 h-20 object-cover rounded-lg shadow-md"
                    />
                    <div className="flex flex-col justify-between">
                        <Title as="h3" className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                            {record.first_name} {record.last_name}
                        </Title>
                        <Text className="text-sm text-gray-600 dark:text-gray-400">
                            Driver ID: {record.id}
                        </Text>
                        <Text className="text-sm text-gray-600 dark:text-gray-400">
                            Car: {record.car?.brand} {record.car?.model}
                        </Text>
                        <Text className="text-sm text-gray-600 dark:text-gray-400">
                            Licence Plate: {record.car?.licence_plate}
                        </Text>
                        <Badge variant="solid" className="mt-2">
                            {record.car?.allow_pets ? 'Pets Allowed' : 'No Pets Allowed'}
                        </Badge>
                    </div>
                </div>

                <Divider />

                <div className="grid grid-cols-2 gap-4">
                    <div>
                        <Text className="text-sm text-gray-500 dark:text-gray-400">Bags Capacity</Text>
                        <Title as="h4" className="text-base font-semibold text-gray-900 dark:text-gray-100">
                            {record.car?.bags_capacity} Bags
                        </Title>
                    </div>
                    <div>
                        <Text className="text-sm text-gray-500 dark:text-gray-400">Created At</Text>
                        <Title as="h4" className="text-base font-semibold text-gray-900 dark:text-gray-100">
                            {new Date(record.created_at).toLocaleDateString()}
                        </Title>
                    </div>
                    <div>
                        <Text className="text-sm text-gray-500 dark:text-gray-400">Updated At</Text>
                        <Title as="h4" className="text-base font-semibold text-gray-900 dark:text-gray-100">
                            {new Date(record.updated_at).toLocaleDateString()}
                        </Title>
                    </div>
                    <div>
                        <Text className="text-sm text-gray-500 dark:text-gray-400">Car ID</Text>
                        <Title as="h4" className="text-base font-semibold text-gray-900 dark:text-gray-100">
                            {record.car?.id}
                        </Title>
                    </div>
                </div>
            </article>
        </div>
    );
}
