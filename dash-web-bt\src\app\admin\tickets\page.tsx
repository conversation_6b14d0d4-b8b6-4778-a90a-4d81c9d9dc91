'use client';

import ScheduleTable from "./table";
import {usePaginatedTickets} from "@/services/query/ticket/usePaginatedTickets";
import AppointmentListPageHeader from "./page-header";
import React, {useState} from "react";
import {Loader} from "rizzui";

export default function CustomersPage() {
    const [currentPage, setCurrentPage] = useState(1);
    const [currentLimit, setCurrentLimit] = useState(10);

    const {
        isPending,
        isError,
        error,
        data,
        isFetching,
        isPlaceholderData,
        refetch,
        status,
    } = usePaginatedTickets(currentPage, currentLimit);

    const onSelectedPage = (page: number) => {
        setCurrentPage(page);
    }

    const onPageLimitChange = (limit: number) => {
        setCurrentLimit(limit);
    }

    const onFiltersChanged = (filters: any) => {
        refetch();
    }

    return <>
        <AppointmentListPageHeader />

        <div className="flex flex-col gap-10 @container">
            {
                data ?
                    <ScheduleTable
                        initialData={data}
                        onSelectedPage={onSelectedPage}
                        onPageLimitChange={onPageLimitChange}
                        variant="elegant"
                        className="[&_.table-filter]:hidden [&_.table-pagination]:hidden"
                        refetch={refetch}
                    /> : <div className="grid h-32 flex-grow place-content-center items-center">
                        <Loader size="lg"/>
                    </div>
            }
        </div>
    </>;
}
