import { getRepository } from 'typeorm';
import { Role } from '../../models/Role';
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const createRole = async (data: Partial<Role>) => {
  const roleRepository = getRepository(Role);
  try {
    const newRole = roleRepository.create(data);
    await roleRepository.save(newRole);
    return { success: true, data: newRole, error: null };
  } catch (error) {
    const interpretedError = interpretDatabaseError(error);
    return { success: false, data: null, error: interpretedError };
  }
};
