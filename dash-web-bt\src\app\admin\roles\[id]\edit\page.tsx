'use client';

import { routes } from '@/config/routes';
import PageHeader from '@/app/shared/lib/page-header';
import CreateEdit from "@/app/shared/app/roles/create-edit";
import {useGetRole} from "@/services/query/role/useGetRole";



const pageHeader = {
  title: 'Edit Role',
  breadcrumb: [
    {
      href: routes.dashboard,
      name: 'Dashboard',
    },
    {
      href: routes.roles,
      name: 'Roles',
    },
    {
      name: 'Edit Role',
    },
  ],
};

export default function Edit({
  params,
}: {
  params: { id: string };
}) {
  const id = params.id;

  const {
    // isPending,
    // isError,
    // error,
    data,
    // isFetching,
    // isPlaceholderData,
    // refetch,
    // status,
  } = useGetRole(id);

  return (
    <>
      <PageHeader title={pageHeader.title} breadcrumb={pageHeader.breadcrumb}>
        {/*<ImportButton title={'Import File'} />*/}
      </PageHeader>

      {
        data ? <CreateEdit id={id} role={data.data} /> : null
      }
    </>
  );
}
