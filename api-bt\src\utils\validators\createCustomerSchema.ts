import * as z from 'zod';
export const CustomerSchema = z.object({
    id: z.string().uuid().optional(),
    first_name: z.string().max(50),
    last_name: z.string().max(50),
    phone_number: z.string().max(30),
    is_phone_validated: z.boolean().default(false),
    user: z.string().uuid().optional().nullable(), // Assuming you're referencing by ID
    tickets: z.array(z.any()).optional(),
    stripe_customer_id: z.string().nullable().optional(),
    created_at: z.string().optional(),
    updated_at: z.string().optional(),
    updated_by: z.number().int().nullable().optional(),
});
