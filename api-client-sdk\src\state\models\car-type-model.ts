import { types, Instance, SnapshotIn } from 'mobx-state-tree';

/**
 * Car Type model in MST
 */
export const CarTypeModel = types.model('CarTypeModel', {
  id: types.identifier,
  name: types.string,
  description: types.maybeNull(types.string),
  price_per_km: types.number,
  base_fare: types.number,
  price_per_minute_wait: types.maybeNull(types.number),
  image: types.maybeNull(types.string),
  capacity: types.number,
  created_at: types.string, 
  updated_at: types.string, 
  updated_by: types.maybeNull(types.number),
})
.views(self => ({
  /**
   * Get created date as Date object
   */
  get createdDate(): Date {
    return new Date(self.created_at);
  },
  
  /**
   * Get updated date as Date object
   */
  get updatedDate(): Date {
    return new Date(self.updated_at);
  },
  
  /**
   * Get display name with capacity
   */
  get displayName(): string {
    return `${self.name} (${self.capacity} seats)`;
  }
}));

// Type definitions for TypeScript
export interface CarType extends Instance<typeof CarTypeModel> {}
export interface CarTypeSnapshotIn extends SnapshotIn<typeof CarTypeModel> {} 