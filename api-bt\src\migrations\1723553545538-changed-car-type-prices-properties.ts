import {MigrationInterface, QueryRunner, TableColumn} from "typeorm";

export class changedCarTypePricesProperties1723553545538 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        const table = await queryRunner.getTable('car_type_price');

        // Check if the 'min_distance' column exists
        const minDistanceColumn = table!.findColumnByName('min_distance');
        if (minDistanceColumn) {
            // Remove the 'min_distance' column from the 'car_type_price' table
            await queryRunner.dropColumn('car_type_price', new TableColumn({
                name: 'min_distance',
                type: 'int4',
                isNullable: true,
            }));
        }

        // Check if the 'max_distance' column exists
        const maxDistanceColumn = table!.findColumnByName('max_distance');
        if(maxDistanceColumn) {
            // If it does, it means it should be renamed.
            // Rename max_distance column to range_max_distance
            await queryRunner.renameColumn('car_type_price', 'max_distance', 'range_max_distance');
        }

        // Check if the 'fixed_charge_value' column exists
        const fixedChargeValueColumn = table!.findColumnByName('fixed_charge_value');
        if(!fixedChargeValueColumn) {
            // If it does not, create it.
            await queryRunner.addColumn('car_type_price', new TableColumn({
                name: 'fixed_charge_value',
                type: 'int4',
                isNullable: false,
            }));
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        const table = await queryRunner.getTable('car_type_price');

        // Check if the 'min_distance' column exists
        const minDistanceColumn = table!.findColumnByName('min_distance');
        if (!minDistanceColumn) {
            // Add the 'min_distance' column to the 'car_type_price' table if it doesn't.
            await queryRunner.addColumn('car_type_price', new TableColumn({
                name: 'min_distance',
                type: 'int4',
                isNullable: true,
            }));
        }

        // Check if the 'max_distance' column exists
        const maxDistanceColumn = table!.findColumnByName('max_distance');
        if(!maxDistanceColumn) {
            // If it doesn't, and range_max_distance exists, it means it should be renamed.
            const rangeMaxDistanceColumn = table!.findColumnByName('range_max_distance');
            if(rangeMaxDistanceColumn) {
                // Rename range_max_distance column to max_distance
                await queryRunner.renameColumn('car_type_price', 'range_max_distance', 'max_distance');
            }
        }

        // Check if the 'fixed_charge_value' column exists
        const fixedChargeValueColumn = table!.findColumnByName('fixed_charge_value');
        if(fixedChargeValueColumn) {
            // If it does, remove it.
            await queryRunner.dropColumn('car_type_price', new TableColumn({
                name: 'fixed_charge_value',
                type: 'int4',
                isNullable: false,
            }));
        }
    }

}
