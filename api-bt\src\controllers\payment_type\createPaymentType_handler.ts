import { Request, Response } from 'express';
import {
    createPaymentType,
} from '../../services/payment-type'; // Adjust the import path as necessary

export const createPaymentTypeHandler = async (req: Request, res: Response) => {
    try {
        const data = req.body;
        const result = await createPaymentType(data);
        if (!result.success) {
            return res.status(400).json(result);
        }
        return res.status(201).json(result);
    } catch (error: any) {
        return res.status(500).json({
            success: false,
            message: 'Failed to create payment type',
            error: error.message,
        });
    }
};
