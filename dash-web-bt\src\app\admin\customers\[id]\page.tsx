import { PiPrinterBold, PiDownloadSimpleBold } from 'react-icons/pi';
import { routes } from '@/config/routes';
import { Button } from 'rizzui';
import PageHeader from '@/app/shared/lib/page-header';
import { metaObject } from '@/config/site.config';

export const metadata = {
  ...metaObject('Customer Details'),
};

const pageHeader = {
  title: 'Customer Details',
  breadcrumb: [
    {
      href: routes.dashboard,
      name: 'Dashboard',
    },
    {
      href: routes.customers,
      name: 'Customers',
    },
    {
      name: 'Customer Details',
    },
  ],
};

export default function Page() {
  return (
    <>
      <PageHeader title={pageHeader.title} breadcrumb={pageHeader.breadcrumb}>
        <div className="mt-6 flex items-center gap-4 @2xl:mt-0">
          <Button className="w-full gap-2 @lg:w-auto" variant="outline">
            <PiPrinterBold className="h-4 w-4" />
            Print
          </Button>
          <Button className="w-full gap-2 @lg:w-auto">
            <PiDownloadSimpleBold className="h-4 w-4" />
            Download
          </Button>
        </div>
      </PageHeader>

      <div className="mt-2 flex flex-col gap-y-6 @container sm:gap-y-10">
      </div>
    </>
  );
}
