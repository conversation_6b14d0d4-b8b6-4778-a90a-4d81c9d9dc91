import express from 'express';
import {
  createDriver_handler,
  deleteDriver_handler,
  getAllDrivers_handler,
  getDriver_handler,
  updateDriver_handler,
} from '../../controllers/driver';

export const driverRouter = express.Router();

driverRouter.post('/drivers', createDriver_handler);
driverRouter.get('/drivers', getAllDrivers_handler);
driverRouter.get('/drivers/:id', getDriver_handler);
driverRouter.put('/drivers/:id', updateDriver_handler);
driverRouter.delete('/drivers/:id', deleteDriver_handler);
