import { Request, Response } from 'express';
import {
  createZone,
} from '../../services/zone';

export const createZone_handler = async (req: Request, res: Response) => {
  try {
    const data = req.body;
    const result = await createZone(data);
    if (!result.success) {
      return res.status(404).json(result);
    }
    return res.json(result);
  } catch (error: any) {
    return res.status(500).json({
      success: false,
      message: 'Failed to create zone',
      errorType: 'InternalServerError',
      data: undefined,
    });
  }
};
