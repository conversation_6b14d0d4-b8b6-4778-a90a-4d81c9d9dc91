'use client';

import FormGroup from "@/app/shared/lib/form-group";
import {Button, Checkbox, Input, Loader, Text} from "rizzui";
import {SubmitHandler} from "react-hook-form";
import FormFooter from "@/components/lib/form-footer";
import {Form} from "@/components/lib/ui/form";
import {useCreatePaymentType} from "@/services/mutations/payment-type/create";
import React, {useEffect, useState} from "react";
import toast from "react-hot-toast";
import {
    PaymentMethodFormTypes,
    paymentMethodFormSchema,
    defaultValues,
    PaymentMethodFormUpdateSchemaTypes
} from "@/utils/validators/app/forms/payment-methods.schema";
import axios from "axios";
import {usePaginatedPaymentTypes} from "@/services/query/payment-type/admin/usePaginatedPaymentTypes";
import {useDeletePaymentType, useUpdatePaymentType} from "@/services/mutations/payment-type";
import DeletePopover from "@/app/shared/lib/delete-popover";
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PiMoney, PiCreditCard, PiX} from 'react-icons/pi';

function trimString(text: string, maxLength: number = 50): string {
    return text.length > maxLength ? text.substring(0, maxLength) + '[...]' : text;
}

interface PaymentMethodsListPropsType {
    refetchTrigger: boolean;
    setRefetchTrigger: (value: boolean) => void;
    submitPaymentType: (data: PaymentMethodFormTypes) => void;
}

const BLOCK_REMOVAL_BY_NAME = ['Stripe', 'Cash'];

function PaymentMethodsList({ refetchTrigger, setRefetchTrigger, submitPaymentType }: PaymentMethodsListPropsType) {
    const [editModeData, setEditModeData] = useState<any>(null);
    const [stripeExists, setStripeExists] = useState<PaymentMethodFormTypes | null>(null);
    const [cashExists, setCashExists] = useState<PaymentMethodFormTypes | null>(null);
    const {
        // isPending,
        // isError,
        error,
        data: paginatedPaymentTypes,
        isFetching,
        // isPlaceholderData,
        refetch,
        // status,
    } = usePaginatedPaymentTypes();

    const pmExists = (pm: string) => {
        if(paginatedPaymentTypes) {
            return paginatedPaymentTypes.data.find((pmi) => pmi.name === pm);
        }
        return false;
    }

    const deletePaymentType = useDeletePaymentType();
    const {
        mutate: updatePaymentType,
        isPending: updatePaymentTypePending,
    } = useUpdatePaymentType();

    useEffect(() => {
        if (refetchTrigger) {
            refetch().then(_r => {});
            setRefetchTrigger(false);  // Reset the trigger
        }
    }, [refetchTrigger, setRefetchTrigger]);

    useEffect(() => {
        if(paginatedPaymentTypes) {
            setStripeExists(pmExists('Card'));
            setCashExists(pmExists('Cash'));
        }
    }, [paginatedPaymentTypes])

    if (isFetching) return (
        <div className="grid h-32 flex-grow place-content-center items-center">
            <Loader size="lg"/>
        </div>
    );

    if (error instanceof Error) return <div>An error occurred: {error.message}</div>;

    const canRemove = (id: string, shouldToast: boolean = false) => {
        const toRemove = paginatedPaymentTypes?.data.find((pm) => pm.id === id)?.name;
        if(BLOCK_REMOVAL_BY_NAME.includes(toRemove)) {
            shouldToast && toast.error('You cannot remove this payment method !');
            return false;
        }
        return true;
    }

    const deletePaymentMethod = (id: string) => {
        // # Validation
        if(!canRemove(id, true)) return;

        deletePaymentType.mutate(id, {
            onError: (error: any) => {
                // Check if the error is an AxiosError and has a response
                if (axios.isAxiosError(error) && error.response) {
                    // Now TypeScript knows error.response exists and its structure
                    toast.error(error.response.data.msg);
                } else {
                    // Handle other errors
                    toast.error("An error occurred while trying to remove the payment type.");
                }
            },
            onSuccess: async () => {
                await refetch();
            },
        })
    }

    const update = (id: string, data: PaymentMethodFormTypes) => {
        updatePaymentType({
            id: id,
            payload: data,
        }, {
            onError: (error: any) => {
                // Check if the error is an AxiosError and has a response
                if (axios.isAxiosError(error) && error.response) {
                    // Now TypeScript knows error.response exists and its structure
                    toast.error(error.response.data.msg);
                } else {
                    // Handle other errors
                    toast.error("An error occurred");
                }
            },
            onSuccess: () => {
                setEditModeData(null)
                refetch().then(_r => {});
            },
        });
    }

    const updateMethodField = (property: string, value: string | boolean)  => {
        const newPrev = {
            ...editModeData,
            [property]: value,
        };
        setEditModeData(newPrev);
    }

    const saveMethod = () => {
        if(editModeData) {
            update(editModeData.id, editModeData);
        }
    }

    const setEditingMode = (method: PaymentMethodFormTypes | null) => {
        setEditModeData(method)
    };

    const enableStripePayment = () => {
        if(paginatedPaymentTypes) {
            const exists = pmExists('Card');
            if(!exists) {
                submitPaymentType({
                    name: 'Card',
                    description: 'Facilitate Card Payment via Stripe.',
                    disabled: false,
                })
            }
            if(exists && exists.disabled) {
                update(exists.id, {
                    ...exists,
                    disabled: false,
                });
            }
        }
    }

    const enableCashPayment = () => {
        if(paginatedPaymentTypes) {
            const exists = pmExists('Cash');
            if(!exists) {
                submitPaymentType({
                    name: 'Cash',
                    description: 'Pay Cash directly to the driver.',
                    disabled: false,
                })
            }
            if(exists?.disabled) {
                update(exists.id, {
                    ...exists,
                    disabled: false,
                });
            }
        }
    }

    const prioritizeAndSortPaymentTypes = (paymentTypes: PaymentMethodFormUpdateSchemaTypes[]): PaymentMethodFormUpdateSchemaTypes[] => {
        const priorityNames = ['Stripe', 'Cash'];

        return paymentTypes.sort((a, b) => {
            const nameA = a.name;
            const nameB = b.name;

            // Check if both names are either 'Stripe' or 'Cash'
            if (priorityNames.includes(nameA) && priorityNames.includes(nameB)) {
                return priorityNames.indexOf(nameA) - priorityNames.indexOf(nameB);
            }

            // Prioritize 'Stripe' or 'Cash' over other names
            if (priorityNames.includes(nameA)) {
                return -1; // a comes before b
            }
            if (priorityNames.includes(nameB)) {
                return 1; // b comes after a
            }

            // If neither name is 'Stripe' or 'Cash', sort alphabetically
            return nameA.localeCompare(nameB);
        });
    };

    return (
        <div className="max-w-md">

            <div className="flex flex-col md:flex-row mb-2">
                {
                    !stripeExists || stripeExists?.disabled ? (
                        <div>
                            <Button
                                type="button"
                                isLoading={isFetching}
                                onClick={enableStripePayment}
                                className="w-full md:w-auto bg-transparent border-dashed-deepOrange text-sky-950 hover:bg-black hover:text-white mr-2">
                                <PiCreditCard className="me-1 h-[17px] w-[17px]"/>
                                Enable Card (via Stripe)
                            </Button>
                        </div>
                    ) : null
                }

                {
                    !cashExists || cashExists?.disabled ? (
                        <div>
                            <Button
                                type="button"
                                isLoading={isFetching}
                                onClick={enableCashPayment}
                                className="w-full md:w-auto bg-transparent border-dashed-deepOrange text-sky-950 hover:bg-black hover:text-white mr-2">
                                <PiMoney className="me-1 h-[17px] w-[17px]"/>
                                Enable Cash
                            </Button>
                        </div>
                    ) : null
                }

            </div>

            <ul className="divide-y divide-gray-200">

                {
                    editModeData ? (
                        <div>
                            <Input
                                className="col-span-full mb-2"
                                prefix={
                                    <PiPen className="h-6 w-6 text-gray-500"/>
                                }
                                type="text"
                                disabled={editModeData.name === 'Card' || editModeData.name === 'Cash'}
                                value={editModeData.name || ''}
                                onChange={(e) => updateMethodField('name', e.target.value)}
                            />
                            <Input
                                className="col-span-full mb-2"
                                prefix={
                                    <PiPen className="h-6 w-6 text-gray-500"/>
                                }
                                type="text"
                                value={editModeData.description || ''}
                                onChange={(e) => updateMethodField('description', e.target.value)}
                            />
                            <div className="ps-3.5 mt-4">
                                <Checkbox
                                    title={' Disabled'}
                                    onChange={(e) => updateMethodField('disabled', e.target.checked)}
                                    checked={editModeData.disabled}
                                    className="cursor-pointer"
                                    label="Disabled"
                                />
                            </div>
                            <button
                                type="button"
                                className="text-green-600 hover:text-green-800">
                            </button>
                            <div>
                                <Button type="button" isLoading={updatePaymentTypePending} onClick={() => saveMethod()}
                                        className="w-full @xl:w-auto bg-black text-chillGold !hover:bg-secondary hover:text-black">
                                    <PiDiscFill className="me-1 h-[17px] w-[17px]"/>
                                    Save
                                </Button>
                                <Button type="button" isLoading={updatePaymentTypePending}
                                        onClick={() => setEditingMode(null)}
                                        className="w-full @xl:w-auto bg-transparent text-chillGold !hover:bg-transparent hover:text-black ml-2 text-center">
                                    <PiX className="me-1 h-[17px] w-[17px]"/>
                                </Button>
                            </div>
                        </div>
                    ) : (
                        <>
                            {paginatedPaymentTypes?.data && prioritizeAndSortPaymentTypes(paginatedPaymentTypes?.data)?.map((method) => (
                                <li key={method.id}
                                    className="py-4 flex items-center justify-between bg-white shadow rounded-lg p-4 mb-3 cursor-pointer">
                                    <div className="flex-grow" onClick={() => setEditingMode(method)}>
                                        <div>
                                            <h3 className="text-lg font-semibold text-blue-800">{method.name}</h3>
                                            <p className="text-sm text-gray-600">{trimString(method.description) || 'No description available.'}</p>
                                        </div>
                                        <span
                                            className="text-xs text-gray-400">Updated: {new Date(method.updated_at).toLocaleString()}</span>
                                    </div>
                                    <div className="flex items-center gap-2 ml-4">
                                        <button
                                            onClick={() => setEditingMode(method)}
                                            type="button"
                                            className="cursor-pointer hover:!border-gray-900 hover:text-gray-700">
                                            <PiPen className="me-1 h-[17px] w-[17px]"/>
                                        </button>
                                        {
                                            canRemove(method.id) ? <DeletePopover
                                                title={`Delete Payment Method`}
                                                description={`Are you sure you want to delete this payment method ?`}
                                                onDelete={() => deletePaymentMethod(method.id)}
                                            /> : null
                                        }
                                    </div>
                                </li>
                            ))}
                        </>
                    )
                }
            </ul>
        </div>
    );
}

export default function PaymentMethodsFormPage() {
    const createPaymentType = useCreatePaymentType();
    const [isLoading, setLoading] = useState(false);
    const [refetchList, setRefetchList] = useState(false);

    const create = (data: PaymentMethodFormTypes) => {
        createPaymentType.mutate({
            payload: data,
        }, {
            onError: (error: any) => {
                // Check if the error is an AxiosError and has a response
                if (axios.isAxiosError(error) && error.response) {
                    // Now TypeScript knows error.response exists and its structure
                    toast.error(error.response.data.msg);
                } else {
                    // Handle other errors
                    toast.error("An error occurred");
                }
                setLoading(false);
            },
            onSuccess: () => {
                toast.success(<Text as="b">New payment type created.</Text>);
                setLoading(false);
                setRefetchList(prev => !prev);
            },
        });
    }
    const onSubmit: SubmitHandler<PaymentMethodFormTypes> = async (data) => {
        // # Validations
        if (!data.name || !data.description) {
            toast.error('Please add a name and a small description for the payment type.')
            return;
        }
        setLoading(true);
        create(data)
    };

    const submitPaymentType = (data: PaymentMethodFormTypes) => {
        onSubmit(data);
    }

    return (
        <Form<PaymentMethodFormTypes>
            validationSchema={paymentMethodFormSchema}
            onSubmit={onSubmit}
            resetValues={defaultValues}
            className="@container"
            useFormProps={{
                mode: 'onChange',
                defaultValues: defaultValues,
                resetOptions: {
                    keepDefaultValues: true,
                },
            }}
        >
            {({register, formState: {errors}}) => {
                return (
                    <>
                        {createPaymentType.isError && (
                            <p>An error occurred: {createPaymentType.error.message}</p>
                        )}

                        <FormGroup
                            title="Payment Type Settings"
                            description="Manage your payment types here."
                            className="pt-7 @2xl:pt-9 @3xl:grid-cols-12 @3xl:pt-11"
                        />

                        <div className="mt-3">
                            <PaymentMethodsList refetchTrigger={refetchList} setRefetchTrigger={setRefetchList} submitPaymentType={submitPaymentType} />
                        </div>

                        <div>
                            <FormGroup
                                title="Create Custom Payment Type"
                                className="pt-7 @2xl:pt-9 @3xl:grid-cols-12 @3xl:pt-11"
                            >
                                <Input
                                    placeholder="Payment Type Name"
                                    {...register('name')}
                                    error={errors.name?.message}
                                    className="flex-grow"
                                />
                                <Input
                                    placeholder="Payment Type Description"
                                    {...register('description')}
                                    error={errors.description?.message}
                                    className="flex-grow"
                                />
                                <div className="ps-3.5 mt-4">
                                    <Checkbox
                                        {...register('disabled')}
                                        title={' Disabled'}
                                        className="cursor-pointer"
                                        label="Disabled"
                                    />
                                </div>
                            </FormGroup>
                        </div>

                        <FormFooter
                            isLoading={isLoading}
                            altBtnText="Cancel"
                            submitBtnText="Add Payment Type"
                            className="mt-10"
                        />
                    </>
                );
            }}
        </Form>
    );
}
