// services/payment.ts
import { getRepository } from 'typeorm';
import { Payment } from '../../models/Payment';
import {paginate, PaginatedResult} from "../../utils/pagination";

export const getAllPayments = async (
    page = 1,
    limit = 10,
): Promise<PaginatedResult<Payment>> => {
  const paymentRepository = getRepository(Payment);

  const options: any = {
    skip: (page - 1) * limit,
    take: limit,
    relations: ['type', 'booking', 'booking.customer'],
    order: {
      created_at: 'DESC',  // Order by created_at in descending order
    },
  };

  const [data, totalRecords] = await paymentRepository.findAndCount(options);

  return paginate<Payment>(data, page, limit, totalRecords);
};
