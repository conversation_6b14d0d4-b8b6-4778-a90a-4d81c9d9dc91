
import { getRepository } from 'typeorm';
import { Booking } from '../../models/Booking';
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const getBookingByBookingCode = async (booking_code: string) => {
  const bookingRepository = getRepository(Booking);
  try {
    const booking = await bookingRepository.findOne({
      booking_code: booking_code,
    }, {
      relations: ['car_type', 'customer', 'payment', 'car', 'car.type'],
    });
    if (!booking) {
      return { success: false, data: null, error: 'Booking not found' };
    }
    return { success: true, data: booking, error: null };
  } catch (error) {
    const interpretedError = interpretDatabaseError(error);
    return { success: false, data: null, error: interpretedError };
  }
};
