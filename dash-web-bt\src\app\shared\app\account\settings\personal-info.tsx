'use client';

import dynamic from 'next/dynamic';
import toast from 'react-hot-toast';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, Controller} from 'react-hook-form';
import {PiEnvelopeSimple} from 'react-icons/pi';
import {Form} from '@/components/lib/ui/form';
import {Text, Input} from 'rizzui';
import FormGroup from '@/app/shared/lib/form-group';
import FormFooter from '@/components/lib/form-footer';
import {
    personalInfoFormSchema,
    PersonalInfoFormTypes,
} from '@/utils/validators/app/forms/personal-info.schema';
import {ProfileHeader} from "@/app/shared/app/account/settings/profile-header";
import {useSession, signOut} from "next-auth/react";
import {useUpdateUser} from "@/services/mutations/user/update";
import {useState} from "react";
import axios from "axios";

const QuillEditor = dynamic(() => import('@/components/lib/ui/quill-editor'), {
    ssr: false,
});

export default function PersonalInfoView() {
    const updateUser = useUpdateUser();
    // get next auth session client side
    const {data: session} = useSession();
    const [isLoading, setLoading] = useState(false);

    const onSubmit: SubmitHandler<PersonalInfoFormTypes> = async (data: any) => {
        setLoading(true);

        updateUser.mutate(data, {
            onError: (error: any) => {
                // Check if the error is an AxiosError and has a response
                if (axios.isAxiosError(error) && error.response) {
                    // Now TypeScript knows error.response exists and its structure
                    toast.error(error.response.data.msg);
                } else {
                    // Handle other errors
                    toast.error("An error occurred");
                }
                setLoading(false);
            },
            onSuccess: () => {
                toast.success(<Text as="b">Successfully changed your information ! Please re-log.</Text>);
                setLoading(false);
                signOut();
            },
        });
    };

    return (
        <Form<PersonalInfoFormTypes>
            validationSchema={personalInfoFormSchema}
            // resetValues={reset}
            onSubmit={onSubmit}
            className="@container"
            useFormProps={{
                mode: 'onChange',
                defaultValues: session?.user?.user,
            }}
        >
            {({register, control, setValue, getValues, formState: {errors}}) => {
                return (
                    <>
                        {updateUser.isError && (
                            <p>An error occurred: {updateUser.error.message}</p>
                        )}

                        <ProfileHeader
                            title={`${session?.user?.user?.username}`}
                            bio={`${session?.user?.user?.bio}`}
                            avatar={session?.user?.user?.avatar_url}
                        >
                        </ProfileHeader>
                        <FormGroup
                            title="Personal Info"
                            description="Update your photo and personal details here"
                            className="pt-7 @2xl:pt-9 @3xl:grid-cols-12 @3xl:pt-11"
                        />
                        <div className="mb-10 grid gap-7 divide-y divide-dashed divide-gray-200 @2xl:gap-9 @3xl:gap-11">
                            <FormGroup
                                title="Account Name"
                                className="pt-7 @2xl:pt-9 @3xl:grid-cols-12 @3xl:pt-11"
                            >
                                <Input
                                    placeholder="User Name"
                                    {...register('username')}
                                    error={errors.first_name?.message}
                                    className="flex-grow"
                                />
                            </FormGroup>

                            <FormGroup
                                title="Name"
                                className="pt-7 @2xl:pt-9 @3xl:grid-cols-12 @3xl:pt-11"
                            >
                                <Input
                                    placeholder="First Name"
                                    {...register('first_name')}
                                    error={errors.first_name?.message}
                                    className="flex-grow"
                                />
                                <Input
                                    placeholder="Last Name"
                                    {...register('last_name')}
                                    error={errors.last_name?.message}
                                    className="flex-grow"
                                />
                            </FormGroup>

                            <FormGroup
                                title="Email Address"
                                className="pt-7 @2xl:pt-9 @3xl:grid-cols-12 @3xl:pt-11"
                            >
                                <Input
                                    className="col-span-full"
                                    prefix={
                                        <PiEnvelopeSimple className="h-6 w-6 text-gray-500"/>
                                    }
                                    type="email"
                                    placeholder="<EMAIL>"
                                    {...register('email')}
                                    error={errors.email?.message}
                                />
                            </FormGroup>

                            <FormGroup
                                title="Your avatar url"
                                className="pt-7 @2xl:pt-9 @3xl:grid-cols-12 @3xl:pt-11"
                            >
                                <Input
                                    placeholder="Avatar URL"
                                    {...register('avatar_url')}
                                    error={errors.avatar_url?.message}
                                    className="flex-grow"
                                />
                            </FormGroup>

                            <FormGroup
                                title="Country"
                                className="pt-7 @2xl:pt-9 @3xl:grid-cols-12 @3xl:pt-11"
                            >
                                <Input
                                    placeholder="Country"
                                    {...register('country')}
                                    error={errors.country?.message}
                                    className="flex-grow"
                                />
                            </FormGroup>

                            <FormGroup
                                title="Bio"
                                className="pt-7 @2xl:pt-9 @3xl:grid-cols-12 @3xl:pt-11"
                            >
                                <Controller
                                    control={control}
                                    name="bio"
                                    render={({field: {onChange, value}}) => (
                                        <QuillEditor
                                            value={value}
                                            onChange={onChange}
                                            className="@3xl:col-span-2 [&>.ql-container_.ql-editor]:min-h-[100px]"
                                            labelClassName="font-medium text-gray-700 dark:text-gray-600 mb-1.5"
                                        />
                                    )}
                                />
                            </FormGroup>
                        </div>

                        <FormFooter
                            isLoading={isLoading}
                            altBtnText="Cancel"
                            submitBtnText="Save"
                        />
                    </>
                );
            }}
        </Form>
    );
}
