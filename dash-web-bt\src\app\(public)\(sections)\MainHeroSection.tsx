'use client';

import Spotlight from "@/app/(public)/(sections)/Spotlight";
import {TypewriterEffect} from "@/components/app/TypewriterEffect";
import {Link} from "react-scroll";
import {MBButton} from "@/components/app/MovingBorder";
import React from "react";
import {useGetServiceAvailability} from "@/services/query/booking/public/useGetServiceAvailability";
import {Loader} from "rizzui";
import {useI18n} from "@/hooks/use-translation";

export default function MainHeroSection() {
    const {
        data: serviceAvailability,
        isFetching: serviceAvailabilityFetching,
    } = useGetServiceAvailability();
    const { t } = useI18n();

    const words = [
        {
            text: t("main.hero.words.one"),
        },
        {
            text: t("main.hero.words.two"),
        },
        {
            text: t("main.hero.words.three"),
        },
        {
            text: t("main.hero.words.four"),
            className: "text-chillGold",
        },
        {
            text: t("main.hero.words.five"),
            className: "text-chillGold dark:text-chillGold",
        },
    ];

    return (
        <main className="flex min-h-screen flex-col items-center justify-center p-20 bg-black">

            <Spotlight fill="#ffbf00"/>

            <div className="flex flex-col items-center justify-center h-[40rem]  ">
                <p className="text-neutral-600 dark:text-neutral-200 text-xs sm:text-base mb-7 ">
                    {t('main.hero.journey_starts')}
                    <Link
                        to={'book'}
                    >
                        <span className='text-chillGold cursor-pointer'> {t('main.hero.journey_starts.here')}</span>
                    </Link>
                </p>

                <TypewriterEffect words={words} className="mb-12 text-2xl"/>

                {
                    serviceAvailabilityFetching ? (
                        <div className="grid h-32 flex-grow place-content-center items-center">
                            <Loader size="lg"/>
                        </div>
                    ) : null
                }

                {
                    !serviceAvailabilityFetching && (
                        serviceAvailability?.data.canBook ? (
                            <div className="flex flex-col md:flex-row space-y-4 md:space-y-0 space-x-0 md:space-x-4">
                                <Link
                                    to={'book'}
                                >
                                    <MBButton className="text-chillGold">
                                        {t('home.cto.btn')}
                                    </MBButton>
                                </Link>
                            </div>
                        ) : <p className="mt-6 text-base text-creamyWhite">
                            {t('main.hero.service_unavailable')}
                        </p>
                    )
                }

            </div>

        </main>
    )
}
