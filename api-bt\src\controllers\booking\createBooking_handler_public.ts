import { Request, Response } from 'express';
import {
  createUpdateBooking_public,
} from '../../services/booking';

export const createBookingHandler_public = async (req: Request, res: Response) => {
  try {
    const data = req.body;
    const result = await createUpdateBooking_public(data);
    if (!result.success) {
      return res.status(404).json(result);
    }
    return res.json(result);
  } catch (error: any) {
    return res.status(500).json({
      success: false,
      message: 'Failed to create booking',
      errorType: 'InternalServerError',
      data: undefined,
    });
  }
};
