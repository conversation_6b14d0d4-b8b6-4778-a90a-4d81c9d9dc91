import { ApiResponse, ApiErrorResponse } from './types';

/**
 * Checks if the response object has a valid format
 * @param response The response to validate
 * @returns boolean indicating if the response is valid
 */
export function isValidResponse(response: unknown): response is ApiResponse<unknown> {
  if (!response || typeof response !== 'object') {
    return false;
  }
  
  // Check if response has the required success property
  return 'success' in response;
}

/**
 * Normalizes API responses to ensure consistent format
 * @param response The response to normalize
 * @returns A normalized API response
 */
export function normalizeResponse<T>(response: unknown): ApiResponse<T> {
  // If it's already a valid response, return it
  if (isValidResponse(response)) {
    return response as ApiResponse<T>;
  }
  
  // If it's null or undefined, create an error response
  if (response === null || response === undefined) {
    return {
      success: false,
      error: {
        success: false,
        msg: 'Empty response received',
      }
    };
  }
  
  // If it's an object but not a valid response, try to normalize it
  if (typeof response === 'object') {
    try {
      // Check if it has data property, wrap it
      if ('data' in response) {
        return {
          success: true,
          data: (response as any).data as T
        };
      }
      
      // If no data property, treat the whole object as data
      return {
        success: true,
        data: response as T
      };
    } catch (error) {
      return {
        success: false,
        error: {
          success: false,
          msg: 'Failed to normalize response',
        }
      };
    }
  }
  
  // If it's a primitive value, wrap it as data
  return {
    success: true,
    data: response as unknown as T
  };
} 