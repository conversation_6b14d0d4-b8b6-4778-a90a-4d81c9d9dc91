import { ApiResponse, ApiErrorResponse } from './types';

/**
 * Checks if the response object has a valid format
 * @param response The response to validate
 * @returns boolean indicating if the response is valid
 */
export function isValidResponse(response: unknown): response is ApiResponse<unknown> {
  if (!response || typeof response !== 'object') {
    return false;
  }
  
  // Check if response has the required success property
  return 'success' in response;
}

/**
 * Normalizes API responses to ensure consistent format
 * @param response The response to normalize
 * @returns A normalized API response
 */
export function normalizeResponse<T>(response: unknown): ApiResponse<T> & Record<string, any> {
  // If it's already a valid response, return it with any additional properties
  if (isValidResponse(response)) {
    return response as ApiResponse<T> & Record<string, any>;
  }

  // If it's null or undefined, create an error response
  if (response === null || response === undefined) {
    return {
      success: false,
      error: {
        success: false,
        msg: 'Empty response received',
      }
    };
  }

  // If it's an object but not a valid response, try to normalize it
  if (typeof response === 'object') {
    const responseObj = response as any;

    try {
      // Handle Axios error responses
      if (responseObj.isAxiosError && responseObj.response) {
        const axiosResponse = responseObj.response;
        const result: any = {
          success: false,
          error: axiosResponse.data?.error || axiosResponse.data?.msg || 'Request failed'
        };

        // Preserve additional properties from axios response
        if (axiosResponse.data?.message) result.message = axiosResponse.data.message;
        if (axiosResponse.status) result.status = axiosResponse.status;
        if (axiosResponse.data?.success !== undefined) result.success = axiosResponse.data.success;

        return result;
      }

      // Handle network errors
      if (responseObj.isAxiosError && responseObj.message) {
        return {
          success: false,
          error: responseObj.message,
          message: 'Unable to connect to the server'
        };
      }

      // Check if it has data property with nested response
      if ('data' in responseObj && typeof responseObj.data === 'object' && responseObj.data !== null) {
        const nestedData = responseObj.data;

        // If nested data has success property, extract it
        if ('success' in nestedData) {
          const result: any = {
            success: nestedData.success,
            data: nestedData.data
          };

          // Preserve additional properties
          if (nestedData.message) result.message = nestedData.message;
          if (nestedData.error) result.error = nestedData.error;
          if (nestedData.status) result.status = nestedData.status;

          return result;
        }

        // Otherwise wrap the nested data
        return {
          success: true,
          data: nestedData as T
        };
      }

      // Check if it has data property, wrap it
      if ('data' in responseObj) {
        return {
          success: true,
          data: responseObj.data as T
        };
      }

      // Check if it looks like an unexpected response format
      if (typeof responseObj.data === 'string') {
        return {
          success: false,
          error: 'Invalid response format',
          message: 'Received an unexpected response format from the server'
        };
      }

      // If no data property, treat the whole object as data
      return {
        success: true,
        data: responseObj as T
      };
    } catch (error) {
      return {
        success: false,
        error: {
          success: false,
          msg: 'Failed to normalize response',
        }
      };
    }
  }

  // If it's a primitive value, wrap it as data
  return {
    success: true,
    data: response as unknown as T
  };
}