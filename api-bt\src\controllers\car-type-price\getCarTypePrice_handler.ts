import { Request, Response } from 'express';
import {
    getCarTypePrice,
} from '../../services/car-type-price'; // Adjust the path as necessary

export const getCarTypePriceHandler = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        const result = await getCarTypePrice(id);
        if (!result.success) {
            return res.status(404).json(result);
        }
        return res.json(result);
    } catch (error: any) {
        return res.status(500).json({ error: error.message });
    }
};
