"use client";
import React from "react";
import Image from "next/image";
import {twMerge} from "tailwind-merge";
import {TracingBeam} from "@/components/app/TracingBeam";
import { useI18n } from "@/hooks/use-translation";

export default function TracingBeamDemo() {
    const { t } = useI18n();

    const content = [
        {
            title: t("tracing_beam.experience.title"),
            description: (
                <div className="text-dark p-6 rounded-lg shadow-lg">
                    <ul className="list-disc pl-5 space-y-3">
                        <li className="text-lg font-medium">
                            {t("tracing_beam.experience.point1")}
                        </li>
                        <li className="text-lg font-medium">
                            {t("tracing_beam.experience.point2")}
                        </li>
                        <li className="text-lg font-medium">
                            {t("tracing_beam.experience.point3")}
                        </li>
                        <li className="text-lg font-medium">
                            {t("tracing_beam.experience.point4")}
                        </li>
                    </ul>
                </div>
            ),
            image: '/confort.webp',
        },
        {
            title: t("tracing_beam.simple_effective.title"),
            description: (
                <div className="text-dark p-6 rounded-lg shadow-lg">
                    <ul className="list-disc pl-5 space-y-3">
                        <li className="text-lg font-medium">
                            {t("tracing_beam.simple_effective.point1")}
                        </li>
                        <li className="text-lg font-medium">
                            {t("tracing_beam.simple_effective.point2")}
                        </li>
                        <li className="text-lg font-medium">
                            {t("tracing_beam.simple_effective.point3")}
                        </li>
                    </ul>
                </div>
            ),
            image: '/transport.webp',
        },
        {
            title: t("tracing_beam.safe_secure.title"),
            description: (
                <div className="text-dark p-6 rounded-lg shadow-lg">
                    <ul className="list-disc pl-5 space-y-3">
                        <li className="text-lg font-medium">
                            {t("tracing_beam.safe_secure.point1")}
                        </li>
                        <li className="text-lg font-medium">
                            {t("tracing_beam.safe_secure.point2")}
                        </li>
                    </ul>
                </div>
            ),
            image: '/gps-tracked.webp',
        },
    ];

    return (
        <TracingBeam className="px-6">
            <div className="max-w-2xl mx-auto antialiased pt-4 relative">
                {content.map((item, index) => (
                    <div key={`content-${index}`} className="mb-10">
                        <p className={twMerge("text-xl mb-4 ")}>
                            {item.title}
                        </p>

                        <div className="text-sm prose prose-sm dark:prose-invert">
                            {item?.image && (
                                <Image
                                    src={item.image}
                                    alt={t("tracing_beam.image_alt")}
                                    height="1000"
                                    width="1000"
                                    className="rounded-lg mb-10 object-cover"
                                />
                            )}
                            {item.description}
                        </div>
                    </div>
                ))}
            </div>
        </TracingBeam>
    );
}
