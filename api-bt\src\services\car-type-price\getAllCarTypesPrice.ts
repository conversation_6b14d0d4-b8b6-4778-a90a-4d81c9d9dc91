import { getRepository } from 'typeorm';
import { CarTypePrice } from '../../models/CarTypePrice';
import { paginate, PaginatedResult } from '../../utils/pagination';

export const getAllCarTypePrices = async (
    page = 1,
    limit = 10,
): Promise<PaginatedResult<CarTypePrice>> => {
    const carTypePriceRepository = getRepository(CarTypePrice);
    const options: any = {
        skip: (page - 1) * limit,
        take: limit,
        order: {
            created_at: 'DESC',  // Order by created_at in descending order
        },
    };

    const [data, totalRecords] = await carTypePriceRepository.findAndCount(options);

    return paginate<CarTypePrice>(data, page, limit, totalRecords);
};
