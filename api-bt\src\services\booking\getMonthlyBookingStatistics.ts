import { Booking } from '../../models/Booking';
import { getBookingsForMonth } from "./getBookingsForCurrentMonth";

export const getMonthlyBookingStatistics = async () => {
    try {
        const bookingsCurrentMonth = await getBookingsForMonth();
        const bookingsPreviousMonth = await getBookingsForMonth(-1);

        // Helper function to calculate metrics
        const calculateMetrics = (bookings: Booking[]) => {
            let totalIncome = 0;
            let totalBookings = bookings.length;
            let totalReturnTrips = 0;
            let totalCancelled = 0;
            let partialStripePayments = 0;
            let fullStripePayments = 0;
            let totalStripePaidAmount = 0;

            bookings.forEach(booking => {
                const payment = booking.payment;
                if (booking.status === 'cancelled') {
                    totalCancelled++;
                }
                if (payment) {
                    if (payment.stripe_payment_id && payment.stripe_charge_status) {
                        if (payment.amount_paid < payment.amount) {
                            partialStripePayments++;
                        }
                        if (payment.amount_paid === payment.amount && payment.paid_status) {
                            fullStripePayments++;
                        }
                        totalStripePaidAmount += payment.amount_paid;
                    }
                }
                if (booking.payment && (booking.payment.paid_status || booking.payment.stripe_charge_status)) {
                    totalIncome += booking.final_amount_to_pay;
                }
                if (booking.return_trip_distance > 0) {
                    totalReturnTrips++;
                }
            });

            return { totalIncome, totalBookings, totalReturnTrips, totalCancelled, partialStripePayments, fullStripePayments, totalStripePaidAmount };
        };

        const currentMetrics = calculateMetrics(bookingsCurrentMonth);
        const previousMetrics = calculateMetrics(bookingsPreviousMonth);

        const diff = (current: number, previous: number, fix: boolean = false, fixed: number = 2) => {
            if(fix) {
                return {
                    current: current.toFixed(fixed),
                    prev: previous.toFixed(fixed),
                    diff: (current - previous).toFixed(fixed),
                    percentage: ((current - previous) / (previous === 0 ? 1 : previous) * 100).toFixed(2) + '%',
                    increased_from_prev_month: current >= previous
                };
            }
            return {
                current: current,
                prev: previous,
                diff: current - previous,
                percentage: ((current - previous) / (previous === 0 ? 1 : previous) * 100).toFixed(2) + '%',
                increased_from_prev_month: current >= previous
            };
        };

        return {
            data: {
                total_income: diff(currentMetrics.totalIncome, previousMetrics.totalIncome, true),
                total_bookings: diff(currentMetrics.totalBookings, previousMetrics.totalBookings),
                total_return_trips: diff(currentMetrics.totalReturnTrips, previousMetrics.totalReturnTrips),
                total_cancelled: diff(currentMetrics.totalCancelled, previousMetrics.totalCancelled),
                partial_stripe_payments: diff(currentMetrics.partialStripePayments, previousMetrics.partialStripePayments),
                full_stripe_payments: diff(currentMetrics.fullStripePayments, previousMetrics.fullStripePayments),
                total_stripe_paid_amount: diff(currentMetrics.totalStripePaidAmount, previousMetrics.totalStripePaidAmount, true),
            },
            error: null,
            success: true,
        };
    } catch (error: any) {
        return {
            data: {},
            error: error.message || 'Failed to fetch data',
            success: false,
        };
    }
};
