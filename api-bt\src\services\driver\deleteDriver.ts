import { getRepository } from 'typeorm';
import { Driver } from '../../models/Driver';
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const deleteDriver = async (id: string) => {
  const driverRepository = getRepository(Driver);

  try {
    // Delete the driver
    const deleteResult = await driverRepository.delete(id);
    if (deleteResult.affected === 0) {
      return { success: false, data: null, error: 'Driver not found' };
    }
    return { success: true, data: { id }, error: null };
  } catch (error) {
    const interpretedError = interpretDatabaseError(error);
    return { success: false, data: null, error: interpretedError };
  }
};