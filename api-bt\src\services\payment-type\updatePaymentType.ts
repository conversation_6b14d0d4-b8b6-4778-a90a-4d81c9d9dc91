import { getRepository } from 'typeorm';
import { PaymentType } from '../../models/PaymentType'; // Adjust the path as necessary

export const updatePaymentType = async (id: string, data: Partial<PaymentType>) => {
  const paymentTypeRepository = getRepository(PaymentType);
  try {
    const paymentType = await paymentTypeRepository.findOne(id);
    if (!paymentType) {
      return { success: false, data: null, error: 'PaymentType not found' };
    }

    paymentTypeRepository.merge(paymentType, data);
    const updatedPaymentType = await paymentTypeRepository.save(paymentType);
    return { success: true, data: updatedPaymentType, error: null };
  } catch (error: any) {
    return { success: false, data: null, error: error.message };
  }
};
