import { getRepository } from 'typeorm';
import { Customer } from '../../models/Customer'; // Update the path according to your project structure
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';
// Adjust import path as needed
export const updateCustomer = async (id: string, data: Partial<Customer>) => {
  const customerRepository = getRepository(Customer);
  try {
    const result = await customerRepository.update(id, data);
    if (result.affected === 0) {
      return { success: false, error: 'Customer not found' };
    }
    const updatedCustomer = await customerRepository.findOne(id);
    return { success: true, data: updatedCustomer };
  } catch (error) {
    return { success: false, error: interpretDatabaseError(error) };
  }
};
