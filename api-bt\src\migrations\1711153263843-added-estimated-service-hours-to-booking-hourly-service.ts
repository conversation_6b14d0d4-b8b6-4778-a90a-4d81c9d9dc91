import {MigrationInterface, QueryRunner, TableColumn} from "typeorm";

export class addedEstimatedServiceHoursToBookingHourlyService1711153263843 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        const bookingTable = await queryRunner.getTable('booking');
        if (bookingTable) {
            if (!bookingTable.columns.find(column => column.name === 'estimated_service_hours')) {
                await queryRunner.addColumn('booking', new TableColumn({
                    name: 'estimated_service_hours',
                    type: 'integer',
                }));
            }
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropColumn('booking', 'estimated_service_hours');
    }

}
