'use client';

import React, { useState, ReactNode } from 'react';

// Define the type for each accordion item
interface AccordionItemProps {
    title: string;
    icon: ReactNode;
    content: string;
    isOpen: boolean; // Controlled by parent now
    onClick: () => void; // For handling click from parent
}

const AccordionItem: React.FC<AccordionItemProps> = ({ title, icon, content, isOpen, onClick }) => {
    return (
        <div className="border-b last:border-b-0"> {/* Remove border at the last item */}
            <h2
                className="text-lg font-semibold py-4 px-6 flex justify-between items-center cursor-pointer"
                onClick={onClick}
            >
                {title}
                <span className="text-xl">{icon}</span>
            </h2>
            {isOpen && (
                <div
                    className="content text-gray-700 p-4"
                    dangerouslySetInnerHTML={{ __html: content }}
                />
            )}
        </div>
    );
};

interface AccordionProps {
    data: Array<{
        title: string;
        icon: ReactNode;
        content: string;
    }>;
}

export const Accordion: React.FC<AccordionProps> = ({ data }) => {
    const [openIndex, setOpenIndex] = useState<number | null>(null);

    const handleClick = (index: number) => {
        // Toggle open index
        setOpenIndex(openIndex === index ? null : index);
    };

    return (
        <div className="w-full max-w-4xl mx-auto mt-10 shadow-lg rounded-lg overflow-hidden">
            {data.map((item, index) => (
                <AccordionItem
                    key={index}
                    {...item}
                    isOpen={openIndex === index}
                    onClick={() => handleClick(index)}
                />
            ))}
        </div>
    );
};
