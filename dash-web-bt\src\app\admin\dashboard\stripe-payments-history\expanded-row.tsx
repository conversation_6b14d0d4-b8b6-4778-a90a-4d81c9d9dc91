import {Title, Text} from 'rizzui';
// import {useRouter} from 'next/navigation';
// import {routes} from "@/config/routes";

interface Card {
    brand: string;
    last4: string;
    exp_month: number;
    exp_year: number;
}

interface PaymentMethodDetails {
    card: Card;
    type: string;
}

interface Props {
    id: string;
    payment_method_details: PaymentMethodDetails;
    receipt_url: string;
    paid: boolean;
    payment_intent: boolean;
    billing_details: any;
    metadata: any;
}

export default function ExpandedStripePaymentsRow({
                                                      id,
                                                      payment_method_details,
                                                      receipt_url,
                                                      paid,
                                                      payment_intent,
                                                      billing_details,
                                                      metadata,
}: Props) {
    // const router = useRouter();

    if ((!id || !payment_method_details || !payment_method_details.card)) {
        return <Text>No other information available.</Text>;
    }
    const navigateToStripeDashboard = (_id: string) => {
        // build stripe link manually
    }

    return (
        <div className="space-y-4">
            {
                payment_method_details && payment_method_details.card ? (
                    <div className="space-y-2">
                        <Title as="h3" className="text-lg font-semibold">Payment Method Details </Title>
                        <div className="grid grid-cols-1 divide-y bg-gray-50 p-3 rounded-lg">
                            <article key={id} className="py-2.5">
                                <div className="flex items-start">
                                    <header>
                                        <Title as="h4" className="mb-0.5 text-sm font-medium">
                                            Paid: {
                                                paid ? (
                                                        <span className="text-green-700">
                                                            Yes
                                                        </span>
                                                    ) : (
                                                        <span className="text-red-700">
                                                            No
                                                        </span>
                                                    )
                                                }
                                        </Title>
                                        <Title as="h4" className="mb-0.5 text-sm font-medium">
                                            Payment Type: {payment_method_details.type}
                                        </Title>
                                        <Title as="h4" className="mb-0.5 text-sm font-medium">
                                            Card Type: {payment_method_details.card.brand}
                                        </Title>
                                        <Text className="mb-1 text-gray-500">
                                            <span>Last 4</span>: {payment_method_details.card.last4}
                                        </Text>
                                        <Text className="mb-1 text-gray-500">
                                            Expiry: {payment_method_details.card.exp_month}/{payment_method_details.card.exp_year}
                                        </Text>
                                        <Text className="mb-1 text-emerald-900">
                                            Stripe Customer Id: <span className="text-green-700 font-medium">{ `${id}` }</span>
                                        </Text>
                                        <Text className="mb-1 text-deepOrange">
                                            Receipt URL <span className="text-deepOrange font-medium">
                                                <a href={receipt_url} target="_blank">here</a>.
                                            </span>
                                        </Text>
                                        <Text className="mb-1 text-blue-950">
                                            Payment Intent <span className="text-blue-950 font-bold">
                                                <a href={`https://dashboard.stripe.com/test/payments/${payment_intent}`} target="_blank">here</a>.
                                            </span>
                                        </Text>
                                    </header>
                                </div>
                            </article>
                        </div>
                    </div>
                ) : null
            }
        </div>
    );
}
