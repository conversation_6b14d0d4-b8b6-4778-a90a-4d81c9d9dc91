import { getRepository } from 'typeorm';
import { CarType } from '../../models/CarType'; // Adjust the path as necessary
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const deleteCarType = async (id: string) => {
  const carTypeRepository = getRepository(CarType);
  try {
    const deleteResult = await carTypeRepository.delete(id);
    if (deleteResult.affected === 0) {
      return { success: false, data: null, error: 'CarType not found' };
    }
    return { success: true, data: { id }, error: null };
  } catch (error) {
    const interpretedError = interpretDatabaseError(error);
    return { success: false, data: null, error: interpretedError };
  }
};
