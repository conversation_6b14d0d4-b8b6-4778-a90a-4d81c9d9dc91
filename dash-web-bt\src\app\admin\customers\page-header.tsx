'use client';

import { PiPlusBold } from 'react-icons/pi';
import { routes } from '@/config/routes';
import { Button } from 'rizzui';
import PageHeader from '@/app/shared/lib/page-header';
import ExportButton from '@/app/shared/lib/export-button';
import { appointmentData } from '@/data/appointment-data';
import { useRouter } from 'next/navigation';
import {useEffect} from "react";

const pageHeader = {
  title: 'Customers List',
  breadcrumb: [
    {
      href: routes.dashboard,
      name: 'Home',
    },
    {
      name: 'Customers List',
    },
  ],
};

interface HeaderProps {
  className?: string;
}

export default function BookingsListPageHeader({ className }: HeaderProps) {
  const router = useRouter();

  let handleCreateBooking = () => {
    router.push(routes.customerCreate); // Replace '/path-to-navigate' with your target path
  };

  return (
    <PageHeader title={pageHeader.title} breadcrumb={pageHeader.breadcrumb}>
      <div className="mt-4 flex flex-col items-center gap-3 @sm:flex-row @lg:mt-0">
        {/*<ExportButton*/}
        {/*  data={appointmentData}*/}
        {/*  fileName="appointment_data"*/}
        {/*  header="ID,Patient,Doctor,Service Type,Date,Status,Payment,Duration"*/}
        {/*/>*/}
        <Button className="w-full @lg:w-auto" onClick={handleCreateBooking}>
          <PiPlusBold className="me-1.5 h-[17px] w-[17px]" />
          Create Customer
        </Button>
      </div>
    </PageHeader>
  );
}
