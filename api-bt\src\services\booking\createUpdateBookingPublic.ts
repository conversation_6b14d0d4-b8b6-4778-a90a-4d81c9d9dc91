import {getConnection, getRepository} from 'typeorm';
import {Booking} from '../../models/Booking';
import {Customer} from '../../models/Customer';
import {Payment} from '../../models/Payment';
import {BookingBag} from '../../models/BookingBag';
import {interpretDatabaseError} from '../../utils/interpretDatabaseError';
import {computeDrivingDistanceByCoords} from '../../utils/computeDrivingDistanceByCoords';
import z from 'zod';
import {createBookingSchema} from '../../utils/validators/createBookingSchema_public';
import {getCarType} from "../car-type";
import {getPaymentType} from "../payment-type";
import {sendMail} from "../vendor/mail/mail";
import stripeTransactionService from '../../services/vendor/stripe/StripeTransactionService'; // Adjust the import path as necessary
import {createBookCode} from '../../utils/createBookingCode';
import moment from "moment";
import calculatePrice from "../../utils/computeTripPrice";
import {getAllSettings} from "../setting";

const stripe = stripeTransactionService.stripe;

const STRIPE_PAYMENT_CURRENCY = process.env.STRIPE_PAYMENT_CURRENCY || 'EUR';
let PARTIAL_DEDUCTION_PERCENTAGE= 25; // 50% deduction for partial payment
let BOOKINGS_ENABLED= false;
export const createUpdateBooking_public = async (bookingInputData: any) => {

    // # Get Global Settings

    const globalSettings = await getAllSettings();
    if(globalSettings && globalSettings.data && globalSettings.data.length > 0) {
        PARTIAL_DEDUCTION_PERCENTAGE = Number(globalSettings.data.find((gs: any) => gs.name === 'partial_payment_deduction')?.value);
        BOOKINGS_ENABLED = globalSettings.data.find((gs: any) => gs.name === 'public_bookings_enabled')?.value === 'yes';
    }

    // # If bookings not enabled from admin, do not allow bookings to happen !
    if(!BOOKINGS_ENABLED) {
        return {success: false, data: null, error: 'Cannot honor bookings now as they are disabled!'};
    }

    // Validate input data first

    try {
        createBookingSchema.parse(bookingInputData);
    } catch (error) {
        if (error instanceof z.ZodError) {
            // Return validation errors to the user in a suitable format
            return {success: false, data: null, error: error.issues};
        } else {
            console.error('Unexpected validation error:', error);
            return {success: false, data: null, error: 'Internal Server Error'};
        }
    }

    if (bookingInputData.scheduled_at && isBefore(bookingInputData.scheduled_at)) {
        return {success: false, data: null, error: 'Cannot schedule with a date in the past.'};
    }

    if (bookingInputData.return_trip_time && isBefore(bookingInputData.return_trip_time, bookingInputData.scheduled_at)) {
        return {
            success: false,
            data: null,
            error: 'Cannot schedule a return trip with a date set before the initial schedule date.'
        };
    }

    const connection = getConnection();
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();

    try {
        if (bookingInputData.trip_type === 'hourly' && !bookingInputData.estimated_service_hours) {
            return {
                success: false,
                data: null,
                error: 'When on hourly, you are required to provide an estimated amount of hours of service.',
            };
        }

        const carType = await getCarType(bookingInputData.car_type);

        if(!carType || !carType.data) {
            return {
                success: false,
                data: null,
                error: 'Cannot schedule this trip because the car type could not be found or it is invalid.'
            };
        }

        let computedFinalAmount: number = 0;
        let computedRecommendedAmount: number = 0;

        if (bookingInputData.trip_type === 'transfer') {
            // # compute distance by coordinates directly from source. never trust client.
            const computedDistanceFromCoordinates = await computeDrivingDistanceByCoords(
                {
                    lat: bookingInputData.pickup_lat,
                    lng: bookingInputData.pickup_long,
                },
                {
                    lat: bookingInputData.destination_lat,
                    lng: bookingInputData.destination_long,
                },
            );

            if(carType.data.transfer_prices && carType.data.transfer_prices.length > 0) {
                // compute final amount to pay and final amount to pay based on trip distance and return trip distance
                let final_amount_to_pay = calculatePrice({
                    distance: +computedDistanceFromCoordinates,
                    ranges: carType.data.use_default_ppk ? [] : (carType.data.transfer_prices || []),
                    defaultPricePerKm: carType.data.default_price_per_km,
                });

                if (!bookingInputData.one_way_trip) {
                    computedFinalAmount = +(final_amount_to_pay * 2).toFixed(2);
                    computedRecommendedAmount = +final_amount_to_pay.toFixed(2);
                } else {
                    computedFinalAmount = +final_amount_to_pay.toFixed(2);
                    computedRecommendedAmount = +final_amount_to_pay.toFixed(2);
                }
            } else if((carType.data.transfer_prices && carType.data.transfer_prices.length < 1) && carType.data.default_price_per_km) {
                let final_amount_to_pay = carType.data.default_price_per_km * computedDistanceFromCoordinates;

                if (!bookingInputData.one_way_trip) {
                    computedFinalAmount = +(final_amount_to_pay * 2).toFixed(2);
                    computedRecommendedAmount = +final_amount_to_pay.toFixed(2);
                } else {
                    computedFinalAmount = +final_amount_to_pay.toFixed(2);
                    computedRecommendedAmount = +final_amount_to_pay.toFixed(2);
                }
            }
        }

        if (bookingInputData.trip_type === 'hourly' && carType.data.hourly_rate && bookingInputData.estimated_service_hours) {
            // compute final amount to pay and final amount to pay based on estimated_service_hours times hourly rate
            const final_amount_to_pay = +bookingInputData.estimated_service_hours * carType.data.hourly_rate;

            computedFinalAmount = +final_amount_to_pay.toFixed(2);
            computedRecommendedAmount = +final_amount_to_pay.toFixed(2);
        }

        await queryRunner.startTransaction();

        const bookingRepository = getRepository(Booking);
        const customerRepository = getRepository(Customer);
        const paymentRepository = getRepository(Payment);
        const bookingBagRepository = getRepository(BookingBag);

        // Handling customer creation or fetch
        let customer;

        customer = await customerRepository.findOne({where: {phone_number: bookingInputData.customer_phone_number}});

        // console.log(JSON.stringify(data, null, 2))

        if (customer) {
            customer.phone_number = bookingInputData.customer_phone_number;
            customer.last_name = bookingInputData.customer_last_name;
            customer.first_name = bookingInputData.customer_first_name;
            customer.email = bookingInputData.customer_email;
        }

        if (!customer) {
            customer = customerRepository.create({
                first_name: bookingInputData.customer_first_name,
                last_name: bookingInputData.customer_last_name,
                phone_number: bookingInputData.customer_phone_number,
                email: bookingInputData.customer_email,
            });
        }

        await customerRepository.save(customer);

        // # Handling payment creation

        // # Compute amount to charge
        let paymentAmount: number;
        if (bookingInputData.partial_payment !== 'full') {
            paymentAmount = computedFinalAmount * (PARTIAL_DEDUCTION_PERCENTAGE / 100);
        } else {
            paymentAmount = computedFinalAmount;
        }

        // # Check if it is a Stripe payment ...
        let stripe_payment_id: string | null | undefined;
        let payment_intent;
        let stripe_customer;

        const paymentType = await getPaymentType(bookingInputData.payment_type);

        if (paymentType && paymentType?.data) {
            // # generate the booking code.
            const bookingCode = createBookCode(bookingInputData.trip_type === 'hourly' ? 'H' : 'T');

            // Handling booking creation
            const bookingData = {
                ...bookingInputData,
                amount: computedFinalAmount,
                recommended_amount_to_pay: computedRecommendedAmount,
                customer: customer,
                status: carType.data
                    .automatic_acceptance ? 'accepted' : 'pending',
                booking_code: bookingCode,
            };

            const newBooking = bookingRepository.create(bookingData);

            let savedBooking = await bookingRepository.save(bookingData);

            if (paymentType.data.name === 'Card' || paymentType.data.name === 'Cash') {
                // Create a payment intent
                try {
                    const stripe_queryParams: {
                        limit: number,
                        email: string,
                    } = {
                        limit: 1,
                        email: customer.email,
                    }

                    const customers = await stripe.customers.list(stripe_queryParams);

                    if (customers.data.length > 0) {
                        // If a customer with the same email exists, use that customer
                        stripe_customer = customers.data[0];
                    } else {
                        // Otherwise, create a new customer
                        stripe_customer = await stripe.customers.create({
                            email: customer.email,
                            name: `${customer.first_name} ${customer.last_name}`,
                            phone: customer.phone_number,
                        });
                    }

                    if (stripe_customer) {
                        // # Only get tip if paying via Stripe in full
                        const withTip = bookingInputData.partial_payment === 'full' ? paymentAmount + (bookingInputData.tip || 0) : paymentAmount;

                        // console.log('withTip', withTip);
                        // console.log('bookingInputData.partial_payment', bookingInputData.partial_payment);
                        // console.log('paymentAmount', paymentAmount);
                        // console.log('bookingInputData.tip', bookingInputData.tip);
                        // throw new Error('Out.')

                        const paymentIntent = await stripe.paymentIntents.create({
                            amount: Math.ceil(withTip * 100), // amount in the smallest currency unit, e.g., cents for EUR, USD
                            currency: STRIPE_PAYMENT_CURRENCY,
                            customer: stripe_customer.id,
                            automatic_payment_methods: {
                                enabled: true,
                            }, // Specify the payment method types you want to accept
                            metadata: {
                                'booking_id': savedBooking.id,
                            },
                        });

                        stripe_payment_id = paymentIntent.client_secret; // Only assign IF successful

                        customer.stripe_customer_id = stripe_customer.id;

                        await customerRepository.save(customer);

                        if (paymentIntent) {
                            payment_intent = paymentIntent.client_secret?.split('_secret_')[0]; // Only assign IF successful
                        } else {
                            console.error('Could not create a Stripe paymentIntent for this payment, thus could not extract ' +
                                'client secret from the intent. Will exit and rollback transaction.');
                        }

                    } else {
                        console.error('Could not create a Stripe customer for the payment. Will exit and rollback transaction.');
                    }

                } catch (error: any) {
                    console.error("Error creating Stripe payment intent: ", error);
                    await queryRunner.rollbackTransaction();

                    return {
                        success: false,
                        data: null,
                        error: `Could not create a stripe payment intent ! ${error.message}`
                    };
                }
            }

            let paymentObjData = {
                amount: computedFinalAmount,
                amount_paid: 0,
                type: {id: bookingInputData.payment_type}, // Assuming type is related by ID
                tip: bookingInputData.tip,
            } as Payment

            if ((paymentType.data.name === 'Card' || paymentType.data.name === 'Cash') && payment_intent) {
                paymentObjData.stripe_payment_id = stripe_payment_id;
                paymentObjData.payment_intent = payment_intent;
            }

            // # Create payment entry
            const payment = paymentRepository.create(paymentObjData);
            await paymentRepository.save(payment);

            payment.booking = Object.create(savedBooking);

            await paymentRepository.save(payment);

            // Handling BookingBag entities
            for (const bag of bookingInputData.bags) {
                const bookingBag = new BookingBag();
                bookingBag.booking = newBooking;
                bookingBag.bag = bag.bag;
                bookingBag.quantity = bag.quantity;
                await bookingBagRepository.save(bookingBag);
            }

            await queryRunner.commitTransaction();

            // # Send mail to customer
            await sendMail(newBooking, customer);

            savedBooking.payment = payment;

            savedBooking = await bookingRepository.save(savedBooking);

            return {success: true, data: savedBooking, error: null};
        } else {
            await queryRunner.rollbackTransaction();
            // console.log('error', 'No payment intent was created', bookingInputData, paymentType);
            return {
                success: false,
                data: null,
                error: 'Payment intent could not be created for the Stripe payment. Please try again or contact admin.'
            };
        }
    } catch (error) {
        console.log('error', error)
        await queryRunner.rollbackTransaction();
        const interpretedError = interpretDatabaseError(error);
        return {success: false, data: null, error: interpretedError};
    } finally {
        await queryRunner.release();
    }
};

function isBefore(targetDateString: string, againstDateString?: string) {
    const inputDate = moment(targetDateString);
    const againstDate = moment(againstDateString);
    return inputDate.isBefore(againstDate);
}
