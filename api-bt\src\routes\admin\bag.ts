import express from 'express';
import {
  createBag_handler,
  deleteBag_handler, getAllBags_handler, getBag_handler, updateBag_handler,
} from '../../controllers/bag';

export const bagRouter = express.Router();

bagRouter.post('/bags', createBag_handler);
bagRouter.get('/bags', getAllBags_handler);
bagRouter.get('/bags/:id', getBag_handler);
bagRouter.put('/bags/:id', updateBag_handler);
bagRouter.delete('/bags/:id', deleteBag_handler);
