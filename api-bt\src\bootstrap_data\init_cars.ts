export const cars: any[] = [
    {
        "id": "9523d7ec-9203-4d37-94d9-1329e16cc190",
        "name": "Toyota Corolla",
        "image": "https://upload.wikimedia.org/wikipedia/commons/thumb/c/c1/2016_Chrysler_300_Limited_AWD_front_4.22.19.jpg/1920px-2016_Chrysler_300_Limited_AWD_front_4.22.19.jpg",
        "brand": "Toyota ",
        "model": "Corolla",
        "allow_pets": true,
        "licence_plate": "MF 11 K",
        "bags_capacity": 11,
        "created_at": "2024-09-21T13:22:58.153Z",
        "updated_at": "2024-09-21T13:22:58.153Z",
        "updated_by": null,
        "type": {
            "id": "13bf4dc1-dac4-4037-a100-d8a6014a5b14",
            "name": "[1]Van",
            "default_price_per_km": 22,
            "seats": 12,
            "hourly_rate": 16,
            "automatic_acceptance": true,
            "use_default_ppk": false,
            "created_at": "2024-09-21T13:21:44.411Z",
            "updated_at": "2024-09-21T15:03:07.074Z",
            "updated_by": null,
            "bags": [
                {
                    "id": "d0513233-50df-4d0d-ad48-618b67fbc929",
                    "quantity": 2,
                    "created_at": "2024-09-21T15:03:07.233Z",
                    "updated_at": "2024-09-21T15:03:07.233Z"
                },
                {
                    "id": "0837c44d-445c-4ab0-b7e8-93ca89f954b4",
                    "quantity": 4,
                    "created_at": "2024-09-21T15:03:07.433Z",
                    "updated_at": "2024-09-21T15:03:07.433Z"
                },
                {
                    "id": "bbfa86a3-b3e2-45f1-805b-cb71a786eb09",
                    "quantity": 4,
                    "created_at": "2024-09-21T15:03:07.333Z",
                    "updated_at": "2024-09-21T15:03:07.333Z"
                }
            ]
        },
        "drivers": [
            {
                "id": "1556550c-bfd7-48b9-9c5d-aa79f4268733",
                "first_name": "Iacob",
                "last_name": "Silviu Iulian",
                "created_at": "2024-09-21T13:19:53.766Z",
                "updated_at": "2024-09-21T13:19:53.766Z",
                "updated_by": null
            },
            {
                "id": "cc42b69f-3fd9-46df-a4f8-d3b3b6c70a2f",
                "first_name": "Gigel",
                "last_name": "Pazvante",
                "created_at": "2024-09-21T13:20:00.486Z",
                "updated_at": "2024-09-21T13:20:00.486Z",
                "updated_by": null
            }
        ]
    },
    {
        "id": "6ccf5110-6199-4827-9d34-2c9e183c1384",
        "name": "Chrysler 300",
        "image": "https://www.usnews.com/object/image/0000018c-5f0b-dc6c-aded-ffbf2d960000/https-cars-dms-usnews-com-static-uploads-images-auto-custom-15294-original-2024-toyota-corolla-angular-front-1.jpg?update-time=1693333947000&size=responsiveGallery",
        "brand": "Chrysler ",
        "model": "Chrysler 300",
        "allow_pets": true,
        "licence_plate": "MF 12 K",
        "bags_capacity": 12,
        "created_at": "2024-09-21T13:22:15.751Z",
        "updated_at": "2024-09-21T13:22:15.751Z",
        "updated_by": null,
        "type": {
            "id": "9f12e350-2a74-4587-96e3-5a9ba5ebe926",
            "name": "[3]  Limo",
            "default_price_per_km": 15,
            "seats": 4,
            "hourly_rate": 15,
            "automatic_acceptance": true,
            "use_default_ppk": false,
            "created_at": "2024-09-21T13:21:01.894Z",
            "updated_at": "2024-09-21T15:03:19.002Z",
            "updated_by": null,
            "bags": [
                {
                    "id": "e4cb7123-69a0-4f3e-aaa3-aa89751cf164",
                    "quantity": 3,
                    "created_at": "2024-09-21T15:03:19.153Z",
                    "updated_at": "2024-09-21T15:03:19.153Z"
                },
                {
                    "id": "e47b9b44-9e1a-4026-93d8-7dc5c734226a",
                    "quantity": 6,
                    "created_at": "2024-09-21T15:03:19.253Z",
                    "updated_at": "2024-09-21T15:03:19.253Z"
                }
            ]
        },
        "drivers": [
            {
                "id": "622e9c3e-b6da-4e47-8a5d-458b46a908bc",
                "first_name": "Doner",
                "last_name": "Kebab",
                "created_at": "2024-09-21T13:20:05.777Z",
                "updated_at": "2024-09-21T13:20:05.777Z",
                "updated_by": null
            }
        ]
    }
];