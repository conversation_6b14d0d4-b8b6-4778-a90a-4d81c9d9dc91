import {MigrationInterface, QueryRunner, TableC<PERSON>umn, TableForeignKey} from "typeorm";

export class addedDriversToCar1715701645087 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Removing the 'current_driver' column from the 'car' table
        const carTable = await queryRunner.getTable('car');
        if (carTable) {
            const currentDriverColumn = carTable.columns.find(column => column.name === 'current_driver');
            if (currentDriverColumn) {
                await queryRunner.dropColumn('car', 'current_driver');
            }
        }

        // Adding the 'car_id' column to the 'driver' table
        const driverTable = await queryRunner.getTable('driver');
        if (driverTable) {
            if (!driverTable.columns.find(column => column.name === 'car_id')) {
                await queryRunner.addColumn('driver', new TableColumn({
                    name: 'car_id',
                    type: 'uuid',
                    isNullable: true,
                }));

                await queryRunner.createForeignKey('driver', new TableForeignKey({
                    columnNames: ['car_id'],
                    referencedColumnNames: ['id'],
                    referencedTableName: 'car',
                    onDelete: 'SET NULL',
                }));
            }
        }
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
        // Removing the 'car_id' foreign key and column from the 'driver' table
        const driverTable = await queryRunner.getTable('driver');
        if (driverTable) {
            const foreignKey = driverTable.foreignKeys.find(fk => fk.columnNames.indexOf('car_id') !== -1);
            if (foreignKey) {
                await queryRunner.dropForeignKey('driver', foreignKey);
            }

            const carIdColumn = driverTable.columns.find(column => column.name === 'car_id');
            if (carIdColumn) {
                await queryRunner.dropColumn('driver', 'car_id');
            }
        }

        // Adding the 'current_driver' column back to the 'car' table
        const carTable = await queryRunner.getTable('car');
        if (carTable) {
            if (!carTable.columns.find(column => column.name === 'current_driver')) {
                await queryRunner.addColumn('car', new TableColumn({
                    name: 'current_driver',
                    type: 'uuid',
                    isNullable: true,
                }));
            }
        }
    }
}
