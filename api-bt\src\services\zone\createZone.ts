import { getRepository } from 'typeorm';
import { Zone } from '../../models/Zone'; // Adjust the path as necessary
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const createZone = async (data: Partial<Zone>) => {
  const zoneRepository = getRepository(Zone);
  try {
    const newZone = zoneRepository.create(data);
    await zoneRepository.save(newZone);
    return { success: true, data: newZone, error: null };
  } catch (error) {
    const interpretedError = interpretDatabaseError(error);
    return { success: false, data: null, error: interpretedError };
  }
};
