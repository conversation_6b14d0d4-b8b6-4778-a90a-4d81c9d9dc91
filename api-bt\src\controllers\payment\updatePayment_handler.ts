import { Request, Response } from 'express';
import {
    updatePayment,
} from '../../services/payment'; // Adjust the import path as necessary

export const updatePaymentHandler = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        const updateData = req.body;
        const result = await updatePayment(id, updateData);
        if (!result.success) {
            return res.status(404).json(result);
        }
        return res.json(result);
    } catch (error: any) {
        return res.status(500).json({
            success: false,
            message: 'Failed to update payment',
            error: error.message,
        });
    }
};
