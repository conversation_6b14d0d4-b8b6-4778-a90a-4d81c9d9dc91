import Stripe from 'stripe';

class StripeTransactionService {
    public stripe: Stripe;

    constructor(apiKey: string) {
        this.stripe = new Stripe(apiKey, {
            apiVersion: '2024-04-10',
        });
    }

    /**
     * Fetches paginated transactions from Stripe.
     * @param limit The number of transaction objects to be returned.
     * @param startingAfter A cursor for use in pagination. `startingAfter` is an object ID that defines your place in the list.
     * @param status The status of the transactions to filter by (e.g., 'succeeded', 'pending', 'failed').
     * @param customer The customer id (cus...) of the transactions to filter by.
     * @returns A promise containing a list of transactions.
     */
    async fetchTransactions(limit: number = 10, startingAfter?: string, status?: Stripe.Charge.Status, customer?: string,): Promise<Stripe.ApiList<Stripe.Charge>> {
        try {
            const params: Stripe.ChargeListParams = {
                limit: limit,
                starting_after: startingAfter,
                ...(status && customer && { status, customer }),
            };

            // Fetch charges as an example of transactions
            const charges = await this.stripe.charges.list(params);

            return charges;
        } catch (error) {
            console.error('Failed to fetch transactions:', error);
            throw error;
        }
    }

    /**
     * Processes a refund for a specific charge.
     * @param chargeId The ID of the charge to refund.
     * @param amount The amount in cents to refund (optional, full refund if not specified).
     * @param reason The reason for the refund (optional).
     * @returns A promise containing the refund object.
     */
    async refundCharge(chargeId: string, amount?: number, reason?: Stripe.RefundCreateParams.Reason): Promise<Stripe.Refund> {
        try {
            const refundOptions: Stripe.RefundCreateParams = {
                charge: chargeId,
                ...(amount && { amount }),  // include amount only if specified
                ...(reason && { reason }), // include reason only if specified
            };
            const refund = await this.stripe.refunds.create(refundOptions);
            return refund;
        } catch (error) {
            console.error(`Failed to refund charge ${chargeId}:`, error);
            throw error;
        }
    }
}

export default new StripeTransactionService(process.env.STRIPE_SECRET_KEY as string);

export type StripeStatusType = Stripe.Charge.Status
