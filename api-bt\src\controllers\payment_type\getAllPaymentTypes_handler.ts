import { Request, Response } from 'express';
import {
    getAllPaymentTypes,
} from '../../services/payment-type'; // Adjust the import path as necessary

export const getAllPaymentTypesHandler = async (_req: Request, res: Response) => {
    try {
        const result = await getAllPaymentTypes();
        return res.json(result);
    } catch (error: any) {
        return res.status(500).json({
            success: false,
            message: 'Failed to get payment types',
            error: error.message,
        });
    }
};
