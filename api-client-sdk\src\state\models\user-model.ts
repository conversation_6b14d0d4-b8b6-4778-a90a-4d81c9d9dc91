import { types, Instance, SnapshotIn } from 'mobx-state-tree';

/**
 * Role model in MST
 */
export const RoleModel = types.model('RoleModel', {
  id: types.identifierNumber,
  name: types.string,
  description: types.maybe(types.string),
});

/**
 * Driver model in MST (simplified for reference in User model)
 */
export const DriverModel = types.model('DriverModel', {
  id: types.identifierNumber,
  // Other driver properties can be added as needed
});

/**
 * User model in MST
 */
export const UserModel = types.model('UserModel', {
  id: types.identifierNumber,
  username: types.string,
  email: types.string,
  // password is not included as it's never returned from API
  avatar_url: types.maybe(types.string),
  country: types.maybe(types.string),
  bio: types.maybe(types.string),
  expo_push_token: types.maybe(types.string),
  created_at: types.string, // We'll convert to Date in views if needed
  first_name: types.string,
  last_name: types.string,
  role: types.maybe(RoleModel),
  driver: types.maybe(DriverModel),
})
.views(self => ({
  /**
   * Get full name
   */
  get fullName(): string {
    return `${self.first_name} ${self.last_name}`;
  },
  
  /**
   * Get created date as Date object
   */
  get createdDate(): Date {
    return new Date(self.created_at);
  },
  
  /**
   * Check if user is an admin
   */
  get isAdmin(): boolean {
    return self.role?.id === 1;
  },
  
  /**
   * Check if user is a driver
   */
  get isDriver(): boolean {
    return !!self.driver;
  },
}));

// Type definitions for TypeScript
export interface User extends Instance<typeof UserModel> {}
export interface UserSnapshotIn extends SnapshotIn<typeof UserModel> {}
export interface Role extends Instance<typeof RoleModel> {}
export interface Driver extends Instance<typeof DriverModel> {} 