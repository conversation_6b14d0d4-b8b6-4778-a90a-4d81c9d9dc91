import {
  <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToOne, JoinColumn,
} from 'typeorm';
import { PaymentType } from './PaymentType';
import { Booking } from "./Booking";
import { ColumnNumericTransformer } from "../utils/ColumnNumericTransformer";

@Entity()
export class Payment {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column('numeric', {
    precision: 7,
    scale: 2,
    transformer: new ColumnNumericTransformer(),
    default: 0, // Setting a default value to avoid null constraints
  })
  amount: number;

  @Column('text', { nullable: true })
  stripe_payment_id: string | null | undefined;

  @Column('text', { nullable: true })
  payment_intent: string;

  @Column('numeric', {
    precision: 7,
    scale: 2,
    transformer: new ColumnNumericTransformer(),
    nullable: true,
  })
  amount_paid: number;

  @Column('numeric', {
    precision: 7,
    scale: 2,
    transformer: new ColumnNumericTransformer(),
    nullable: true,
  })
  tip: number;

  @OneToOne(() => Booking)
  @JoinColumn({ name: "booking_id" })
  booking: Booking;

  @Column({ default: false })
  paid_status: boolean;

  @Column({ default: false })
  stripe_charge_status: boolean;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ nullable: true })
  updated_by: number;

  // Many-to-one relationship to PaymentType
  @ManyToOne(() => PaymentType, paymentType => paymentType.payments)
  @JoinColumn({ name: 'payment_type_id' }) // Correctly use JoinColumn for foreign key
  type: PaymentType;
}
