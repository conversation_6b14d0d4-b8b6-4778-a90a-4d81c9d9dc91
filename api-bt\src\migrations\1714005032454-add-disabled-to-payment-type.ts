import {MigrationInterface, QueryRunner, TableColumn} from "typeorm";

export class addDisabledToPaymentType1714005032454 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        const paymentTypeTable = await queryRunner.getTable('payment_type');
        if (paymentTypeTable) {
            if (!paymentTypeTable.columns.find(column => column.name === 'disabled')) {
                await queryRunner.addColumn('customer', new TableColumn({
                    name: 'disabled',
                    type: 'boolean',
                    default: false,
                }));
            }
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropColumn('payment_type', 'disabled');
    }

}
