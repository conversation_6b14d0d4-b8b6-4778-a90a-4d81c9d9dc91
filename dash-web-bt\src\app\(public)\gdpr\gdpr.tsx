'use client';

import React from 'react';
import { useI18n } from "@/hooks/use-translation";

const GDPRStatement = () => {
    const { t } = useI18n();

    return (
        <div className="min-h-screen bg-gray-100 dark:bg-gray-900 flex flex-col items-center py-12">
            <div className="w-full max-w-3xl dark:bg-gray-800 p-8 rounded-lg shadow-md dark:shadow-xl">
                <h1 className="text-3xl font-bold mb-8 text-gray-900 dark:text-gray-100">{t('gdpr.title')}</h1>
                <div className="mb-6 text-gray-800 dark:text-gray-200">
                    <p className="mb-4">{t('gdpr.intro')}</p>
                    <h2 className="text-xl font-semibold mb-2 text-gray-900 dark:text-gray-100">{t('gdpr.section1.title')}</h2>
                    <p className="mb-4">{t('gdpr.section1.intro')}</p>
                    <ul className="list-disc list-inside mb-4">
                        <li>{t('gdpr.section1.item1')}</li>
                        <li>{t('gdpr.section1.item2')}</li>
                    </ul>
                    <h2 className="text-xl font-semibold mb-2 text-gray-900 dark:text-gray-100">{t('gdpr.section2.title')}</h2>
                    <p className="mb-4">{t('gdpr.section2.intro')}</p>
                    <ul className="list-disc list-inside mb-4">
                        <li>{t('gdpr.section2.item1')}</li>
                        <li>{t('gdpr.section2.item2')}</li>
                    </ul>
                    <h2 className="text-xl font-semibold mb-2 text-gray-900 dark:text-gray-100">{t('gdpr.section3.title')}</h2>
                    <p className="mb-4">{t('gdpr.section3.content')}</p>
                    <h2 className="text-xl font-semibold mb-2 text-gray-900 dark:text-gray-100">{t('gdpr.section4.title')}</h2>
                    <p className="mb-4">
                        {t('gdpr.section4.content')}{' '}
                        <a href="mailto:<EMAIL>" className="text-blue-500 dark:text-blue-400 underline">
                            <EMAIL>
                        </a>.
                    </p>
                    <h2 className="text-xl font-semibold mb-2 text-gray-900 dark:text-gray-100">{t('gdpr.section5.title')}</h2>
                    <p className="mb-4">{t('gdpr.section5.content')}</p>
                    <h2 className="text-xl font-semibold mb-2 text-gray-900 dark:text-gray-100">{t('gdpr.section6.title')}</h2>
                    <p className="mb-4">{t('gdpr.section6.content')}</p>
                    <h2 className="text-xl font-semibold mb-2 text-gray-900 dark:text-gray-100">{t('gdpr.contact.title')}</h2>
                    <p className="mb-4">
                        {t('gdpr.contact.content')}{' '}
                        <a href="mailto:<EMAIL>" className="text-blue-500 dark:text-blue-400 underline">
                            <EMAIL>
                        </a>.
                    </p>
                </div>
            </div>
        </div>
    );
}

export default GDPRStatement;
