import React from "react";

type RouteInfoCardTypes = {
    distance: number,
    duration: number,
    address: string,
    returnDistance: number | null,
    returnDuration: number | null,
    destinationAddress?: string | null,
}
const RouteInfoCard = ({
                                          address,
                                          distance,
                                          duration,
                                          destinationAddress,
                                          returnDistance,
                                          returnDuration
                                      }: RouteInfoCardTypes) => {

    const getDurationString = () => {
        if (duration < 60) return `${duration} minutes`
        const hours = Math.floor(duration / 60)
        const minutes = duration % 60
        return `${hours} hours ${minutes} minutes`;
    }

    return (
        <div className="bg-opacity-100 mt-2 p-4 shadow-md border-t border-gray-200">
            <div className="flex flex-col md:flex-row justify-between items-center text-sm">
                <div className="text-creamyWhite w-full">
                    <h4 className="font-semibold text-creamyWhite">Outbound</h4>
                    {
                        distance ? (
                            <>
                                <p><strong>Pickup Address:</strong> {address}</p>
                                <p><strong>Destination Address:</strong> {destinationAddress || '-'}</p>
                                <p><strong>Distance:</strong> {distance} km</p>
                                <p><strong>Duration:</strong> {getDurationString()}</p>

                            </>
                        ) : <p className="text-red">Please select your pick up and destination address</p>
                    }
                </div>
                {
                    returnDistance && returnDuration ? (
                        <div className="text-creamyWhite w-full">
                            <h4 className="font-semibold text-creamyWhite">Return</h4>
                            <p><strong>Pickup Address:</strong> {destinationAddress}</p>
                            <p><strong>Destination Address:</strong> {address}</p>
                            <p><strong>Distance:</strong> {returnDistance} min</p>
                            <p><strong>Duration:</strong> {returnDuration} km</p>
                        </div>
                    ) : null
                }
            </div>
        </div>
    );
};

export default RouteInfoCard;