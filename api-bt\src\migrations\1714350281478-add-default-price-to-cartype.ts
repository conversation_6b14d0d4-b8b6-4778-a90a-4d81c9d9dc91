import {MigrationInterface, QueryRunner, TableColumn} from "typeorm";

export class addDefaultPriceToCartype1714350281478 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        const carTypeTable = await queryRunner.getTable('car_type');
        if (carTypeTable) {
            if (!carTypeTable.columns.find(column => column.name === 'disabled')) {
                await queryRunner.addColumn('car_type', new TableColumn({
                    name: 'default_price_per_km',
                    type: 'decimal',
                    isNullable: false,
                }));
            }
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropColumn('car_type', 'default_price_per_km');
    }

}
