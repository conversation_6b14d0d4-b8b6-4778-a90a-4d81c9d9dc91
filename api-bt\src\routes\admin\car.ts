import express from 'express';
import {
  createCar_handler,
  deleteCar_handler,
  getAllCars_handler,
  getCar_handler,
  updateCar_handler,
} from '../../controllers/car';

export const carRouter = express.Router();

carRouter.post('/cars', createCar_handler);
carRouter.get('/cars', getAllCars_handler);
carRouter.get('/cars/:id', getCar_handler);
carRouter.put('/cars/:id', updateCar_handler);
carRouter.delete('/cars/:id', deleteCar_handler);
