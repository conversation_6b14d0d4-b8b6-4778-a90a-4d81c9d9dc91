/**
 * Interface for paginated API responses
 */
export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    currentPage: number;
    itemsPerPage: number;
    totalItems: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

/**
 * Interface for pagination parameters
 */
export interface PaginationParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'ASC' | 'DESC';
  [key: string]: any;
}

/**
 * Creates pagination parameters for API requests
 * @param params Pagination parameters
 * @returns Formatted pagination parameters for the API
 */
export function createPaginationParams(params: PaginationParams = {}): Record<string, any> {
  return {
    page: params.page || 1,
    limit: params.limit || 10,
    sort: params.sort,
    order: params.order || 'DESC',
    ...Object.entries(params)
      .filter(([key]) => !['page', 'limit', 'sort', 'order'].includes(key))
      .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {}),
  };
} 