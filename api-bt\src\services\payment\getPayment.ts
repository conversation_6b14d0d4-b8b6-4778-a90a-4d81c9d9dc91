// services/payment.ts
import { getRepository } from 'typeorm';
import { Payment } from '../../models/Payment';

export const getPayment = async (id: string) => {
  const paymentRepository = getRepository(Payment);
  try {
    const payment = await paymentRepository.findOne(id, {
        // relations: ['type', 'booking', 'booking.customer'],
    });
    if (!payment) {
      return { success: false, data: null, error: 'Payment not found' };
    }
    return { success: true, data: payment, error: null };
  } catch (error: any) {
    return { success: false, data: null, error: error.message };
  }
};

export const getPaymentByIntent = async (paymentIntent: string) => {
  const paymentRepository = getRepository(Payment);
  try {
    const payment = await paymentRepository.findOne({
      where: { payment_intent: paymentIntent },
    }, );
    if (!payment) {
      return { success: false, data: null, error: 'Payment not found' };
    }
    return { success: true, data: payment, error: null };
  } catch (error: any) {
    return { success: false, data: null, error: error.message };
  }
};

