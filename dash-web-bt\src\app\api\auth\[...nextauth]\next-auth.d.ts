// next-auth.d.ts
import 'next-auth';

declare module 'next-auth' {
    interface Session {
        user: {
            "success": boolean,
            "token": string,
            "user": {
                "id": number,
                "username": string,
                "first_name": string,
                "last_name": string,
                "country": string,
                "email": string,
                "avatar_url": string,
                "bio": string,
                "created_at": string,
            }
        }
    }
}
