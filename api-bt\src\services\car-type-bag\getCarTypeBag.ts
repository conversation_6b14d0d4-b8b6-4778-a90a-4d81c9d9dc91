import { getRepository } from 'typeorm';
import { CarTypeBag } from '../../models/CarTypeBag';
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const getCarType_Bag = async (id: string) => {
    const carType_BagRepository = getRepository(CarTypeBag);
    try {
        const carType_Bag = await carType_BagRepository.findOne(id);
        if (!carType_Bag) {
            return { success: false, data: null, error: 'CarType_Bag not found' };
        }
        return { success: true, data: carType_Bag, error: null };
    } catch (error) {
        const interpretedError = interpretDatabaseError(error);
        return { success: false, data: null, error: interpretedError };
    }
};
