import { Title, Text } from 'rizzui';

export default function ExpandedRow({ currentDriver }: any) {
  if (!currentDriver) {
    return <Text>No current driver data associated with this car.</Text>;
  }
  return (
    <div className="grid grid-cols-1 divide-y bg-gray-0 px-3.5 dark:bg-gray-50">
      <article
          key={currentDriver.id}
          className="flex items-center justify-between py-6 first-of-type:pt-2.5 last-of-type:pb-2.5"
      >
        <div className="flex items-start">
          <header>
            <Title as="h4" className="mb-0.5 text-sm font-medium">
              {currentDriver.first_name} {currentDriver.last_name}
            </Title>
            {/*<Text className="text-xs text-gray-500">*/}
            {/*  Phone number: ${current_driver.phone_number}*/}
            {/*</Text>*/}
          </header>
        </div>
      </article>
    </div>
  );
}
