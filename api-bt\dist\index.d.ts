/**
 * API Client SDK
 * This is a minimal version of the SDK to demonstrate building correctly.
 */
export declare const apiClient: {
    get: () => Promise<{}>;
    post: () => Promise<{}>;
    put: () => Promise<{}>;
    delete: () => Promise<{}>;
};
export declare const userService: {
    getUsers: () => Promise<any[]>;
    getUser: (id: number) => Promise<{
        id: number;
    }>;
};
export declare const tanstackUseUsers: () => {
    data: {
        users: any[];
    };
};
export declare const useUser: () => {
    data: {
        user: any;
    };
};
export declare const useLogin: () => {
    mutate: () => Promise<void>;
};
export declare const useLogout: () => {
    mutate: () => Promise<void>;
};
export declare const reactUseUsers: () => {
    users: any[];
};
