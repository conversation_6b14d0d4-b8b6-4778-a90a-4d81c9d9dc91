import {getRepository} from 'typeorm';
import {CarType} from '../../models/CarType';
import {Bag} from '../../models/Bag';
import {CarTypeBag} from '../../models/CarTypeBag';
import {CarTypePrice} from '../../models/CarTypePrice';
import {interpretDatabaseError} from '../../utils/interpretDatabaseError';

export const createCarType = async (data: Partial<CarType> & { bags: { bag: string; quantity: number }[] }) => {
    const carTypeRepository = getRepository(CarType);
    const bagRepository = getRepository(Bag);
    const carTypeBagRepository = getRepository(CarTypeBag);
    const carTypePriceRepository = getRepository(CarTypePrice);

    try {
        const carTypeData = { ...data };
        delete carTypeData.transfer_prices;

        // Step 1: Create the CarType
        const newCarType = carTypeRepository.create(carTypeData);

        // Step 2: Save the CarType
        const carType = await carTypeRepository.save(newCarType);

        // console.log('data', JSON.stringify(data, null, 2))

        // Step 3: Process the bags array
        if (data.bags && data.bags.length > 0) {
            for (const bagData of data.bags) {
                const bag = await bagRepository.findOne({where: {id: bagData.bag.id}});

                if (bag) {
                    // Step 4: Create the association between CarType and Bag in CarTypeBag
                    const carTypeBag = carTypeBagRepository.create({
                        carType: newCarType,
                        bag: bag,
                        quantity: bagData.quantity,
                    });

                    // Step 5: Save the CarTypeBag association
                    await carTypeBagRepository.save(carTypeBag);
                } else {
                    throw new Error(`Bag with ID ${bagData.bag} not found`);
                }
            }
        }

        // Step 4: Process the transfer_prices array
        if (data.transfer_prices && data.transfer_prices.length > 0) {
            for (const transferPricesData of data.transfer_prices) {
                if (transferPricesData) {
                    // Step 5: Create the association between CarType and Bag in CarTypeBag
                    const carTypePriceRange = carTypePriceRepository.create({
                        price_per_km: transferPricesData.price_per_km,
                        car_type: carType,
                        name: transferPricesData.name,
                        range_max_distance: transferPricesData.range_max_distance,
                        fixed_charge_value: transferPricesData.fixed_charge_value
                    });

                    // Step 6: Save the CarTypePrice association
                    await carTypePriceRepository.save(carTypePriceRange);
                }
            }
        }

        return {success: true, data: newCarType, error: null};
    } catch (error) {
        const interpretedError = interpretDatabaseError(error);
        return {success: false, data: null, error: interpretedError};
    }
};
