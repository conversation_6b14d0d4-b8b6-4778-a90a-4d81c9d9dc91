import {CarTypePrice} from '../models/CarTypePrice';

type CalculatePriceArgs = {
    distance: number
    ranges: CarTypePrice[]
    defaultPricePerKm: number
}

function calculatePrice({
    distance,
    ranges,
    defaultPricePerKm,
}: CalculatePriceArgs): number {

    if((!ranges || !ranges.length) && defaultPricePerKm && +defaultPricePerKm > 0) {
        return +Number(distance * +defaultPricePerKm).toFixed(2)
    }

    let applicableRange: CarTypePrice | null = null;

    // Iterate over each range to find the applicable range
    for (const range of ranges) {
        if (distance <= range.range_max_distance) {
            // If this is the first applicable range or has a smaller max distance than the current one, use it
            if (!applicableRange || range.range_max_distance < applicableRange.range_max_distance) {
                applicableRange = range;
            }
        }
    }

    // If an applicable range is found, calculate the price
    if (applicableRange) {
        const pricePerKm = applicableRange.price_per_km;
        const fixedCharge = applicableRange.fixed_charge_value;
        return +pricePerKm * +distance + fixedCharge;
    }

    // If no applicable range is found, return 0 as a default case
    return 0;
}

export default calculatePrice;