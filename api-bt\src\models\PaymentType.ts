import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { Payment } from './Payment';

@Entity()
export class PaymentType {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ length: 50 })
  name: string;

  @Column({ length: 500 })
  description: string;

  @Column({ default: false })
  disabled: boolean;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ nullable: true })
  updated_by: number;

  // One-to-many relationship with Payment
  @OneToMany(() => Payment, payment => payment.type)
  payments: Payment[];
}
