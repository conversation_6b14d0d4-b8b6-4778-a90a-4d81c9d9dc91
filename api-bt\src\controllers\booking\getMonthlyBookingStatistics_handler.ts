import {Request, Response} from 'express';
import {getMonthlyBookingStatistics} from '../../services/booking';

export const getMonthlyBookingStatisticsHandler = async (_req: Request, res: Response) => {
    try {
        const aggregates = await getMonthlyBookingStatistics();
        return res.json(aggregates);
    } catch (error: any) {
        return res.status(400).json({error: error.message});
    }
};
