import { routes } from '@/config/routes';
import PageHeader from '@/app/shared/lib/page-header';
import { metaObject } from '@/config/site.config';
import CreateEdit from '@/app/shared/app/customers/create-edit';

export const metadata = {
  ...metaObject('Create Customer'),
};

const pageHeader = {
  title: 'Create Customer',
  breadcrumb: [
    {
      href: routes.dashboard,
      name: 'Dashboard',
    },
    {
      href: routes.customers,
      name: 'Customers',
    },
    {
      name: 'Create Customer',
    },
  ],
};

export default function CreateShipmentPage() {
  return (
    <>
      <PageHeader title={pageHeader.title} breadcrumb={pageHeader.breadcrumb}>
      </PageHeader>

      <CreateEdit />
    </>
  );
}
