import { getRepository } from 'typeorm';
import { Customer } from '../../models/Customer'; // Update the path according to your project structure
import { interpretDatabaseError } from '../../utils/interpretDatabaseError'; // Adjust import path as needed

export const createCustomer = async (data: Partial<Customer>) => {
  const customerRepository = getRepository(Customer);
  try {
    const newCustomer = customerRepository.create(data);
    await customerRepository.save(newCustomer);
    return { success: true, data: newCustomer };
  } catch (error) {
    return { success: false, error: interpretDatabaseError(error) };
  }
};
