import { useState, useEffect } from 'react';
import { Select, Text } from 'rizzui';

interface Bag {
    id: string;
    name: string;
    description: string;
}

export interface BagWithQuantity {
    quantity: number;
    bag: Omit<Bag, 'created_at' | 'updated_at'>;
}

interface Props {
    bags: BagWithQuantity[];
    onSelectionChange: (selectedBags: ReturnBagQuantities[]) => void;
    initialSelectedBags?: ReturnBagQuantities[],
}

export interface ReturnBagQuantities {
    bag: string;
    quantity: number;
}

const isSafariB = () => {
    const ua = navigator.userAgent;
    return ua.includes('Safari') && !ua.includes('Chrome') && !ua.includes('Android');
};

const BagSelectionComponent: React.FC<Props> = ({ bags, onSelectionChange, initialSelectedBags }) => {
    const [selectedBags, setSelectedBags] = useState<ReturnBagQuantities[]>(initialSelectedBags || []);
    const [isIphone, setIsIphone] = useState<boolean>(false);

    useEffect(() => {
        if (isSafariB()) {
            setIsIphone(true);
        }
    }, []);

    const handleQuantityChange = (bagId: string, newQuantity: number) => {
        const updatedSelectedBags = selectedBags.some((item) => item.bag === bagId)
            ? selectedBags.map((item) =>
                item.bag === bagId ? { ...item, quantity: newQuantity } : item
            )
            : [...selectedBags, { bag: bagId, quantity: newQuantity }];

        setSelectedBags(updatedSelectedBags);
        onSelectionChange(updatedSelectedBags); // Assuming you want to propagate changes up.
    };

    const bagQuantity = (quantity: number = 10) => {
        return new Array(quantity + 1).fill(null).map((_, index) => ({
            label: `${index}`,
            value: index,
        }))
    };

    return (
        <div className="p-4">
            {
                bags && bags.length ? (
                    <Text as="span" className="mb-6 text-sm font-medium text-white">
                        What bags should be carried?
                    </Text>
                ) : null
            }

            {bags?.map((bag) => {
                const currentQuantity = selectedBags.find((item) => item.bag === bag.bag.id)?.quantity || 0;
                const options = bagQuantity(bag.quantity);

                return (
                    <div key={bag.bag.id} className="flex flex-wrap items-center gap-4 mb-4 text-white">
                        <div className="flex-1">
                            <label htmlFor="autocomplete" className="text-sm font-medium">
                                {bag.bag.name}
                            </label>
                            <p className="text-sm text-white-600">{bag.bag.description}</p>
                            <p className="text-sm font-semibold">(max: {bag.quantity})</p>
                        </div>

                        {isIphone ? (
                            <select
                                className="flex-initial w-full sm:w-auto p-2 rounded-md"
                                value={currentQuantity}
                                onChange={(e) => handleQuantityChange(bag.bag.id, Number(e.target.value))}
                            >
                                {options.map((option) => (
                                    <option key={option.value} value={option.value}>
                                        {option.label}
                                    </option>
                                ))}
                            </select>
                        ) : (
                            <Select
                                className="flex-initial w-full sm:w-auto"
                                labelClassName="text-white-900"
                                value={currentQuantity}
                                dropdownClassName="p-2 gap-1 grid !z-10"
                                inPortal={false}
                                onChange={(value) => handleQuantityChange(bag.bag.id, Number(value))}
                                options={options}
                                getOptionValue={(option) => option.value}
                                displayValue={(selected: number) => options.find((c) => c.value === selected)?.label ?? ''}
                            />
                        )}
                    </div>
                );
            })}
        </div>
    );
};

export default BagSelectionComponent;
