import Mailjet from 'node-mailjet';

const mailjet = new Mailjet({
    apiKey: process.env.MJ_APIKEY_PUBLIC,
    apiSecret: process.env.MJ_APIKEY_PRIVATE,
});

export async function sendMail(booking: any, customer: any) {
    try {
        // use this tempalte id 6180213
        const mail = await mailjet
            .post("send", {'version': 'v3.1'})
            .request({
                "Messages":[
                    {
                        "From": {
                            "Email": "<EMAIL>",
                            "Name": "click4Transfer"
                        },
                        "To": [
                            {
                                "Email": customer.email,
                                "Name": `${customer.first_name} ${customer.last_name}`
                            }
                        ],
                        "Subject": `Booking Confirmation - ${booking.booking_code || booking.id}`,
                        "TextPart": `Dear ${customer.first_name},\n\nYour booking has been confirmed.\n\nBooking Details:\n- Pickup Address: ${booking.pickup_address}\n- Destination Address: ${booking.destination_address || 'N/A'}\n- Scheduled At: ${booking.scheduled_at}\n\nThank you for choosing our service.\n\nBest regards,\nclick4Transfer`,
                        "HTMLPart": `<h3>Dear ${customer.first_name},</h3><br />Your booking has been confirmed.<br /><br />Booking Details:<br />- Pickup Address: ${booking.pickup_address}<br />- Destination Address: ${booking.destination_address || 'N/A'}<br />- Scheduled At: ${booking.scheduled_at}<br /><br />Thank you for choosing our service.<br /><br />Best regards,<br />click4Transfer`,
                        "CustomID": "BookingConfirmation",
                    }
                ]
            });

        // console.log(mail.body);

        return {
            mail_body: mail.body,
            sent: true,
            err: null,
        }

    } catch(err: any) {
        console.log(err.statusCode);

        return {
            mail_body: null,
            sent: false,
            err: `${err.statusCode} / ${err.message}`,
        }
    }
}
