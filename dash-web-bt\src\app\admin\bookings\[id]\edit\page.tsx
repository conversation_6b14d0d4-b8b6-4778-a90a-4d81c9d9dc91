'use client';

import { Metadata } from 'next';
import { routes } from '@/config/routes';
import { metaObject } from '@/config/site.config';
import PageHeader from '@/app/shared/lib/page-header';
import CreateEdit from '@/app/shared/app/bookings/create-edit';
import {useGetBooking} from "@/services/query/booking/admin/useGetBooking";

const pageHeader = {
  title: 'Edit Booking',
  breadcrumb: [
    {
      href: routes.dashboard,
      name: 'Dashboard',
    },
    {
      href: routes.bookings,
      name: 'Bookings',
    },
    {
      name: 'Edit Booking',
    },
  ],
};

export default function EditShipmentsPage({
  params,
}: {
  params: { id: string };
}) {
  const id = params.id;

  const {
    isPending,
    isError,
    error,
    data,
    isFetching,
    isPlaceholderData,
    refetch,
    status,
  } = useGetBooking(id);

  return (
    <>
      <PageHeader title={pageHeader.title} breadcrumb={pageHeader.breadcrumb}>
        {/*<ImportButton title={'Import File'} />*/}
      </PageHeader>

      {
        data?.data ? <CreateEdit id={id} booking={data?.data} /> : null
      }
    </>
  );
}
