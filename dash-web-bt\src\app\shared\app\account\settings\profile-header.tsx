import {useLayout} from "@/hooks/use-layout";
import {useBerylliumSidebars} from "@/layouts/beryllium/beryllium-utils";
import cn from "@/utils/class-names";
import {LAYOUT_OPTIONS} from "@/config/enums";
import Image from "next/image";
import {Text, Title} from "rizzui";
import {PiSealCheckFill} from "react-icons/pi";

export function ProfileHeader({
                                  title,
                                  bio,
                                  children,
                                  avatar,
                              }: React.PropsWithChildren<{ title: string | undefined; bio?: string, avatar?: string }>) {
    const {layout} = useLayout();
    const {expandedLeft} = useBerylliumSidebars();

    if(!avatar) avatar = `https://gravatar.com/avatar/e76cd40a8214ddde4fce91ed3675eb65?s=400&d=robohash&r=x`;

    return (
        <div
            className={cn(
                'relative z-0 -mx-4 px-4 pt-28 before:absolute before:start-0 before:top-0 before:h-40 before:w-full before:bg-gradient-to-r before:from-[#F8E1AF] before:to-[#F6CFCF] @3xl:pt-[190px] @3xl:before:h-[calc(100%-120px)] dark:before:from-[#bca981] dark:before:to-[#cbb4b4] md:-mx-5 md:px-5 lg:-mx-8 lg:px-8 xl:-mx-6 xl:px-6 3xl:-mx-[33px] 3xl:px-[33px] 4xl:-mx-10 4xl:px-10',
                layout === LAYOUT_OPTIONS.BERYLLIUM && expandedLeft
                    ? 'before:start-5 3xl:before:start-[25px]'
                    : 'xl:before:w-[calc(100%_+_10px)]'
            )}
        >
            <div
                className="relative z-10 mx-auto flex w-full max-w-screen-2xl flex-wrap items-end justify-start gap-6 border-b border-dashed border-muted pb-10">
                <div
                    className="relative -top-1/3 aspect-square w-[110px] overflow-hidden rounded-full border-[6px] border-white bg-gray-100 shadow-profilePic @2xl:w-[130px] @5xl:-top-2/3 @5xl:w-[150px] dark:border-gray-50 3xl:w-[200px]">
                    <Image
                        src={avatar}
                        alt="profile-pic"
                        fill
                        sizes="(max-width: 768px) 100vw"
                        className="aspect-auto"
                    />
                </div>
                <div>
                    <Title
                        as="h2"
                        className="mb-2 inline-flex items-center gap-3 text-xl font-bold text-gray-900"
                    >
                        {title}
                        <PiSealCheckFill className="h-5 w-5 text-primary md:h-6 md:w-6"/>
                    </Title>
                    {bio ? (
                        <Text dangerouslySetInnerHTML={{ __html: bio }} className="text-sm text-gray-500"></Text>
                    ) : null}
                </div>
                {children}
            </div>
        </div>
    );
}
