"use client";
import React from "react";
import { StickyScroll } from "@/components/app/StickyScrollReveal";
import Image from "next/image";
import malaga from '@public/cities/malaga.jpg';
import sevilla from '@public/cities/sevilla.jpg';
import huelva from '@public/cities/huelva.jpg';
import granada from '@public/cities/granada.jpg';
import cadiz from '@public/cities/cadiz.jpg';
import { useI18n } from "@/hooks/use-translation";

export default function StickyScrollRevealDemo() {
    const { t } = useI18n();

    const content = [
        {
            title: t("city.seville.title"),
            description: t("city.seville.description"),
            content: (
                <div className="h-full w-full flex items-center justify-center text-white">
                    <Image
                        src={sevilla}
                        width={300}
                        height={300}
                        className="h-full w-full object-cover"
                        alt={t("city.seville.alt")}
                    />
                </div>
            ),
        },
        {
            title: t("city.huelva.title"),
            description: t("city.huelva.description"),
            content: (
                <div className="h-full w-full flex items-center justify-center text-white">
                    <Image
                        src={huelva}
                        width={300}
                        height={300}
                        className="h-full w-full object-cover"
                        alt={t("city.huelva.alt")}
                    />
                </div>
            ),
        },
        {
            title: t("city.cadiz.title"),
            description: t("city.cadiz.description"),
            content: (
                <div className="h-full w-full flex items-center justify-center text-white">
                    <Image
                        src={cadiz}
                        width={300}
                        height={300}
                        className="h-full w-full object-cover"
                        alt={t("city.cadiz.alt")}
                    />
                </div>
            ),
        },
        {
            title: t("city.malaga.title"),
            description: t("city.malaga.description"),
            content: (
                <div className="h-full w-full flex items-center justify-center text-white">
                    <Image
                        src={malaga}
                        width={300}
                        height={300}
                        className="h-full w-full object-cover"
                        alt={t("city.malaga.alt")}
                    />
                </div>
            ),
        },
        {
            title: t("city.granada.title"),
            description: t("city.granada.description"),
            content: (
                <div className="h-full w-full flex items-center justify-center text-white">
                    <Image
                        src={granada}
                        width={300}
                        height={300}
                        className="h-full w-full object-cover"
                        alt={t("city.granada.alt")}
                    />
                </div>
            ),
        },
    ];

    return (
        <div className="p-10">
            <StickyScroll content={content} />
        </div>
    );
}
