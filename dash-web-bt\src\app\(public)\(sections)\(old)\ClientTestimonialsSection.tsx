// ClientTestimonialsSection.tsx
import React from 'react';
import Container from '@/app/(public)/(sections)/(old)/container';
import {FaQuoteLeft} from 'react-icons/fa'; // Icon for quotes

const ClientTestimonialsSection: React.FC = () => {
    return (
        <section className="bg-white text-gray-800">
            <Container>
                <section className="text-gray-600 body-font">
                    <div className="container px-5 py-8 mx-auto">
                        <div className="text-center mb-12">
                            <h1 className="text-3xl font-semibold mb-4">Lo que dicen nuestros clientes</h1>
                            <p className="max-w-xl mx-auto">Tu satisfacción es nuestra prioridad. Lee lo que nuestros
                                clientes dicen de nosotros.</p>
                        </div>
                        <div className="flex flex-wrap">
                            <div className="p-4 md:w-1/3 w-full">
                                <div className="h-full bg-gray-100 p-8 rounded">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                         className="block w-5 h-5 text-gray-400 mb-4" viewBox="0 0 975.036 975.036">
                                        <path
                                            d="M925.036 57.197h-304c-27.6 0-50 22.4-50 50v304c0 27.601 22.4 50 50 50h145.5c-1.9 79.601-20.4 143.3-55.4 191.2-27.6 37.8-69.399 69.1-125.3 93.8-25.7 11.3-36.8 41.7-24.8 67.101l36 76c11.6 24.399 40.3 35.1 65.1 24.399 66.2-28.6 122.101-64.8 167.7-108.8 55.601-53.7 93.7-114.3 114.3-181.9 20.601-67.6 30.9-159.8 30.9-276.8v-239c0-27.599-22.401-50-50-50zM106.036 913.497c65.4-28.5 121-64.699 166.9-108.6 56.1-53.7 94.4-114.1 115-181.2 20.6-67.1 30.899-159.6 30.899-277.5v-239c0-27.6-22.399-50-50-50h-304c-27.6 0-50 22.4-50 50v304c0 27.601 22.4 50 50 50h145.5c-1.9 79.601-20.4 143.3-55.4 191.2-27.6 37.8-69.4 69.1-125.3 93.8-25.7 11.3-36.8 41.7-24.8 67.101l35.9 75.8c11.601 24.399 40.501 35.2 65.301 24.399z"></path>
                                    </svg>
                                    <p className="leading-relaxed mb-6">&quotUn servicio excepcional y un trato muy
                                        profesional. Los vehículos son cómodos y elegantes. Altamente
                                        recomendado.&quot</p>
                                    <a className="inline-flex items-center">
                                        <span className="flex-grow flex flex-col pl-4">
                                          <span
                                              className="title-font font-medium text-gray-900">- Alejandro Gómez</span>
                                        </span>
                                    </a>
                                </div>
                            </div>
                            <div className="p-4 md:w-1/3 w-full">
                                <div className="h-full bg-gray-100 p-8 rounded">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                         className="block w-5 h-5 text-gray-400 mb-4" viewBox="0 0 975.036 975.036">
                                        <path
                                            d="M925.036 57.197h-304c-27.6 0-50 22.4-50 50v304c0 27.601 22.4 50 50 50h145.5c-1.9 79.601-20.4 143.3-55.4 191.2-27.6 37.8-69.399 69.1-125.3 93.8-25.7 11.3-36.8 41.7-24.8 67.101l36 76c11.6 24.399 40.3 35.1 65.1 24.399 66.2-28.6 122.101-64.8 167.7-108.8 55.601-53.7 93.7-114.3 114.3-181.9 20.601-67.6 30.9-159.8 30.9-276.8v-239c0-27.599-22.401-50-50-50zM106.036 913.497c65.4-28.5 121-64.699 166.9-108.6 56.1-53.7 94.4-114.1 115-181.2 20.6-67.1 30.899-159.6 30.899-277.5v-239c0-27.6-22.399-50-50-50h-304c-27.6 0-50 22.4-50 50v304c0 27.601 22.4 50 50 50h145.5c-1.9 79.601-20.4 143.3-55.4 191.2-27.6 37.8-69.4 69.1-125.3 93.8-25.7 11.3-36.8 41.7-24.8 67.101l35.9 75.8c11.601 24.399 40.501 35.2 65.301 24.399z"></path>
                                    </svg>
                                    <p className="leading-relaxed mb-6">&quotLa experiencia fue impecable desde el
                                        principio hasta el final. ¡Definitivamente usaré Click4Transfer para todos mis
                                        viajes!&quot</p>
                                    <a className="inline-flex items-center">
                                        <span className="flex-grow flex flex-col pl-4">
                                          <span className="title-font font-medium text-gray-900">- Martina Ruiz</span>
                                        </span>
                                    </a>
                                </div>
                            </div>
                            <div className="p-4 md:w-1/3 w-full">
                                <div className="h-full bg-gray-100 p-8 rounded">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                         className="block w-5 h-5 text-gray-400 mb-4" viewBox="0 0 975.036 975.036">
                                        <path
                                            d="M925.036 57.197h-304c-27.6 0-50 22.4-50 50v304c0 27.601 22.4 50 50 50h145.5c-1.9 79.601-20.4 143.3-55.4 191.2-27.6 37.8-69.399 69.1-125.3 93.8-25.7 11.3-36.8 41.7-24.8 67.101l36 76c11.6 24.399 40.3 35.1 65.1 24.399 66.2-28.6 122.101-64.8 167.7-108.8 55.601-53.7 93.7-114.3 114.3-181.9 20.601-67.6 30.9-159.8 30.9-276.8v-239c0-27.599-22.401-50-50-50zM106.036 913.497c65.4-28.5 121-64.699 166.9-108.6 56.1-53.7 94.4-114.1 115-181.2 20.6-67.1 30.899-159.6 30.899-277.5v-239c0-27.6-22.399-50-50-50h-304c-27.6 0-50 22.4-50 50v304c0 27.601 22.4 50 50 50h145.5c-1.9 79.601-20.4 143.3-55.4 191.2-27.6 37.8-69.4 69.1-125.3 93.8-25.7 11.3-36.8 41.7-24.8 67.101l35.9 75.8c11.601 24.399 40.501 35.2 65.301 24.399z"></path>
                                    </svg>
                                    <p className="leading-relaxed mb-6">&quotImpresionado con la puntualidad y el servicio al cliente. Hicieron que
                                        mi viaje de negocios fuera mucho más relajante.&quot</p>
                                    <a className="inline-flex items-center">
                                        <span className="flex-grow flex flex-col pl-4">
                                          <span className="title-font font-medium text-gray-900">- Daniel Fernández</span>
                                        </span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </Container>
        </section>
    );
};

export default ClientTestimonialsSection;
