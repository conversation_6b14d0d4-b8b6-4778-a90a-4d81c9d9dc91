import { getRepository } from 'typeorm';
import {PaymentType} from "../../models/PaymentType";
import {paginate, PaginatedResult} from "../../utils/pagination";

export const getAllPaymentTypes = async (
    page = 1,
    limit = 10,
): Promise<PaginatedResult<PaymentType>> => {
  const paymentTypeRepository = getRepository(PaymentType);

  const options: any = {
    skip: (page - 1) * limit,
    take: limit,
    relations: ['payments'],
    order: {
      created_at: 'DESC',  // Order by created_at in descending order
    },
  };

  const [data, totalRecords] = await paymentTypeRepository.findAndCount(options);

  return paginate<PaymentType>(data, page, limit, totalRecords);
};
