import { getRepository } from 'typeorm';
import { CarTypeBag } from '../../models/CarTypeBag';
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const createCarType_Bag = async (data: Partial<CarTypeBag>) => {
    const carType_BagRepository = getRepository(CarTypeBag);
    try {
        const newCarType_Bag = carType_BagRepository.create(data);
        await carType_BagRepository.save(newCarType_Bag);
        return { success: true, data: newCarType_Bag, error: null };
    } catch (error) {
        const interpretedError = interpretDatabaseError(error);
        return { success: false, data: null, error: interpretedError };
    }
};
