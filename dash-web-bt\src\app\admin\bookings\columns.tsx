'use client';

import Link from 'next/link';
import { Text, Badge, Tooltip, Checkbox, ActionIcon } from 'rizzui';
import { HeaderCell } from '@/components/lib/ui/table';
import PencilIcon from '@/components/lib/icons/pencil';
import AvatarCard from '@/components/lib/ui/avatar-card';
import DateCell from '@/components/lib/ui/date-cell';
import DeletePopover from '@/app/shared/lib/delete-popover';
import { routes } from "@/config/routes";
import { PiPrinterBold, PiDownloadSimpleBold, PiCheckBold, PiXBold, PiClockBold } from 'react-icons/pi';

type Status = 'pending' | 'accepted' | 'rejected' | 'cancelled' | 'completed';

const statusStyles: Record<Status, { icon: React.ReactNode, color: string, font: string, animation: string }> = {
    pending: {
        icon: <PiClockBold className="mr-2" />,
        color: 'text-yellow-500',
        font: 'font-medium',
        animation: 'animate-pulse'
    },
    accepted: {
        icon: <PiCheckBold className="mr-2" />,
        color: 'text-green-500',
        font: 'font-bold',
        animation: 'animate-bounce'
    },
    rejected: {
        icon: <PiXBold className="mr-2" />,
        color: 'text-red-500',
        font: 'font-bold',
        animation: 'animate-shake'
    },
    cancelled: {
        icon: <PiXBold className="mr-2" />,
        color: 'text-gray-500',
        font: 'font-medium',
        animation: ''
    },
    completed: {
        icon: <PiPrinterBold className="mr-2" />,
        color: 'text-blue-500',
        font: 'font-semibold',
        animation: 'animate-shake'
    }
};

interface StatusTextProps {
    status: Status;
}

const StatusText: React.FC<StatusTextProps> = ({ status }) => {
    const { icon, color, font, animation } = statusStyles[status];
    return (
        <div className={`flex items-center justify-center ${color} ${font} ${animation} transition-all duration-300 ease-in-out`}>
            {icon}
            <span className="dark:text-gray-600 text-center uppercase tracking-wide">
                {status}
            </span>
        </div>
    );
};

function getStatusBadge(status: boolean) {
    switch (status) {
        case true:
            return (
                <div className="flex items-center">
                    <Badge color="success" renderAsDot/>
                    <Text className="ms-2 font-bold text-green-700">{status} Paid</Text>
                </div>
            );
        case false:
            return (
                <div className="flex items-center">
                    <Badge color="danger" renderAsDot/>
                    <Text className="ms-2 font-bold text-red-700">{status} Unpaid</Text>
                </div>
            );
        default:
            return (
                <div className="flex items-center">
                    <Badge renderAsDot className="bg-gray-400"/>
                    <Text className="ms-2 font-bold text-gray-600">{status} No Status</Text>
                </div>
            );
    }
}

type Columns = {
    data: any[];
    sortConfig?: any;
    handleSelectAll: any;
    checkedItems: string[];
    onDeleteItem: (id: string) => void;
    onHeaderCellClick: (value: string) => void;
    onChecked?: (id: string) => void;
};

// @ts-ignore
export const getColumns = ({
                               data,
                               sortConfig,
                               checkedItems,
                               onDeleteItem,
                               onHeaderCellClick,
                               handleSelectAll,
                               onChecked,
                           }: Columns) => [
        {
            title: <HeaderCell title="Car Type"/>,
            dataIndex: 'car_type',
            key: 'car_type',
            width: 120,
            render: (value: any, row: any) => {
                return (
                    <AvatarCard src="https://via.placeholder.com/150" name={row?.car_type?.name.replace(/\[[-]?\d+\],?/g, '')}
                                description={row?.car_type?.seats} className=""/>
                );
        },
    },
    {
        title: (
            <HeaderCell
                title="Scheduled At"
                sortable
                ascending={
                    sortConfig?.direction === 'asc' && sortConfig?.key === 'scheduled_at'
                }
            />
        ),
        onHeaderCell: () => onHeaderCellClick('scheduled_at'),
        dataIndex: 'scheduled_at',
        key: 'scheduled_at',
        width: 200,
        render: (value: Date) => {
            const currentDate = new Date().getTime();
            const isPast = new Date(value).getTime() < currentDate;
            const backgroundColor = isPast ? 'bg-red-100' : 'bg-green-100';

            return <DateCell date={value} className={`${backgroundColor} p-2 rounded-md shadow-inner`} />;
        },
    },
    {
        title: <HeaderCell title="Customer"/>,
        dataIndex: 'customer',
        key: 'customer',
        width: 120,
        render: (_value: any, row: any) => {
            return <a className='text-lg hover:text-purple-600 transition-colors duration-200' href={routes.customerEdit(row?.customer?.id)}><b>{row?.customer?.first_name || row?.customer?.last_name}</b></a>
        },
    },
    {
        title: (
            <HeaderCell
                title="Payment Type"
                sortable
                ascending={
                    sortConfig?.direction === 'asc' && sortConfig?.key === 'payment'
                }
            />
        ),
        onHeaderCell: () => onHeaderCellClick('payment'),
        dataIndex: 'payment',
        key: 'payment_type',
        width: 200,
        render: (_value: string, row: any) => {
            return <Text className={`font-medium text-center`}>
                <span className={`text-${row.payment?.type.name === 'Card' ? 'green-500' : 'brown-500'} font-extrabold uppercase`}>{row.payment?.type?.name}</span>
            </Text>
        },
    },
    {
        title: (
            <HeaderCell
                title="Order Status"
                sortable
                ascending={
                    sortConfig?.direction === 'asc' && sortConfig?.key === 'status'
                }
            />
        ),
        onHeaderCell: () => onHeaderCellClick('status'),
        dataIndex: 'status',
        key: 'status',
        width: 200,
        render: (value: string, row: any) => {
            return <StatusText status={row.status} />
        },
    },
    {
        title: <HeaderCell title="Paid Status"/>,
        dataIndex: 'paid_status',
        key: 'paid_status',
        width: 120,
        render: (value: boolean, row: any) => getStatusBadge(row.payment?.paid_status),
    },
    {
        title: (
            <HeaderCell
                title="Amount to Pay (Є)"
                sortable
                ascending={
                    sortConfig?.direction === 'asc' && sortConfig?.key === 'amount_to_pay'
                }
            />
        ),
        onHeaderCell: () => onHeaderCellClick('amount_to_pay'),
        dataIndex: 'amount_to_pay',
        key: 'amount_to_pay',
        width: 200,
        render: (value: string, row: any) => {
            return <Text className="font-medium text-amber-900 dark:text-gray-600 text-center">
                {Number(row.payment?.amount).toFixed(2)}
            </Text>
        },
    },
    {
        title: (
            <HeaderCell
                title="Amount Paid (Є)"
                sortable
                ascending={
                    sortConfig?.direction === 'asc' && sortConfig?.key === 'amount_paid'
                }
            />
        ),
        onHeaderCell: () => onHeaderCellClick('amount_paid'),
        dataIndex: 'amount_paid',
        key: 'amount_paid',
        width: 200,
        render: (value: string, row: any) => {
            const color = row.payment?.amount_paid >= row.payment?.amount ? 'green-500' : 'red-500';
            return <Text className={`font-medium text-center`}>
                <span className={`text-${color} font-extrabold`}>{Number(row.payment?.amount_paid).toFixed(2)}</span>
            </Text>
        },
    },
    {
        title: (
            <HeaderCell
                title="Remaining (Є)"
                sortable
                ascending={
                    sortConfig?.direction === 'asc' && sortConfig?.key === 'remaining'
                }
            />
        ),
        onHeaderCell: () => onHeaderCellClick('remaining'),
        dataIndex: 'remaining',
        key: 'remaining',
        width: 200,
        render: (value: string, row: any) => {
            if(row.payment?.amount && row.payment?.amount_paid) {
                const remaining = +row.payment?.amount - +row.payment?.amount_paid;
                const color = +remaining <= 0 ? 'green-500' : 'red-500';
                return <Text className={`font-medium text-center`}>
                    <span className={`text-${color} font-extrabold`}>{Number(remaining).toFixed(2)}</span>
                </Text>
            }
            return '-';
        },
    },
    {
        title: (
            <HeaderCell
                title="Booking Code"
                sortable
                ascending={
                    sortConfig?.direction === 'asc' && sortConfig?.key === 'booking_code'
                }
            />
        ),
        onHeaderCell: () => onHeaderCellClick('booking_code'),
        dataIndex: 'booking_code',
        key: 'booking_code',
        width: 200,
        render: (value: string) => <Text className={`font-medium text-center`}>
            <span className={`text-chillGold uppercase`}>{value}</span>
        </Text>,
    },
    {
        title: <HeaderCell title="One way"/>,
        dataIndex: 'one_way_trip',
        key: 'one_way_trip',
        width: 120,
        render: (value: boolean) => value ? <span className="text-green-500 font-bold">Yes</span> : <span className="text-red-500 font-bold">No</span>,
    },
    {
        title: (
            <HeaderCell
                title="Created"
                sortable
                ascending={
                    sortConfig?.direction === 'asc' && sortConfig?.key === 'created_at'
                }
            />
        ),
        onHeaderCell: () => onHeaderCellClick('created_at'),
        dataIndex: 'created_at',
        key: 'created_at',
        width: 200,
        render: (value: Date) => <DateCell date={value} className="bg-gray-50 p-2 rounded-md shadow-inner"/>,
    },
    {
        title: <></>,
        dataIndex: 'action',
        key: 'action',
        width: 140,
        render: (_: string, row: any) => (
            <div className="flex items-center justify-end gap-3 pe-3">
                <Tooltip
                    size="sm"
                    content={'Edit Booking'}
                    placement="top"
                    color="invert"
                >
                    <Link href={routes.bookingEdit(row.id)}>
                        <ActionIcon
                            as="span"
                            size="sm"
                            variant="outline"
                            className="hover:!border-gray-900 hover:text-gray-700 hover:scale-105 transition-transform duration-200"
                        >
                            <PencilIcon className="h-4 w-4"/>
                        </ActionIcon>
                    </Link>
                </Tooltip>
                <DeletePopover
                    title={'Delete the booking'}
                    description={'Are you sure you want to delete this #${row.id} booking ?'}
                    onDelete={() => onDeleteItem(row.id)}
                />
            </div>
        ),
    },
];
