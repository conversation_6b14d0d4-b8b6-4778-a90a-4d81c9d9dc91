import { types, Instance, SnapshotIn } from 'mobx-state-tree';
import { CarTypeModel } from './car-type-model';

/**
 * Car model in MST
 */
export const CarModel = types.model('CarModel', {
  id: types.identifier,
  name: types.string,
  image: types.optional(types.string, 'https://via.placeholder.com/150'),
  brand: types.maybeNull(types.string),
  model: types.maybeNull(types.string),
  allow_pets: types.optional(types.boolean, true),
  licence_plate: types.maybeNull(types.string),
  bags_capacity: types.number,
  created_at: types.string, 
  updated_at: types.string, 
  updated_by: types.maybeNull(types.number),
  type: types.maybeNull(types.late(() => CarTypeModel)),
})
.views(self => ({
  /**
   * Get created date as Date object
   */
  get createdDate(): Date {
    return new Date(self.created_at);
  },
  
  /**
   * Get updated date as Date object
   */
  get updatedDate(): Date {
    return new Date(self.updated_at);
  },
  
  /**
   * Get full name with brand and model
   */
  get fullName(): string {
    const parts = [];
    if (self.brand) parts.push(self.brand);
    if (self.model) parts.push(self.model);
    if (parts.length === 0) return self.name;
    
    return `${self.name} (${parts.join(' ')})`;
  }
}));

// Type definitions for TypeScript
export interface Car extends Instance<typeof CarModel> {}
export interface CarSnapshotIn extends SnapshotIn<typeof CarModel> {} 