import { Request, Response } from 'express';
import {
    createCarTypePrice,
} from '../../services/car-type-price'; // Adjust the path as necessary

export const createCarTypePriceHandler = async (req: Request, res: Response) => {
    try {
        const data = req.body;
        const result = await createCarTypePrice(data);
        if (!result.success) {
            return res.status(404).json(result);
        }
        return res.json(result);
    } catch (error: any) {
        return res.status(500).json({
            success: false,
            message: 'Failed to create car type price',
            errorType: 'InternalServerError',
            data: undefined,
        });
    }
};
