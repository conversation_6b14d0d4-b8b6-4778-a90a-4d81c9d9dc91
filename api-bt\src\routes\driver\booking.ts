import express from 'express';
import {
  // createBookingHandler,
  // deleteBookingHandler,
  getAllBookingsHandler,
  getBookingHandler,
  updateBookingHandler,
  // getMonthlyBookingStatisticsHandler,
  getBookingByBookingCodeHandler,
} from '../../controllers/booking';

export const driverBookingRouter = express.Router();

// driverBookingRouter.post('/bookings', createBookingHandler);
driverBookingRouter.get('/bookings', getAllBookingsHandler);
// driverBookingRouter.get('/bookings/statistics/monthly', getMonthlyBookingStatisticsHandler);
driverBookingRouter.get('/bookings/:id', getBookingHandler);
driverBookingRouter.get('/bookings/by/:booking_code', getBookingByBookingCodeHandler);
driverBookingRouter.put('/bookings/:id', updateBookingHandler);
// driverBookingRouter.delete('/bookings/:id', deleteBookingHandler);
