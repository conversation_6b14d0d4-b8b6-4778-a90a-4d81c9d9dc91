import { Request, Response } from 'express';
import {
  getAllCarTypes,
} from '../../services/car-type';

export const hasCarTypes_handler = async (req: Request, res: Response) => {
  try {
    const page = 1;
    const limit = 100;
    const seats = parseInt(req.query.seats as string, 10) || 0;
    const paginatedResult = await getAllCarTypes(page, limit, seats);

    if(!seats) {
      return res.json({
        found: 0,
        hasCarTypes: false,
        error: `Please provide &seats.`,
      });
    }

    if(paginatedResult && paginatedResult.data && paginatedResult.data.length > 0) {
      return res.json({
        found: paginatedResult.data.length,
        hasCarTypes: true,
        error: null,
      });
    } else {
      return res.json({
        found: paginatedResult.data.length,
        hasCarTypes: false,
        error: `Could not find any Car Types that can accommodate ${seats} people.`,
      });
    }
  } catch (err: any) {
    return res.status(400).json({ error: err.message });
  }
};
