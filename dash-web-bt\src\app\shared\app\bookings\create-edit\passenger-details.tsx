import {useFormContext} from 'react-hook-form';
import {Input, Checkbox} from 'rizzui';
import cn from '@/utils/class-names';
import FormGroup from '@/app/shared/lib/form-group';

interface FormSenderInfoProps {
    className?: string;
}

export default function PassengerDetails({className}: FormSenderInfoProps) {
    const {
        register,
        formState: {errors},
    } = useFormContext();
    return (
        <FormGroup
            title="Passenger Information"
            description="Create passenger information here"
            className={cn(className)}
        >
            <Input
                label="Adults"
                type={'number'}
                placeholder="1"
                labelClassName="font-medium text-gray-900"
                {...register('adults', {valueAsNumber: true})}
                error={errors.adults?.message as string}
            />
            <Input
                label="Children"
                type={'number'}
                placeholder="1"
                labelClassName="font-medium text-gray-900"
                {...register('children', {valueAsNumber: true})}
                error={errors.children?.message as string}
            />
            <Input
                label="Baby seat (3 years old or under)"
                type={'number'}
                placeholder="1"
                labelClassName="font-medium text-gray-900"
                {...register('children_chairs_under_five', {valueAsNumber: true})}
                error={errors.children_chairs_under_five?.message as string}
            />
            <Input
                label="Booster Seat (older than 3 years old)"
                type={'number'}
                placeholder="1"
                labelClassName="font-medium text-gray-900"
                {...register('children_chairs_above_five', {valueAsNumber: true})}
                error={errors.children_chairs_above_five?.message as string}
            />
            <Input
                label="Pets"
                type={'number'}
                placeholder="1"
                labelClassName="font-medium text-gray-900"
                {...register('pets', {valueAsNumber: true})}
                error={errors.pets?.message as string}
            />
            <Input
                label="Flight Number"
                placeholder="flight number"
                labelClassName="font-medium text-gray-900"
                {...register('flight_number')}
                error={errors.flight_number?.message as string}
            />
            <Input
                label="Other Details"
                placeholder="Other details"
                labelClassName="font-medium text-gray-900"
                {...register('other_details')}
                error={errors.other_details?.message as string}
            />
        </FormGroup>
    );
}
