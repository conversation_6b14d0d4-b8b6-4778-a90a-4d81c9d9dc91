import { UserSchema, UserCreateSchema, UserUpdateSchema } from '../../models/user';

describe('User models', () => {
  describe('UserSchema', () => {
    it('should validate a valid user', () => {
      const validUser = {
        id: 1,
        username: 'testuser',
        email: '<EMAIL>',
        first_name: 'Test',
        last_name: 'User',
        avatar_url: 'https://example.com/avatar.jpg',
        created_at: '2023-01-01T12:00:00Z',
        role: {
          id: 1,
          name: 'admin',
        },
      };

      const result = UserSchema.safeParse(validUser);
      expect(result.success).toBe(true);
      
      if (result.success) {
        expect(result.data.id).toBe(validUser.id);
        expect(result.data.username).toBe(validUser.username);
        expect(result.data.email).toBe(validUser.email);
        expect(result.data.created_at instanceof Date).toBe(true);
      }
    });

    it('should reject an invalid user', () => {
      const invalidUser = {
        id: 1,
        username: 'tu', // too short
        email: 'not-an-email',
        first_name: 'Test',
        last_name: 'User',
        created_at: '2023-01-01T12:00:00Z',
      };

      const result = UserSchema.safeParse(invalidUser);
      expect(result.success).toBe(false);
      
      if (!result.success) {
        expect(result.error.issues.length).toBeGreaterThan(0);
        const errorMap = result.error.flatten().fieldErrors;
        expect(errorMap.username).toBeDefined();
        expect(errorMap.email).toBeDefined();
      }
    });
  });

  describe('UserCreateSchema', () => {
    it('should validate valid user creation data', () => {
      const validCreateData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
        confirmed_password: 'password123',
        first_name: 'Test',
        last_name: 'User',
      };

      const result = UserCreateSchema.safeParse(validCreateData);
      expect(result.success).toBe(true);
    });

    it('should reject when passwords do not match', () => {
      const dataWithMismatchedPasswords = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
        confirmed_password: 'different',
        first_name: 'Test',
        last_name: 'User',
      };

      const result = UserCreateSchema.safeParse(dataWithMismatchedPasswords);
      expect(result.success).toBe(false);
      
      if (!result.success) {
        const errorMap = result.error.flatten().fieldErrors;
        expect(errorMap.confirmed_password).toBeDefined();
      }
    });
  });

  describe('UserUpdateSchema', () => {
    it('should validate partial user data for update', () => {
      const validUpdateData = {
        email: '<EMAIL>',
        first_name: 'Updated',
      };

      const result = UserUpdateSchema.safeParse(validUpdateData);
      expect(result.success).toBe(true);
    });

    it('should reject invalid data for update', () => {
      const invalidUpdateData = {
        email: 'not-an-email',
      };

      const result = UserUpdateSchema.safeParse(invalidUpdateData);
      expect(result.success).toBe(false);
      
      if (!result.success) {
        const errorMap = result.error.flatten().fieldErrors;
        expect(errorMap.email).toBeDefined();
      }
    });

    it('should allow empty fields when they are optional', () => {
      const updateWithOptionalFields = {
        bio: '',
        avatar_url: '',
        country: '',
      };

      const result = UserUpdateSchema.safeParse(updateWithOptionalFields);
      expect(result.success).toBe(true);
    });
  });
}); 