'use client';

import { routes } from '@/config/routes';
import PageHeader from '@/app/shared/lib/page-header';
import CreateEdit from "@/app/shared/app/bags/create-edit";
import {useGetBag} from "@/services/query/bag/useGetBag";

const pageHeader = {
  title: 'Edit Bag',
  breadcrumb: [
    {
      href: routes.dashboard,
      name: 'Dashboard',
    },
    {
      href: routes.bags,
      name: 'Bags',
    },
    {
      name: 'Edit Bag',
    },
  ],
};

export default function Edit({
  params,
}: {
  params: { id: string };
}) {
  const id = params.id;

  const {
    // isPending,
    // isError,
    // error,
    data,
    // isFetching,
    // isPlaceholderData,
    // refetch,
    // status,
  } = useGetBag(id);

  return (
    <>
      <PageHeader title={pageHeader.title} breadcrumb={pageHeader.breadcrumb}>
        {/*<ImportButton title={'Import File'} />*/}
      </PageHeader>

      {
        data?.data ? <CreateEdit id={id} bag={data?.data} /> : null
      }
    </>
  );
}
