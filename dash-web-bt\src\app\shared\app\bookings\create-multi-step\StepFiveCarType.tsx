'use client';

import { use<PERSON><PERSON> } from 'jotai';
import React, {useEffect, useState} from 'react';
import { toast } from 'react-hot-toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import { Text } from 'rizzui';
import {
  formDataAtom,
  useStepperOne,
} from '@/app/shared/app/bookings/create-multi-step';
import FormSummary from '@/app/shared/app/bookings/create-multi-step/form-summary';
import {
  CarTypeSchema,
  carTypeSchema,
} from './form/multistep-form.schema';
import {SearchableSelect} from "@/components/app/searchable-select";
import {usePaginatedCarTypes} from "@/services/query/car-type/public/usePaginatedCarTypes";
import {CarTypeSchemaType} from "@/utils/validators/app/entities";
import BagSelectionComponent, {
  BagWithQuantity,
  ReturnBagQuantities
} from "@/app/shared/app/bookings/create-edit/sections/BagSelectionComponent";
import {useBookingSession} from "@/hooks/use-booking-ls";

export default function StepTwo() {
  const { step, gotoNextStep } = useStepperOne();
  const [formData, setFormData] = useAtom(formDataAtom);
  const [selectedCarType, setSelectedCarType] = useState<CarTypeSchemaType | null>(() => null);
  const currentStep = 5;
  const bookingSession = useBookingSession();

  const {
    control,
    formState: { errors },
    setValue,
    getValues,
    watch,
    handleSubmit,
  } = useForm<CarTypeSchema>({
    resolver: zodResolver(carTypeSchema),
    defaultValues: {
      car_type: formData.car_type ?? undefined,
      bags: formData.bags,
    },
  });

  const carType = watch('car_type');

  const totalSeats = (formData?.children || 0) + (formData?.adults || 0);

  const {
    // isPending,
    // isError,
    // error,
    data: carTypes,
    isFetching,
    // isPlaceholderData,
    // refetch,
    // status,
  } = usePaginatedCarTypes(1, 30, totalSeats);

  const carTypesOptions = carTypes?.data
  .map((val) => {
    // Updated regex to handle optional negative numbers
    const match = val.name.match(/^\[(-?\d+)\]\s*(.*)$/);
    return {
      value: val.id,
      label: match ? match[2] : val.name,
      sortOrder: match ? parseInt(match[1], 10) : Number.MAX_SAFE_INTEGER,
    };
  })
  .sort((a, b) => a.sortOrder - b.sortOrder)
  .map(({ value, label }) => ({ value, label })) || [];

  useEffect(() => {
    const matchedCt: CarTypeSchemaType = carTypes?.data.find((c) => c.id === carType);
    if(matchedCt) setSelectedCarType(matchedCt);
  }, [carType]);

  useEffect(() => {
    if (errors.car_type) {
      toast.error('You need to select a car type in order to continue.');
    }
  }, [errors]);

  function onCarTypeSelect(d: any[], onChange?: any) {
    const carType = carTypes?.data.find((c) => c.id === d);
    setSelectedCarType(carType);
    onChange && onChange(d)
  }

  function onSelectionChange(selectedBags: ReturnBagQuantities[]) {
    setValue('bags', selectedBags);
  }

  const onSubmit: SubmitHandler<CarTypeSchema> = (data) => {
    console.log('data', data);
    bookingSession?.updateOrInitSession(currentStep + 1, data);

    // @ts-ignore
    setFormData((prev) => ({
      ...prev,
      ...data,
    }));
    gotoNextStep();
  };

  return (
      <>
        <div className="col-span-full flex flex-col justify-center @5xl:col-span-5">
          <FormSummary
              className="@7xl:me-24"
              title="Some more details about your trip"
              description="We will need some more information about your trip. Let's start with the car type."
          />
        </div>

        <div className="col-span-full flex items-center justify-center">
          <form
              id={`rhf-${step.toString()}`}
              onSubmit={handleSubmit(onSubmit)}
              className="flex-grow rounded-lg mb-16"
          >
            <>
              <div className='text-creamyWhite'>
                <Text as="span" className="rizzui-select-label block text-sm mb-1.5 font-medium text-creamyWhite">
                  Select Car Type Below
                </Text>

                <Controller
                    control={control}
                    name="car_type"
                    render={({field: {value, onChange}}) => {
                      return <SearchableSelect
                          data={carTypesOptions}
                          isFetching={isFetching}
                          value={value as string | undefined}
                          onChange={(d) => {
                            onCarTypeSelect(d, onChange)
                          }}
                      />
                    }}
                />
              </div>

              <div className="col-span-full">
                <BagSelectionComponent bags={selectedCarType?.bags as BagWithQuantity[]}
                                       initialSelectedBags={getValues('bags')}
                                       onSelectionChange={onSelectionChange}/>
              </div>
            </>
          </form>
        </div>
      </>
  );
}
