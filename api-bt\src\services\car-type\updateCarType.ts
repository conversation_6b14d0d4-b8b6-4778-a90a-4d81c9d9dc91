import { getRepository } from 'typeorm';
import { CarType } from '../../models/CarType';
import { CarTypeBag } from '../../models/CarTypeBag';
import { Bag } from '../../models/Bag';
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';
import { CarTypePrice } from '../../models/CarTypePrice';

export const updateCarType = async (
    id: string,
    data: Partial<CarType> & { bags?: { bag: string; quantity: number }[], transfer_prices?: { price_per_km: number; name: string; range_max_distance: number; fixed_charge_value: number }[] }
) => {
    const carTypeRepository = getRepository(CarType);
    const carTypeBagRepository = getRepository(CarTypeBag);
    const bagRepository = getRepository(Bag);
    const carTypePriceRepository = getRepository(CarTypePrice);

    try {
        // Step 1: Find the CarType by ID
        const carType = await carTypeRepository.findOne(id, { relations: ['bags', 'transfer_prices'] });
        if (!carType) {
            return { success: false, data: null, error: 'CarType not found' };
        }

        // Step 2: Merge and update the CarType data (excluding bags and transfer_prices for now)
        const ctData = { ...data };
        delete ctData.bags;
        delete ctData.transfer_prices;
        carTypeRepository.merge(carType, ctData);
        const updatedCarType = await carTypeRepository.save(carType);

        // Step 3: Remove all existing CarTypeBags and replace with new ones
        if (data.bags) {
            // Delete all existing CarTypeBag associations
            await carTypeBagRepository.delete({ carType: carType });

            // Add the new CarTypeBags
            for (const bagData of data.bags) {
                const bag = await bagRepository.findOne({ where: { id: bagData.bag.id } });
                if (bag) {
                    const newCarTypeBag = carTypeBagRepository.create({
                        carType: updatedCarType,
                        bag: bag,
                        quantity: bagData.quantity,
                    });
                    await carTypeBagRepository.save(newCarTypeBag);
                } else {
                    throw new Error(`Bag with ID ${bagData.bag} not found`);
                }
            }
        }

        // Step 4: Remove all existing CarTypePrices and replace with new ones
        if (data.transfer_prices) {
            // Delete all existing CarTypePrice associations
            await carTypePriceRepository.delete({ car_type: carType });

            // Add the new CarTypePrices
            for (const transferPricesData of data.transfer_prices) {
                const newCarTypePrice = carTypePriceRepository.create({
                    car_type: carType,
                    price_per_km: transferPricesData.price_per_km,
                    name: transferPricesData.name,
                    range_max_distance: transferPricesData.range_max_distance,
                    fixed_charge_value: transferPricesData.fixed_charge_value
                });
                await carTypePriceRepository.save(newCarTypePrice);
            }
        }

        return { success: true, data: updatedCarType, error: null };
    } catch (error) {
        const interpretedError = interpretDatabaseError(error);
        return { success: false, data: null, error: interpretedError };
    }
};
