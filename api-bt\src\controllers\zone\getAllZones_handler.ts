import { Request, Response } from 'express';
import {
  getAllZones,
} from '../../services/zone';

export const getAllZones_handler = async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string, 10) || 1;
    const limit = parseInt(req.query.limit as string, 10) || 10;
    const paginatedResult = await getAllZones(page, limit);
    return res.json(paginatedResult);
  } catch (err: any) {
    return res.status(400).json({ error: err.message });
  }
};
