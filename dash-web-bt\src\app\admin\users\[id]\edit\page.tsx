'use client';

import { routes } from '@/config/routes';
import PageHeader from '@/app/shared/lib/page-header';
import CreateEdit from "@/app/shared/app/users/create-edit";
import {useGetUser} from "@/services/query/user/useGetUser";

const pageHeader = {
  title: 'Edit User',
  breadcrumb: [
    {
      href: routes.dashboard,
      name: 'Dashboard',
    },
    {
      href: routes.users,
      name: 'Users',
    },
    {
      name: 'Edit User',
    },
  ],
};

export default function Edit({
  params,
}: {
  params: { id: string };
}) {
  const id = params.id;

  const {
    // isPending,
    // isError,
    // error,
    data,
    // isFetching,
    // isPlaceholderData,
    // refetch,
    // status,
  } = useGetUser(id);

  return (
    <>
      <PageHeader title={pageHeader.title} breadcrumb={pageHeader.breadcrumb}>
        {/*<ImportButton title={'Import File'} />*/}
      </PageHeader>

      {
        data?.data ? <CreateEdit id={id} user={data?.data} /> : null
      }
    </>
  );
}
