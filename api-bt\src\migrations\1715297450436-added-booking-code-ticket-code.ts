import {MigrationInterface, QueryRunner, TableColumn} from "typeorm";

export class addedBookingCodeTicketCode1715297450436 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        const ticketTable = await queryRunner.getTable('ticket');
        if (ticketTable) {
            if (!ticketTable.columns.find(column => column.name === 'disabled')) {
                await queryRunner.addColumn('ticket', new TableColumn({
                    name: 'booking_code',
                    type: 'text',
                    isNullable: true,
                }));
            }
        }

        const bookingTable = await queryRunner.getTable('booking');
        if (bookingTable) {
            if (!bookingTable.columns.find(column => column.name === 'disabled')) {
                await queryRunner.addColumn('booking', new TableColumn({
                    name: 'booking_code',
                    type: 'text',
                    isNullable: true,
                }));
            }
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropColumn('ticket', 'booking_code');
        await queryRunner.dropColumn('booking', 'booking_code');
    }

}
