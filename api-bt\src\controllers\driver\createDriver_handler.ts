import { Request, Response } from 'express';
import { createDriver } from '../../services/driver';

export const createDriver_handler = async (req: Request, res: Response) => {
  try {
    const data = req.body;
    const result = await createDriver(data);
    if (!result.success) {
      return res.status(404).json(result);
    }
    return res.json(result);
  } catch (error: any) {
    return res.status(500).json({
      success: false,
      message: 'Failed to create driver',
      errorType: 'InternalServerError',
      data: undefined,
    });
  }
};
