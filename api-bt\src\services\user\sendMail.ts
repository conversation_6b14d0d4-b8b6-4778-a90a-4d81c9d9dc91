import Mailjet from 'node-mailjet';

const mailjet = new Mailjet({
    apiKey: process.env.MJ_APIKEY_PUBLIC,
    apiSecret: process.env.MJ_APIKEY_PRIVATE,
});

export async function sendMail(email: string, firstName: string, password?: string) {
    try {
        const subject = 'Your Account Credentials';
        const textPart = password 
            ? `Dear ${firstName},\n\nYour account has been created. Your password is: ${password}\n\nBest regards,\nYour Service`
            : `Dear ${firstName},\n\nYour account email has been updated.\n\nBest regards,\nYour Service`;

        await mailjet
            .post("send", { 'version': 'v3.1' })
            .request({
                "Messages": [
                    {
                        "From": {
                            "Email": "<EMAIL>",
                            "Name": "click4Transfer"
                        },
                        "To": [{ "Email": email, "Name": firstName }],
                        "Subject": subject,
                        "TextPart": textPart,
                        "HTMLPart": `<h3>Dear ${firstName},</h3><br />${textPart.replace(/\n/g, '<br />')}`,
                        "CustomID": "AccountCredentials"
                    }
                ]
            });
    } catch (err: any) {
        console.log(err.statusCode);
    }
}