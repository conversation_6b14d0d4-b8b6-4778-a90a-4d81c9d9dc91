'use client';

// import React, {useCallback, useEffect, useState} from 'react';
import React, {useEffect, useState} from 'react';
import {useAtom} from 'jotai';
import {zodResolver} from '@hookform/resolvers/zod';
// import {Controller, SubmitHandler, useForm, UseFormRegister, UseFormSetValue} from 'react-hook-form';
import {Controller, SubmitHandler, useForm} from 'react-hook-form';
import FormSummary from '@/app/shared/app/bookings/create-multi-step/form-summary';
import {
    formDataAtom,
    useStepperOne,
} from '@/app/shared/app/bookings/create-multi-step';
import {
    PaymentDetailsSchema,
    paymentDetailsSchema,
} from './form/multistep-form.schema';
// import {AdvancedRadio, Input, Loader, Radio, RadioGroup, Select, Text} from "rizzui";
import {AdvancedRadio, Input, Loader, RadioGroup, Select} from "rizzui";
import FormGroup from "@/app/shared/lib/form-group";
import {Pi<PERSON>heckCircleFill} from "react-icons/pi";
import NoSSR from "@/components/lib/no-ssr";
import {usePaginatedPaymentTypes} from "@/services/query/payment-type/public/usePaginatedPaymentTypes";
import {carTypeService} from "@/services/api/public";
import {useBookingSession} from "@/hooks/use-booking-ls";
import calculatePrice from "@/app/shared/app/bookings/create-multi-step/compute-trip-price";
import {useGetGlobalSettings} from "@/services/query/setting/public/useGetGlobalSettings";
import {PaymentTypeSchemaType} from "@/utils/validators/app/entities";

// type TipComponentTypes = {
//     register: UseFormRegister<PaymentDetailsSchema>,
//     setValue: UseFormSetValue<PaymentDetailsSchema>,
//     errors: Record<any, any>,
//     final_amount: number,
//     label?: string,
//     disabled?: boolean,
// }

// function TipComponent({setValue, register, errors, final_amount, label, disabled}: TipComponentTypes) {
//     const [selectedTipPercentage, setSelectedTipPercentage] = useState<number | null>(null);
//
//     useEffect(() => {
//         const percentage = selectedTipPercentage || 0;
//         const tipValue = Math.ceil(percentage * 0.01 * final_amount)
//         setValue('tip', tipValue);
//     }, [selectedTipPercentage]);
//     const selectTipPercentage = (ev: any) => {
//         setSelectedTipPercentage(ev.target.value)
//     }
//
//     const labelText = label || `Buy me a coffee`;
//
//     return (
//         <div className='col-span-1 mt-4'>
//             <Text as="span"
//                   className="block text-sm mb-1.5 font-medium text-creamyWhite">
//                 { labelText }
//             </Text>
//
//             <div className="grid grid-cols-4 md:grid-cols-4 gap-4 items-center">
//                 <Radio disabled={disabled} checked={selectedTipPercentage === 5} value={5} onChange={selectTipPercentage} className="col-span-1 md:col-span-1 p-2" label="5%" size="sm"/>
//                 <Radio disabled={disabled} checked={selectedTipPercentage === 15} value={15} onChange={selectTipPercentage} className="col-span-1 md:col-span-1 p-2" label="10%" size="sm"/>
//                 <Radio disabled={disabled} checked={selectedTipPercentage === 20} value={20} onChange={selectTipPercentage} className="col-span-1 md:col-span-1 p-2" label="15%" size="sm"/>
//                 <Input
//                     disabled={disabled}
//                     step={0.01}
//                     label=""
//                     placeholder="ie: 1.25"
//                     type={'number'}
//                     className="col-span-1 md:col-span-1 bg-gray-200 p-2 bg-transparent "
//                     inputClassName="!border-none !border-b-2-chillGold border-r-0"
//                     labelClassName="font-medium text-creamyWhite"
//                     {...register('tip', {valueAsNumber: true})}
//                     error={errors.tip?.message as string}
//                 />
//             </div>
//         </div>
//     )
// }

export default function StepSixPaymentDetails() {
    const [cashPaymentObject, setCashPaymentObject] = useState<PaymentDetailsSchema & { id: string ,} | null>(null);
    const [cardPaymentObject, setCardPaymentObject] = useState<PaymentDetailsSchema & { id: string ,} | null>(null);
    const [globalPartialPaymentDeductionSetting, setGlobalPartialPaymentDeductionSetting] = useState<number | null>(null);
    const {step, gotoNextStep} = useStepperOne();
    const [formData, setFormData] = useAtom(formDataAtom);
    const currentStep = 6;
    const bookingSession = useBookingSession();
    const {data: publicGlobalSettings} = useGetGlobalSettings('public');

    const {
        handleSubmit,
        register,
        formState: {errors},
        control,
        setValue,
        // getValues,
        watch,
    } = useForm<PaymentDetailsSchema>({
        resolver: zodResolver(paymentDetailsSchema),
        defaultValues: {
            partial_payment: formData.partial_payment ?? undefined,
            payment_type: formData.payment_type ?? undefined,
            final_amount_to_pay: formData.final_amount_to_pay ?? undefined,
            // tip: formData.tip,
        },
    });

    const finalAmount = watch('final_amount_to_pay');
    const paymentType = watch('payment_type');

    const {
        // isPending,
        // isError,
        // error,
        data: paymentTypes,
        isFetching: paymentTypesLoading,
        // isPlaceholderData,
        // refetch,
        // status,
    } = usePaginatedPaymentTypes(1, 30);

    const [customerPaymentValue, setCustomerPaymentValue] = useState(0);

    useEffect(() => {
        computeRecommendedPrice();
    }, []);

    useEffect(() => {
        if(publicGlobalSettings && publicGlobalSettings.data) {
            const partial_payment_deduction = publicGlobalSettings.data.find((pgs: any) => pgs.name === 'partial_payment_deduction');
            if(partial_payment_deduction) {
                setGlobalPartialPaymentDeductionSetting(+partial_payment_deduction.value / 100)
                const val: number = (Number(finalAmount) * (+partial_payment_deduction.value / 100) || 0.25);
                setCustomerPaymentValue(val);
                computeRecommendedPrice();
            }
        }
    }, [finalAmount]);

    useEffect(() => {
        if(paymentTypes) {
            const cash = paymentTypes.data.find(pt => {
                return pt.name === 'Cash';
            })
            const card = paymentTypes.data.find(pt => {
                return pt.name === 'Card';
            })
            setCashPaymentObject(cash);
            setCardPaymentObject(card);
        }
    }, [paymentTypes]);

    // function findBestMatch(prices: any[], distance: number) {
    //     let bestMatch = null;
    //
    //     for (const price of prices) {
    //         // Convert price_per_km to a number, assuming it's stored as a string.
    //         price.price_per_km = Number(price.price_per_km);
    //         price.max_distance = Number(price.max_distance);
    //         price.min_distance = Number(price.min_distance);
    //
    //         if (price.min_distance <= distance && price.max_distance >= distance) {
    //             // If exactly matches the criteria, return immediately.
    //             return price;
    //         } else if (distance > price.max_distance) {
    //             // If the distance is greater than max_distance, look for the option with the highest max_distance.
    //             if (!bestMatch || price.max_distance > bestMatch.max_distance) {
    //                 bestMatch = price;
    //             }
    //         }
    //     }
    //
    //     // Return the best match found, or null if no suitable match was found.
    //     return bestMatch;
    // }

    // console.log('formData', formData)

    function computeRecommendedPrice() {
        if (formData.car_type && typeof formData.car_type === 'string') {
            try {
                carTypeService.getCarType(formData.car_type).then((data) => {
                    // # Compute prices based on transfer_prices, if they exist.
                    if (formData.trip_type === 'transfer' && data.data?.default_price_per_km && formData.trip_distance) {
                        // compute final amount to pay and final amount to pay based on trip distance and return trip distance
                        let final_amount_to_pay = calculatePrice({
                            ranges: data.data?.use_default_ppk ? [] : (data.data.transfer_prices && data.data.transfer_prices.length > 0 ? data.data.transfer_prices : []),
                            distance: formData.trip_distance,
                            defaultPricePerKm: data.data.default_price_per_km,
                        }).price.toFixed(2);

                        // console.log('-----------', final_amount_to_pay, {
                        //     ranges: data.data?.use_default_ppk ? [] : (data.data.transfer_prices && data.data.transfer_prices.length > 0 ? data.data.transfer_prices : []),
                        //     distance: formData.trip_distance,
                        //     defaultPricePerKm: data.data.default_price_per_km,
                        // })

                        if(final_amount_to_pay && !formData.one_way_trip) {
                            setValue('final_amount_to_pay', +final_amount_to_pay * 2);
                            setValue('recommended_amount_to_pay', +final_amount_to_pay * 2);
                        } else {
                            setValue('final_amount_to_pay', +final_amount_to_pay);
                            setValue('recommended_amount_to_pay', +final_amount_to_pay);
                        }

                        return;
                    }

                    // # Compute prices based on hourly value, if hourly was selected and estimated_service_hours is present.
                    if (formData.trip_type === 'hourly' && data.data.hourly_rate && formData.estimated_service_hours) {
                        // compute final amount to pay and final amount to pay based on estimated_service_hours times hourly rate
                        const final_amount_to_pay = (formData.estimated_service_hours * data.data.hourly_rate).toFixed(2);

                        setValue('final_amount_to_pay', +final_amount_to_pay);
                        setValue('recommended_amount_to_pay', +final_amount_to_pay);
                        return;
                    }
                })
            } catch (e) {
                console.error(e);
            }
        }
    }
    const onSubmit: SubmitHandler<PaymentDetailsSchema> = (data) => {
        setFormData((prev) => ({
            ...prev,
            ...data,
        }));
        // console.log('formData #=> ', formData)
        bookingSession?.updateOrInitSession(currentStep + 1, data);

        gotoNextStep();
    };

    // const computeFullFinalAmount = () => {
    //     const tip = getValues('tip') || 0;
    //     return finalAmount + tip;
    // }

    if(paymentTypes?.data.find((pt: PaymentTypeSchemaType) => pt.id === paymentType)?.name === 'Cash') {
        setValue('partial_payment', 'partial')
    }

    return (
        <>
            <div className="col-span-full flex flex-col justify-center @5xl:col-span-5">
                <FormSummary
                    title="Payment Details"
                    description="We will need some more information about the payment. Let's start with the payment type."
                />
            </div>
            <div className="col-span-full items-center justify-center text-creamyWhite">
                <form
                    id={`rhf-${step.toString()}`}
                    onSubmit={handleSubmit(onSubmit)}
                    className="flex-grow rounded-lg mb-16 text-creamyWhite">
                    <FormGroup
                        title="Who are you traveling with ?"
                        description="It helps us to provide you with the best service."
                        headingColor='text-creamyWhite'
                    >
                        <Input
                            step={0.01}
                            label="Final Amount to Pay (€)"
                            placeholder="ie: 1.25"
                            disabled={true}
                            type={'number'}
                            labelClassName="font-medium text-creamyWhite"
                            {...register('final_amount_to_pay', {valueAsNumber: true})}
                            error={errors.final_amount_to_pay?.message as string}
                        />

                        {
                            !paymentTypesLoading && (paymentTypes && paymentTypes?.data.length) ? (
                                <NoSSR>
                                    <Controller
                                        control={control}
                                        name="payment_type"
                                        render={({field: {value, onChange}}) => (
                                            <Select
                                                label="Select Payment Type Below"
                                                placeholder={''}
                                                // className="text-white"
                                                selectClassName="text-white"
                                                labelClassName="text-creamyWhite"
                                                dropdownClassName="p-2 gap-1 grid !z-10"
                                                inPortal={false}
                                                value={value}
                                                onChange={(selected) => {
                                                    localStorage.setItem('payment_type', paymentTypes?.data?.find((pt) => pt.id === selected)?.name ?? '');
                                                    onChange(selected);
                                                }}
                                                options={paymentTypes.data.sort((a, b) => {
                                                    if (a.name === 'Cash' && b.name !== 'Cash') return -1;
                                                    if (a.name === 'Card' && b.name !== 'Card' && b.name !== 'Cash') return -1;
                                                    if (b.name === 'Cash' && a.name !== 'Cash') return 1;
                                                    if (b.name === 'Card' && a.name !== 'Card' && a.name !== 'Cash') return 1;
                                                    return 0;
                                                }).map((paymentType) => ({
                                                    value: paymentType.id,
                                                    label: paymentType.name,
                                                }))}
                                                getOptionValue={(option) => option.value}
                                                displayValue={(selected: string) =>
                                                    paymentTypes?.data.find((c) => c.id === selected)?.name ?? ''
                                                }
                                                error={errors?.payment_type?.message as string}
                                            />
                                        )}
                                    />
                                </NoSSR>
                            ) : <div className="grid h-32 flex-grow place-content-center items-center">
                                <Loader size="lg"/>
                            </div>
                        }

                        {
                            paymentType && (paymentType === cardPaymentObject?.id || paymentType === cashPaymentObject?.id) ? (
                                <>
                                    <Controller
                                        name="partial_payment"
                                        control={control}
                                        render={({field: {value, onChange}}) => (
                                            <RadioGroup
                                                value={value}
                                                setValue={(d) => {
                                                    onChange(d);
                                                }}
                                                className="col-span-full grid gap-4 @lg:grid-cols-2"
                                            >
                                                {
                                                    cardPaymentObject?.id === paymentType ? (
                                                            <AdvancedRadio
                                                                value="full"
                                                                className=" [&_.rizzui-advanced-checkbox]:!px-5 [&_.rizzui-advanced-checkbox]:!py-4 text-creamyWhite"
                                                                inputClassName="[&~span]:border-0 [&~span]:ring-1 [&~span]:ring-gray-200 [&~span:hover]:ring-primary [&:checked~span:hover]:ring-primary [&:checked~span]:border-1 [&:checked~.rizzui-advanced-checkbox]:ring-2 [&~span_.icon]:opacity-0 [&:checked~span_.icon]:opacity-100"
                                                            >
                                                                <div className="flex justify-between">
                                                                    <span className="font-medium text-creamyWhite">Full payment</span>
                                                                    <PiCheckCircleFill className="icon h-5 w-5 text-primary"/>
                                                                </div>
                                                                <p className="text-creamyLightBrown">I pay in full now
                                                                    (€{finalAmount.toFixed(2)}).</p>
                                                            </AdvancedRadio>
                                                        ) : null
                                                }
                                                <AdvancedRadio
                                                    value="partial"
                                                    className=" [&_.rizzui-advanced-checkbox]:!px-5 [&_.rizzui-advanced-checkbox]:!py-4 text-creamyWhite"
                                                    inputClassName="[&~span]:border-0 [&~span]:ring-1 [&~span]:ring-gray-200 [&~span:hover]:ring-primary [&:checked~span:hover]:ring-primary [&:checked~span]:border-1 [&:checked~.rizzui-advanced-checkbox]:ring-2 [&~span_.icon]:opacity-0 [&:checked~span_.icon]:opacity-100"
                                                >
                                                    <div className="flex justify-between">
                                                        <span className="font-medium text-creamyWhite">Partial Payment</span>
                                                        <PiCheckCircleFill className="icon h-5 w-5 text-primary"/>
                                                    </div>
                                                    <p className="text-creamyLightBrown">Pay {globalPartialPaymentDeductionSetting ? globalPartialPaymentDeductionSetting * 100 : 25}% now (€{customerPaymentValue.toFixed(2)}). { cashPaymentObject?.id === paymentType && 'For cash payments, a mandatory partial payment is required to be paid via Card.' }</p>
                                                </AdvancedRadio>
                                            </RadioGroup>
                                        )}
                                    />
                                    {/*{*/}
                                    {/*    paymentTypes?.data.find((pt: any) => (pt.name === 'Card') || pt.name === 'Cash') ?*/}
                                    {/*        <TipComponent disabled={false} setValue={setValue} register={register} errors={errors} final_amount={finalAmount} /> :*/}
                                    {/*        <TipComponent disabled={true} setValue={setValue} register={register} errors={errors} final_amount={finalAmount} />*/}
                                    {/*}*/}
                                </>
                            ) : null
                        }

                    </FormGroup>
                </form>
            </div>
        </>
    );
}
