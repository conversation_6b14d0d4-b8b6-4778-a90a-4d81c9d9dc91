'use client';

import { routes } from '@/config/routes';
import PageHeader from '@/app/shared/lib/page-header';
import CreateEdit from '@/app/shared/app/cars/create-edit';
import {useGetCar} from "@/services/query/car/useGetCar";

const pageHeader = {
  title: 'Edit Car',
  breadcrumb: [
    {
      href: routes.dashboard,
      name: 'Dashboard',
    },
    {
      href: routes.cars,
      name: 'Cars',
    },
    {
      name: 'Edit Car',
    },
  ],
};

export default function Edit({
  params,
}: {
  params: { id: string };
}) {
  const id = params.id;

  const {
    // isPending,
    // isError,
    // error,
    data: car,
    // isFetching,
    // isPlaceholderData,
    // refetch,
    // status,
  } = useGetCar(id);

  return (
    <>
      <PageHeader title={pageHeader.title} breadcrumb={pageHeader.breadcrumb}>
        {/*<ImportButton title={'Import File'} />*/}
      </PageHeader>

      {
        car?.data ? <CreateEdit id={params.id} car={car.data} /> : null
      }
    </>
  );
}
