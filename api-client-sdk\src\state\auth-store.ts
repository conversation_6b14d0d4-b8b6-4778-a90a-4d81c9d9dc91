import { types, flow, Instance, SnapshotIn } from 'mobx-state-tree';
import { authService } from '../services/auth-service';
import { LoginCredentials } from '../models/auth';
import { User, UserModel } from './models/user-model';

/**
 * Auth store model for managing authentication state
 */
export const AuthStore = types
  .model('AuthStore', {
    isAuthenticated: types.optional(types.boolean, false),
    token: types.maybe(types.string),
    currentUser: types.maybe(UserModel),
    error: types.maybe(types.string),
    isLoading: types.optional(types.boolean, false),
  })
  .views((self) => ({
    /**
     * Check if user is an admin
     */
    get isAdmin(): boolean {
      return self.currentUser?.role?.id === 1;
    },
    
    /**
     * Check if user is a driver
     */
    get isDriver(): boolean {
      return !!self.currentUser?.driver;
    },
  }))
  .actions((self) => {
    /**
     * Set the loading state
     */
    const setLoading = (loading: boolean) => {
      self.isLoading = loading;
    };

    /**
     * Set error message
     */
    const setError = (error: string | null) => {
      self.error = error || undefined;
    };

    /**
     * Clear the auth state
     */
    const clearAuth = () => {
      self.isAuthenticated = false;
      self.token = undefined;
      self.currentUser = undefined;
      self.error = undefined;
    };

    /**
     * Set the auth state
     */
    const setAuth = (token: string, user: User) => {
      self.isAuthenticated = true;
      self.token = token;
      self.currentUser = user;
      self.error = undefined;
    };

    /**
     * Login as a user (flow for async)
     */
    const login = flow(function* (credentials: LoginCredentials) {
      setLoading(true);
      setError(null);
      
      try {
        const response = yield authService.login(credentials);
        
        if (response.success && response.token && response.user) {
          setAuth(response.token, response.user);
          return { success: true };
        } else {
          setError(response.msg || 'Login failed');
          return { success: false, error: response.msg };
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Login failed';
        setError(errorMessage);
        return { success: false, error: errorMessage };
      } finally {
        setLoading(false);
      }
    });

    /**
     * Login as an admin (flow for async)
     */
    const loginAdmin = flow(function* (credentials: LoginCredentials) {
      setLoading(true);
      setError(null);
      
      try {
        const response = yield authService.loginAdmin(credentials);
        
        if (response.success && response.token && response.user) {
          setAuth(response.token, response.user);
          return { success: true };
        } else {
          setError(response.msg || 'Admin login failed');
          return { success: false, error: response.msg };
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Admin login failed';
        setError(errorMessage);
        return { success: false, error: errorMessage };
      } finally {
        setLoading(false);
      }
    });

    /**
     * Login as a driver (flow for async)
     */
    const loginDriver = flow(function* (credentials: LoginCredentials) {
      setLoading(true);
      setError(null);
      
      try {
        const response = yield authService.loginDriver(credentials);
        
        if (response.success && response.token && response.user) {
          setAuth(response.token, response.user);
          return { success: true };
        } else {
          setError(response.msg || 'Driver login failed');
          return { success: false, error: response.msg };
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Driver login failed';
        setError(errorMessage);
        return { success: false, error: errorMessage };
      } finally {
        setLoading(false);
      }
    });

    /**
     * Logout (flow for async)
     */
    const logout = flow(function* () {
      setLoading(true);
      
      try {
        yield authService.logout();
        clearAuth();
        return { success: true };
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Logout failed';
        setError(errorMessage);
        return { success: false, error: errorMessage };
      } finally {
        setLoading(false);
      }
    });

    return {
      setLoading,
      setError,
      clearAuth,
      setAuth,
      login,
      loginAdmin,
      loginDriver,
      logout,
    };
  });

// Type definitions
export interface IAuthStore extends Instance<typeof AuthStore> {}
export interface IAuthStoreSnapshotIn extends SnapshotIn<typeof AuthStore> {}

// Create and export a default instance of the auth store
export const createAuthStore = () => AuthStore.create({
  isAuthenticated: false,
  isLoading: false,
}); 