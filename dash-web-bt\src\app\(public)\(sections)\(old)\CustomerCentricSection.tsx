import React from 'react';
import Container from '@/app/(public)/(sections)/(old)/container';

const CustomerCentricSection: React.FC = () => {
    return (
        <section className="py-16 text-white">
            <Container>
                <div className="flex flex-col md:flex-row items-center">
                    {/* Image with effects */}
                    <div className="md:w-1/2 lg:w-2/5 mb-8 md:mb-0 flex justify-center relative overflow-hidden rounded-2xl shadow-lg">
                        <img src={'/banners/main.png'} alt="Customer Centric Image" className="w-full h-full object-cover filter grayscale opacity-80 hover:opacity-100 transition-opacity duration-300 transform hover:scale-105" />
                        {/* Optional overlay for additional effect */}
                        {/* <div className="absolute inset-0 bg-black opacity-50"></div> */}
                    </div>
                    {/* Text content */}
                    <div className="md:w-1/2 lg:w-3/5 text-center md:text-left md:ml-8">
                        <h2 className="text-3xl font-semibold mb-6 text-chillGold">
                            El cliente es el centro de nuestro universo
                        </h2>
                        <p className="mb-4">
                            Entendemos que cada cliente es único. Por eso, ofrecemos soluciones de transporte personalizadas que se adaptan a sus necesidades específicas, garantizando un servicio que supera las expectativas.
                        </p>
                        <p className="mb-4">
                            Con un enfoque en la satisfacción del cliente, nuestro equipo está siempre listo para proporcionar un servicio excepcional, asegurando que su experiencia sea memorable.
                        </p>
                    </div>
                </div>
            </Container>
        </section>
    );
};

export default CustomerCentricSection;
