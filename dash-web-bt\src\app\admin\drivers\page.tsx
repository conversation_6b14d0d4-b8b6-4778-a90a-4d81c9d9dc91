'use client';

import {usePaginatedDrivers} from "@/services/query/driver/usePaginatedDrivers";
import Header from "./page-header";
import React, {useState} from "react";
import Table from "./table";
import {Loader} from "rizzui";

export default function Page() {
    const [currentPage, setCurrentPage] = useState(1);
    const [currentLimit, setCurrentLimit] = useState(10);

    const {
        isPending,
        isError,
        error,
        data,
        isFetching,
        isPlaceholderData,
        refetch,
        status,
    } = usePaginatedDrivers(currentPage, currentLimit);

    const onSelectedPage = (page: number) => {
        setCurrentPage(page);
    }

    const onPageLimitChange = (limit: number) => {
        setCurrentLimit(limit);
    }

    const onFiltersChanged = (filters: any) => {
        refetch();
    }

    return <>
        <Header />

        <div className="flex flex-col gap-10 @container">
            {
                data ?
                    <Table
                        initialData={data}
                        onSelectedPage={onSelectedPage}
                        onPageLimitChange={onPageLimitChange}
                        variant="elegant"
                        className="[&_.table-filter]:hidden [&_.table-pagination]:hidden"
                    /> : <div className="grid h-32 flex-grow place-content-center items-center">
                        <Loader size="lg"/>
                    </div>
            }
        </div>
    </>;
}
