import { getRepository } from 'typeorm';
import { Zone } from '../../models/Zone'; // Adjust the path as necessary
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const getZone = async (id: string) => {
  const zoneRepository = getRepository(Zone);
  try {
    const zone = await zoneRepository.findOne(id);
    if (!zone) {
      return { success: false, data: null, error: 'Zone not found' };
    }
    return { success: true, data: zone, error: null };
  } catch (error) {
    const interpretedError = interpretDatabaseError(error);
    return { success: false, data: null, error: interpretedError };
  }
};
