'use client';

import React, {useEffect, useState, ChangeEventHandler} from 'react';
import {useAtom} from 'jotai';
import {toast} from 'react-hot-toast';
import {zodResolver} from '@hookform/resolvers/zod';
import {Controller, SubmitHandler, useForm} from 'react-hook-form';
import {AdvancedRadio, Checkbox, RadioGroup, Select, Text} from 'rizzui';
import RocketFlamingIcon from '@/components/lib/icons/rocket-flaming';
import CarParkingIcon from '@/components/lib/icons/car-parking';
import FormSummary from '@/app/shared/app/bookings/create-multi-step/form-summary';
import {
    formDataAtom,
    useStepperOne,
} from '@/app/shared/app/bookings/create-multi-step';
import {
    JourneyDetailsSchema,
    journeyDetailsSchema,
} from './form/multistep-form.schema';
import NoSSR from "@/components/lib/no-ssr";
import {PiCar} from "react-icons/pi";
import Autocomplete from "@/components/lib/google-map/autocomplete";
import RouteInfoCard from './sections/RouteInfoCard';
import {useSearchParams} from "next/navigation";
import NoSsr from "@/components/lib/no-ssr";
import {useBookingSession} from "@/hooks/use-booking-ls";

import {useGetGlobalSettings} from "@/services/query/setting/public/useGetGlobalSettings";
import PickupReturnDate, { calculateDateLimits } from './sections/PickupReturnDate';
import useScrollLockOnHover from '@/hooks/use-scroll-lock-on-hover';

const tripTypes: { name: string; label: string; icon: React.ReactNode }[] = [
    {name: 'transfer', label: 'Transfer', icon: <RocketFlamingIcon/>},
    {name: 'hourly', label: 'Hourly', icon: <CarParkingIcon/>},
];

export default function StepTwo() {
    const {step, gotoNextStep} = useStepperOne();
    const [formData, setFormData] = useAtom(formDataAtom);

    const [isTwoWayTrip, setIsTwoWayTrip] = useState(() => false);
    const [destinationLatLng, setDestinationLatLng] = useState(() => [0, 0]);
    const searchParams = useSearchParams()

    const pickup_location = searchParams.get('pickup_location')

    const [pickupLatLng, setPickupLatLng] = useState<number[] | null>(() => {
        if (pickup_location) {
            // console.log('+pickup_location[0], +pickup_location[1] @@@ ', +pickup_location[0], +pickup_location[1], pickup_location)
            const [_add, lat, long] = pickup_location?.split('|');
            return [+lat, +long]
        }
        return null
    });

    const {data: publicGlobalSettings} = useGetGlobalSettings('public');

    const [globalBookingSettings, setGlobalBookingSettings] = useState<{
        min: number | null,
        allowMaxTime: number | null,
        minBefore: number | null,
    }>({
        min: null,
        allowMaxTime: null,
        minBefore: null,
    });

    const currentStep = 2;

    const {
        control,
        formState: {errors},
        handleSubmit,
        watch,
        setValue,
        // register,
        getValues,
    } = useForm<JourneyDetailsSchema>({
        resolver: zodResolver(journeyDetailsSchema),
        defaultValues: {
            trip_type: formData.trip_type ?? undefined,
            destination_long: formData.destination_long ?? undefined,
            destination_lat: formData.destination_lat ?? undefined,
            destination_address: formData.destination_address ?? undefined,
            one_way_trip: formData.one_way_trip ?? undefined,
            pickup_address: formData.pickup_address ?? undefined,
            pickup_lat: formData.pickup_lat ?? undefined,
            pickup_long: formData.pickup_long ?? undefined,
            return_trip_time: formData.return_trip_time ?? undefined,
            scheduled_at: formData.scheduled_at ?? undefined,
            estimated_service_hours: formData.estimated_service_hours ?? undefined,
            return_trip_distance: formData.return_trip_distance ?? undefined,
            trip_distance: formData.trip_distance ?? undefined,
            return_trip_duration: formData.return_trip_duration ?? undefined,
            trip_duration: formData.trip_duration ?? undefined,
        },
    });

    const bookingSession = useBookingSession();
    const tripType = watch('trip_type');
    const tripDistance = watch('trip_distance');
    const tripDuration = watch('trip_duration');
    const returnTripDistance = watch('return_trip_distance');
    const returnTripDuration = watch('return_trip_duration');
    const oneWayTrip = watch('one_way_trip');
    const destinationAddress = watch('destination_address');
    const scheduledAtDate = watch('scheduled_at');

    const setAddress = (type: 'pickup' | 'destination', place: any, onChange: (val: string) => void) => {
        setValue(`${type}_address`, place.formatted_address);
        setValue(`${type}_lat`, place.geometry.location.lat().toString());
        setValue(`${type}_long`, place.geometry.location.lng().toString());

        if (type === 'pickup') {
            setPickupLatLng([place.geometry.location.lat(), place.geometry.location.lng()]);
        } else {
            // # Stupid hack to get the destination lat long
            if (pickup_location) {
                const [_address, lat, lng] = pickup_location?.split('|');
                if (lat && lng) {
                    setPickupLatLng([parseFloat(lat), parseFloat(lng)]);
                }
            }
            setDestinationLatLng([place.geometry.location.lat(), place.geometry.location.lng()]);
        }
        onChange(place.formatted_address);
    };

    function onRouteComputed() {
        const route = arguments[0];
        if(route && route.legs && route.legs.length > 0) {
            // distance in km rounded to 2 decimals
            const distance = +(route.legs[0].distance.value / 1000).toFixed(2);
            setValue('trip_distance', distance);
            setValue('return_trip_distance', distance);
            // duration in minutes rounded
            const duration = Math.round(route.legs[0].duration.value / 60);
            setValue('trip_duration', duration);
            setValue('return_trip_duration', duration);
        } else {
            console.error('Legs not found.');
            toast.error('No path found between these 2 locations. Please be reasonable in your searches !');
        }
    }

    const onSubmit: SubmitHandler<JourneyDetailsSchema> = (data) => {
        // # Custom checking
        // const settings = publicGlobalSettings?.data?.reduce((acc: any, setting: any) => {
        //     acc[setting.name] = parseInt(setting.value, 10);
        //     return acc;
        //   }, {});
        // calculateDateLimits 
        // const { minDate } = calculateDateLimits(data.scheduled_at, settings.minHoursBeforeCurrent, settings.startHour, settings.endHour);

        // const scheduledAtDate = new Date(data.scheduled_at);
        // if (scheduledAtDate && scheduledAtDate < minDate) {
        //     toast.error('Pickup date cannot be in the past.');
        //     return;
        // }

        // # - if hourly - then we require Estimated Service Hours
        if (data.trip_type === 'hourly' && (!data.estimated_service_hours || !data.pickup_address || !data.pickup_lat || !data.pickup_lat || !data.estimated_service_hours)) {
            toast.error('On an hourly plan you are required to specify an estimated number of hours you wish our drivers to assist you as well as a pickup location.');
            return;
        }

        if (data.trip_type === 'transfer' && (!data.destination_long || !data.destination_lat || !data.destination_address || !data.trip_distance)) {
            if(!data.one_way_trip && (!data.return_trip_distance || !data.return_trip_time || !data.return_trip_duration)) {
                toast.error('On transfer with a two way journey, you are required to also specify a return trip time.');
            }
            toast.error('On transfer, you are required to specify an initial destination address.');
            return;
        }

        bookingSession?.updateOrInitSession(currentStep + 1, data);

        setFormData((prev) => ({
            ...prev,
            ...data,
        }));

        gotoNextStep();
    };

    const quantitySelectorOptionsGen = (set: number = 5) => {
        const options = [];
        for (let i = 0; i <= set; i++) {
            options.push({
                value: i,
                label: `${i}`,
            })
        }
        return options
    };

    const quantitySelectorOptions = quantitySelectorOptionsGen(24);

    useEffect(() => {
        if (pickup_location) {
            const [address, lat, lng] = pickup_location?.split('|');
            if (address && lat && lng) {
                setValue('pickup_address', address);
                setValue('pickup_long', lng.toString());
                setValue('pickup_lat', lat.toString());
            }
        }
    }, [pickup_location, setValue]);

    useEffect(() => {
        if (errors.trip_type) {
            toast.error(errors.trip_type.message as string);
        }
    }, [errors]);

    useEffect(() => {
        setIsTwoWayTrip(!oneWayTrip)
    }, [oneWayTrip]);


    return (
        <>
            <div className="col-span-full flex flex-col justify-center @5xl:col-span-5">
                <FormSummary
                    className="@7xl:me-10"
                    title="Let's get started"
                    description="We will start by gathering some information about your trip. Let's start with where you would want to go - and when. Otherwise, if you want to be provided with hourly services, just select 'Hourly'."
                />
            </div>

            <div className="col-span-full items-center justify-center">
                <form
                    id={`rhf-${step.toString()}`}
                    onSubmit={handleSubmit(onSubmit)}
                    className="flex-grow rounded-lg mb-16"
                >
                    <>
                        <div className="col-span-full lg:col-span-6">
                            <Controller
                                name="trip_type"
                                control={control}
                                render={({field: {value, onChange}}) => (
                                    <RadioGroup
                                        value={value || ''} // Provide an empty string as fallback if value is null
                                        setValue={onChange}
                                        className="col-span-full grid grid-cols-2 gap-4 @3xl:grid-cols-2 @4xl:gap-6 @6xl:grid-cols-2 rounded-lg"
                                    >
                                        {tripTypes.map((tripType) => (
                                            <AdvancedRadio
                                                key={tripType.name}
                                                value={tripType.name}
                                                className=" [&_.rizzui-advanced-radio]:px-6 [&_.rizzui-advanced-radio]:py-6 text-creamyWhite"
                                                inputClassName="[&~span]:border-0 [&~span]:ring-1 [&~span]:ring-gray-200 [&~span:hover]:ring-primary [&:checked~span:hover]:ring-primary [&:checked~span]:border-primary [&:checked~.rizzui-advanced-radio]:ring-2 [&~span_.icon]:opacity-0 [&:checked~span_.icon]:opacity-100"
                                            >
                                                <span className="mb-4 block h-8 w-8 [&_svg]:w-8">
                                                  {tripType.icon}
                                                </span>
                                                <span className="font-semibold">{tripType.label}</span>
                                            </AdvancedRadio>
                                        ))}
                                    </RadioGroup>
                                )}
                            />
                        </div>

                        {
                            tripType === 'transfer' && (
                                <NoSSR>
                                    <div className="col-span-full lg:col-span-6 mt-6">
                                        <Controller
                                            name="one_way_trip"
                                            control={control}
                                            render={({field: {value, onChange}}) => (
                                                <Checkbox
                                                    checked={value}
                                                    value={value ? 'true' : 'false'}
                                                    onChange={(ev) => {
                                                        setIsTwoWayTrip(ev.currentTarget.value === 'true');
                                                        onChange(ev);
                                                    }}
                                                    label={
                                                        <span className="flex items-center gap-1 text-creamyWhite">
                                                            One-Way Trip
                                                            <PiCar className="h-6 w-6 text-creamyWhite"/>
                                                          </span>
                                                    }
                                                    size="sm"
                                                    className="-mt-2 [&_svg]:top-0 rounded-lg"
                                                />
                                            )}
                                        />
                                    </div>

                                    <div className="flex flex-col md:grid md:grid-cols-2 md:gap-4">
                                        <div className="mt-4 col-span-1">
                                            <div className="flex flex-col space-y-2">
                                                <label htmlFor="autocomplete"
                                                       className="text-sm font-medium text-creamyWhite">Pickup
                                                    Address</label>
                                                    <Controller
                                                        name="pickup_address"
                                                        control={control}
                                                        render={({field: {value, onChange}}) => (
                                                            <Autocomplete
                                                                apiKey={process.env.NEXT_PUBLIC_GOOGLE_MAP_API_KEY as string}
                                                                mapClassName="rounded-lg"
                                                                spinnerClassName="grid h-full w-full place-content-center"
                                                                className="w-full rounded-lg text-creamyWhite"
                                                                hideMap={true}
                                                                onPlaceSelect={(place) => {
                                                                    setAddress('pickup', place, onChange);
                                                                }}
                                                                defaultInputValue={value || ''}
                                                            />
                                                        )}
                                                    />
                                                <div role="alert"
                                                     className="text-red text-[13px] mt-0.5 rizzui-input-error-text">
                                                    {errors.pickup_address?.message as string}
                                                </div>
                                            </div>
                                        </div>
                                        <div className="mt-4 col-span-1">
                                            <div className="flex flex-col space-y-2">
                                                <label htmlFor="autocomplete"
                                                       className="text-sm font-medium text-creamyWhite">Destination
                                                    Address</label>
                                                    <Controller
                                                        name="destination_address"
                                                        control={control}
                                                        render={({field: {value, onChange}}) => (
                                                            <Autocomplete
                                                                apiKey={process.env.NEXT_PUBLIC_GOOGLE_MAP_API_KEY as string}
                                                                mapClassName="rounded-lg"
                                                                spinnerClassName="grid h-full w-full place-content-center"
                                                                className="w-full rounded-lg text-creamyWhite"
                                                                hideMap={true}
                                                                onPlaceSelect={(place) => {
                                                                    setAddress('destination', place, onChange);
                                                                }}
                                                                defaultInputValue={value || ''}
                                                            />
                                                        )}
                                                    />
                                                <div role="alert"
                                                     className="text-red text-[13px] mt-0.5 rizzui-input-error-text">
                                                    {errors.destination_address?.message as string}
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <RouteInfoCard
                                        distance={tripDistance || 0}
                                        duration={tripDuration || 0}
                                        address={getValues('pickup_address')}
                                        returnDistance={!oneWayTrip && returnTripDistance ? returnTripDistance : null}
                                        returnDuration={!oneWayTrip && returnTripDuration ? returnTripDuration : null}
                                        destinationAddress={destinationAddress}
                                    />

                                    {
                                        !!destinationLatLng[0] && pickupLatLng && !!pickupLatLng[0] && (
                                            <div className="mt-3">
                                                <Autocomplete
                                                    apiKey={process.env.NEXT_PUBLIC_GOOGLE_MAP_API_KEY as string}
                                                    hideInput={true}
                                                    mapClassName="rounded-lg"
                                                    spinnerClassName="grid h-full w-full place-content-center"
                                                    className="relative h-[200px] w-full flex-grow rounded-lg bg-gray-50 col-span-full"
                                                    startLocation={pickupLatLng}
                                                    endLocation={destinationLatLng}
                                                    onRouteComputed={onRouteComputed}
                                                />
                                            </div>
                                        )
                                    }
                                </NoSSR>
                            )
                        }

                        {/* <div ref={useScrollLockOnHover() as React.RefObject<HTMLDivElement>}> */}
                            <PickupReturnDate control={control} scheduledAtDate={scheduledAtDate} errors={errors} isTwoWayTrip={isTwoWayTrip} tripType={tripType} publicGlobalSettings={publicGlobalSettings} />
                        {/* </div> */}

                        {
                            tripType === 'hourly' && (
                                <div className="flex flex-col md:grid md:grid-cols-2 md:gap-4">
                                    <NoSsr>
                                        <div className="mt-4 col-span-1">
                                            <div className="flex flex-col space-y-2">
                                                <label htmlFor="autocomplete"
                                                       className="text-sm font-medium text-creamyWhite">Pickup
                                                    Address</label>
                                                    <Controller
                                                        name="pickup_address"
                                                        control={control}
                                                        render={({field: {value, onChange}}) => {
                                                            return (
                                                                <Autocomplete
                                                                    apiKey={process.env.NEXT_PUBLIC_GOOGLE_MAP_API_KEY as string}
                                                                    mapClassName="rounded-lg"
                                                                    spinnerClassName="grid h-full w-full place-content-center"
                                                                    className="rounded-lg text-creamyWhite"
                                                                    hideMap={true}
                                                                    onPlaceSelect={(place) => setAddress('pickup', place, onChange)}
                                                                    defaultInputValue={value || ''}
                                                                />
                                                            )
                                                        }}
                                                    />
                                                <div role="alert"
                                                     className="text-red text-[13px] mt-0.5 rizzui-input-error-text">
                                                    {errors.pickup_address?.message as string}
                                                </div>
                                            </div>
                                        </div>
                                        {
                                            pickupLatLng ? (
                                                <div className="flex flex-col md:grid md:grid-cols-2 col-span-full">
                                                    <div className="mt-3 col-span-1">
                                                        <Autocomplete
                                                            apiKey={process.env.NEXT_PUBLIC_GOOGLE_MAP_API_KEY as string}
                                                            hideInput={true}
                                                            mapClassName="rounded-lg"
                                                            spinnerClassName="grid h-full w-full place-content-center"
                                                            className="relative h-[200px] w-full flex-grow rounded-lg bg-gray-50 col-span-full"
                                                            startLocation={pickupLatLng}
                                                            onRouteComputed={onRouteComputed}
                                                        />
                                                    </div>
                                                </div>
                                            ) : null
                                        }
                                    </NoSsr>
                                    <div className="mt-4 col-span-1">
                                        <Controller
                                            control={control}
                                            name="estimated_service_hours"
                                            render={({field: {value, onChange}}) => {
                                                return (
                                                    <Select
                                                        label="Estimated Service Hours"
                                                        className="text-creamyWhite"
                                                        labelClassName="text-creamyWhite"
                                                        dropdownClassName="p-2 gap-1 grid !z-10"
                                                        inPortal={false}
                                                        value={value}
                                                        onChange={onChange}
                                                        options={quantitySelectorOptions}
                                                        getOptionValue={(option) => option.value}
                                                        displayValue={(selected: number) => {
                                                            return quantitySelectorOptions.find((c) => c.value === selected)?.label ?? ''
                                                        }
                                                        }
                                                        error={errors?.estimated_service_hours?.message as string}
                                                    />
                                                )
                                            }}
                                        />
                                    </div>
                                </div>
                            )
                        }
                    </>
                </form>
            </div>
        </>
    );
}
