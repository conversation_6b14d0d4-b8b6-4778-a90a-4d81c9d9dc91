import express from 'express';
import {
  createZone_handler,
  deleteZone_handler,
  getAll<PERSON>ones_handler,
  getZone_handler,
  updateZone_handler,
} from '../../controllers/zone';

export const zoneRouter = express.Router();

zoneRouter.post('/zones', createZone_handler);
zoneRouter.get('/zones', getAllZones_handler);
zoneRouter.get('/zones/:id', getZone_handler);
zoneRouter.put('/zones/:id', updateZone_handler);
zoneRouter.delete('/zones/:id', deleteZone_handler);
