import { useCallback } from 'react';
import { useStore } from './provider';
import { LoginCredentials } from '../models/auth';
import { User, UserCreate, UserUpdate } from '../models/user';
import { PaginationParams } from '../utils/pagination';

/**
 * Hook for accessing auth-related functionality
 * @returns Auth actions and state
 */
export function useAuth() {
  const { authStore } = useStore();
  
  // Login function
  const login = useCallback(async (credentials: LoginCredentials) => {
    return await authStore.login(credentials);
  }, [authStore]);
  
  // Admin login function
  const loginAdmin = useCallback(async (credentials: LoginCredentials) => {
    return await authStore.loginAdmin(credentials);
  }, [authStore]);
  
  // Driver login function
  const loginDriver = useCallback(async (credentials: LoginCredentials) => {
    return await authStore.loginDriver(credentials);
  }, [authStore]);
  
  // Logout function
  const logout = useCallback(async () => {
    return await authStore.logout();
  }, [authStore]);
  
  return {
    // State
    isAuthenticated: authStore.isAuthenticated,
    currentUser: authStore.currentUser,
    isLoading: authStore.isLoading,
    error: authStore.error,
    isAdmin: authStore.isAdmin,
    isDriver: authStore.isDriver,
    
    // Actions
    login,
    loginAdmin,
    loginDriver,
    logout,
  };
}

/**
 * Hook for accessing user-related functionality
 * @returns User actions and state
 */
export function useUsers() {
  const { userStore } = useStore();
  
  // Fetch users function
  const fetchUsers = useCallback(async (params?: PaginationParams) => {
    return await userStore.fetchUsers(params);
  }, [userStore]);
  
  // Fetch user by ID function
  const fetchUserById = useCallback(async (id: number) => {
    return await userStore.fetchUserById(id);
  }, [userStore]);
  
  // Select user function
  const selectUser = useCallback((id: number) => {
    userStore.selectUser(id);
  }, [userStore]);
  
  return {
    // State
    users: userStore.users,
    selectedUser: userStore.selectedUser,
    isLoading: userStore.isLoading,
    error: userStore.error,
    pagination: {
      currentPage: userStore.currentPage,
      itemsPerPage: userStore.itemsPerPage,
      totalItems: userStore.totalItems,
      totalPages: userStore.totalPages,
    },
    
    // Actions
    fetchUsers,
    fetchUserById,
    selectUser,
    getUserById: userStore.getUserById,
  };
}

/**
 * Hook that combines multiple resource hooks
 * @returns Combined hooks
 */
export function useApi() {
  const auth = useAuth();
  const users = useUsers();
  
  return {
    auth,
    users,
  };
} 