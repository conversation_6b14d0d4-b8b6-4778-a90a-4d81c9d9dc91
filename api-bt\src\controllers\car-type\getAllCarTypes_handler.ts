import { Request, Response } from 'express';
import {
  getAllCarTypes,
} from '../../services/car-type';

export const getAllCarTypes_handler = async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string, 10) || 1;
    const limit = parseInt(req.query.limit as string, 10) || 10;
    const seats = parseInt(req.query.seats as string, 10) || 0;
    const paginatedResult = await getAllCarTypes(page, limit, seats);
    return res.json(paginatedResult);
  } catch (err: any) {
    return res.status(400).json({ error: err.message });
  }
};
