import { useFormContext } from 'react-hook-form';
import { Input } from 'rizzui';
import cn from '@/utils/class-names';
import FormGroup from '@/app/shared/lib/form-group';

interface FormSenderInfoProps {
  className?: string;
}

export default function FormSenderInfo({ className }: FormSenderInfoProps) {
  const {
    register,
    formState: { errors },
  } = useFormContext();
  return (
    <FormGroup
      title="Let's get some information about your new bag type"
      description="Add data here"
      className={cn(className)}
    >
      <Input
        label="Name"
        placeholder="ie: Satch<PERSON>, To<PERSON>, <PERSON><PERSON>, etc"
        labelClassName="font-medium text-gray-900"
        {...register('name')}
        error={errors.name?.message as string}
      />
      <Input
        label="Description"
        labelClassName="font-medium text-gray-900"
        placeholder="ie: A small bag with a long strap, used for carrying personal belongings"
        {...register('description')}
        error={errors.description?.message as string}
      />
    </FormGroup>
  );
}
