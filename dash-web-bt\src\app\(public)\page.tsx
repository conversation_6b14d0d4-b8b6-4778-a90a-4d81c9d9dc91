'use client';

import React from 'react';

import {I18nProvider} from "@/hooks/use-translation";
import i18nTranslations from './i18n-translations.json';

import dynamic from "next/dynamic";
const ContentChildren = dynamic(
    () => import('./(sections)/ContentChildren'),
    { ssr: false }
)

export default function MultiStepFormPage() {
    return (
        <I18nProvider translations={i18nTranslations} defaultLanguage="ES">
            <ContentChildren />
        </I18nProvider>
    );
}
