'use client';

import Image from 'next/image';
import {useForm} from 'react-hook-form';
import b_03 from '@public/banners/b_03.webp';
import FormSummary from '@/app/shared/app/bookings/create-multi-step/form-summary';
import {useStepperOne} from '@/app/shared/app/bookings/create-multi-step';
import {useBookingSession} from "@/hooks/use-booking-ls";
import {useEffect} from "react";

export default function StepOne() {
    const {step, gotoNextStep, setStep} = useStepperOne();
    const {handleSubmit, setValue} = useForm();
    const bookingSession = useBookingSession();
    const bookingSessionData = bookingSession?.getSessionIfValid() || {}

    // console.log('bookingSessionData', bookingSessionData)
    if(!localStorage.getItem('payment_type')) {
        localStorage.setItem('payment_type', 'Cash');
    }

    useEffect(() => {
        if (bookingSessionData && bookingSessionData.current_step) {
            setStep(bookingSessionData.current_step - 1);
        }
    }, [bookingSessionData, step]);

    const onSubmit = () => {
        gotoNextStep();
    };

    return (
        <>
            <div className="col-span-full flex flex-col justify-center @4xl:col-span-5">
                <FormSummary
                    descriptionClassName="@7xl:me-10"
                    title="Hello there !"
                    description="We will be asking for some more information to be able to provide a good experience."
                />
            </div>

            <div className="col-span-full flex items-center justify-center @5xl:col-span-7">
                <form
                    id={`rhf-${step.toString()}`}
                    onSubmit={handleSubmit(onSubmit)}
                    className="col-span-full grid aspect-[4/3] gap-4 @3xl:grid-cols-12 @4xl:col-span-7 @5xl:gap-5 @7xl:gap-8 mb-50"
                >
                    <Image
                        src={b_03}
                        alt="home front part 1"
                        className="mt-auto rounded-lg object-cover object-left-top @3xl:col-span-4 @3xl:h-96 @6xl:h-5/6"
                    />
                    <Image
                        src={b_03}
                        alt="home front part 2"
                        className="my-auto hidden rounded-lg object-cover @3xl:col-span-4 @3xl:block @3xl:h-96 @6xl:h-5/6"
                    />
                    <Image
                        src={b_03}
                        alt="home front part 3"
                        className="mb-auto hidden rounded-lg object-cover object-right-bottom @3xl:col-span-4 @3xl:block @3xl:h-96 @6xl:h-5/6"
                    />
                </form>
            </div>
        </>
    );
}
