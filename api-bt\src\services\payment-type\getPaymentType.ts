import { getRepository } from 'typeorm';
import { PaymentType } from '../../models/PaymentType'; // Adjust the path as necessary


export const getPaymentType = async (id: string) => {
  const paymentTypeRepository = getRepository(PaymentType);
  try {
    const paymentType = await paymentTypeRepository.findOne(id);
    if (!paymentType) {
      return { success: false, data: null, error: 'PaymentType not found' };
    }
    return { success: true, data: paymentType, error: null };
  } catch (error: any) {
    return { success: false, data: null, error: error.message };
  }
};
