import { getRepository } from 'typeorm';
import { Zone } from '../../models/Zone'; // Adjust the path as necessary
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const updateZone = async (id: string, data: Partial<Zone>) => {
  const zoneRepository = getRepository(Zone);
  try {
    const zone = await zoneRepository.findOne(id);
    if (!zone) {
      return { success: false, data: null, error: 'Zone not found' };
    }

    zoneRepository.merge(zone, data);
    const updatedZone = await zoneRepository.save(zone);
    return { success: true, data: updatedZone, error: null };
  } catch (error) {
    const interpretedError = interpretDatabaseError(error);
    return { success: false, data: null, error: interpretedError };
  }
};
