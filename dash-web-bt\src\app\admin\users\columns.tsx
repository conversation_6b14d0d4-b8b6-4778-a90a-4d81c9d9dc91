'use client';

import Link from 'next/link';
import { Text, Tooltip, Checkbox, ActionIcon } from 'rizzui';
import { HeaderCell } from '@/components/lib/ui/table';
import PencilIcon from '@/components/lib/icons/pencil';
import DateCell from '@/components/lib/ui/date-cell';
import DeletePopover from '@/app/shared/lib/delete-popover';
import { routes } from "@/config/routes";
import { UserSchemaType } from '@/utils/validators/app/entities';

type ColumnsProps = {
    data: UserSchemaType[];
    sortConfig?: { direction: string; key: string };
    handleSelectAll: () => void;
    checkedItems: string[];
    onDeleteItem: (id: string) => void;
    onHeaderCellClick: (value: string) => void;
    onChecked?: (id: string) => void;
};

export const getColumns = ({
    sortConfig,
    onDeleteItem,
    onHeaderCellClick,
}: ColumnsProps) => [
    {
        title: (
            <HeaderCell
                title="Name"
                sortable
                ascending={sortConfig?.direction === 'asc' && sortConfig?.key === 'name'}
            />
        ),
        onHeaderCell: () => onHeaderCellClick('name'),
        dataIndex: 'name',
        key: 'name',
        width: 200,
        render: (value: string, row: UserSchemaType) => {
            return (
                <Text className="font-medium text-gray-700 dark:text-gray-600">
                    {row?.first_name} {row?.last_name}
                </Text>
            )
        },
    },
    // email
    {
        title: (
            <HeaderCell
                title="Email"
                sortable
                ascending={sortConfig?.direction === 'asc' && sortConfig?.key === 'email'}
            />
        ),
        onHeaderCell: () => onHeaderCellClick('email'),
        dataIndex: 'email',
        key: 'email',
    },

    // country
    // {
    //     title: (
    //         <HeaderCell
    //             title="Country"
    //             sortable
    //             ascending={sortConfig?.direction === 'asc' && sortConfig?.key === 'country'}
    //         />
    //     ),
    //     onHeaderCell: () => onHeaderCellClick('country'),
    //     dataIndex: 'country',
    // },
    // role in this format: 
    // "role"/* : {
    //         "id": 1,
    //         "name": "admin",
    //         "created_at": "2024-09-22T17:31:17.729Z"
    //     }, */
    {
        title: (
            <HeaderCell
                title="Role"
                sortable
                ascending={sortConfig?.direction === 'asc' && sortConfig?.key === 'role'}
            />
        ),
        onHeaderCell: () => onHeaderCellClick('role'),
        dataIndex: 'role',
        key: 'role',
        width: 200,
        render: (value: any, row: UserSchemaType) => {
            return (
                <Text>{value?.name}</Text>
            )
        },
    },
    // driver name in this format : 
    // driver": {
    //     "id": "622e9c3e-b6da-4e47-8a5d-458b46a908bc",
    //     "first_name": "Doner",
    //     "last_name": "Kebab",
    //     "created_at": "2024-09-21T13:20:05.777Z",
    //     "updated_at": "2024-09-21T13:20:05.777Z",
    //     "updated_by": null
    // }
    {
        title: (
            <HeaderCell
                title="Driver"
                sortable
                ascending={sortConfig?.direction === 'asc' && sortConfig?.key === 'driver'}
            />
        ),
        onHeaderCell: () => onHeaderCellClick('driver'),
        dataIndex: 'driver',
        key: 'driver',
        width: 200,
        render: (value: any) => <Text>{!!value?.first_name && !!value?.last_name ? `Yes` : 'N\\A'}</Text>,
    },
    {
        title: (
            <HeaderCell
                title="Created At"
                sortable
                ascending={sortConfig?.direction === 'asc' && sortConfig?.key === 'created_at'}
            />
        ),
        onHeaderCell: () => onHeaderCellClick('created_at'),
        dataIndex: 'created_at',
        key: 'created_at',
        width: 200,
        render: (value: Date) => <DateCell date={value} />,
    },
    {
        title: <></>,
        dataIndex: 'action',
        key: 'action',
        width: 140,
        render: (_: string, row: UserSchemaType) => (
            <div className="flex items-center justify-end gap-3 pe-3">
                <Tooltip size="sm" content="Edit User" placement="top" color="invert">
                    <Link href={routes.userEdit(row.id)}>
                        <ActionIcon
                            as="span"
                            size="sm"
                            variant="outline"
                            className="hover:!border-gray-900 hover:text-gray-700"
                        >
                            <PencilIcon className="h-4 w-4" />
                        </ActionIcon>
                    </Link>
                </Tooltip>
                <DeletePopover
                    title="Delete the user"
                    description={`Are you sure you want to delete this #${row.id} user?`}
                    onDelete={() => {
                        if (row.id) {
                            onDeleteItem(row.id.toString())
                        }
                    }}
                />
            </div>
        ),
    },
];
