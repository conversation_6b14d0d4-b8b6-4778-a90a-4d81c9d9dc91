import { getRepository } from 'typeorm';
import { CarTypePrice } from '../../models/CarTypePrice';
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const updateCarTypePrice = async (id: string, data: Partial<CarTypePrice>) => {
    const carTypePriceRepository = getRepository(CarTypePrice);
    try {
        const carTypePrice = await carTypePriceRepository.findOne(id);
        if (!carTypePrice) {
            return { success: false, data: null, error: 'CarTypePrice not found' };
        }

        carTypePriceRepository.merge(carTypePrice, data);
        const updatedCarTypePrice = await carTypePriceRepository.save(carTypePrice);
        return { success: true, data: updatedCarTypePrice, error: null };
    } catch (error) {
        const interpretedError = interpretDatabaseError(error);
        return { success: false, data: null, error: interpretedError };
    }
};
