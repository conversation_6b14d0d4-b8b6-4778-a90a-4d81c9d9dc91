import { Request, Response } from 'express';
import {
    deleteCarType_Bag,
} from '../../services/car-type-bag';

export const deleteCarType_Bag_handler = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        const result = await deleteCarType_Bag(id);
        if (!result.success) {
            return res.status(404).json(result);
        }
        return res.json(result);
    } catch (error: any) {
        return res.status(500).json({ error: error.message });
    }
};
