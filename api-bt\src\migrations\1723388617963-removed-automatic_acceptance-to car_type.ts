import {MigrationInterface, QueryRunner, TableColumn} from "typeorm";

export class removedAutomaticAcceptanceFromCarAddedToCarType1723388617963 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropColumn('car', new TableColumn({
            name: 'automatic_acceptance',
            type: 'boolean',
            default: false, // Set a default value if necessary
        }));
        await queryRunner.addColumn('car_type', new TableColumn({
            name: 'automatic_acceptance',
            type: 'boolean',
            default: false, // Set a default value if necessary
        }));
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.addColumn('car', new TableColumn({
            name: 'automatic_acceptance',
            type: 'boolean',
            default: false, // Set a default value if necessary
        }));
        await queryRunner.dropColumn('car_type', new TableColumn({
            name: 'automatic_acceptance',
            type: 'boolean',
            default: false, // Set a default value if necessary
        }));
    }

}
