import { routes } from '@/config/routes';
import { metaObject } from '@/config/site.config';
import CreateEdit from '@/app/shared/app/bookings/create-edit';
import PageHeader from '@/app/shared/lib/page-header';

export const metadata = {
  ...metaObject('Create Booking'),
};

const pageHeader = {
  title: 'Create Booking',
  breadcrumb: [
    {
      href: routes.dashboard,
      name: 'Dashboard',
    },
    {
      href: routes.bookings,
      name: 'Bookings',
    },
    {
      name: 'Create Booking',
    },
  ],
};

export default function CreateShipmentPage() {
  return (
    <>
      <PageHeader title={pageHeader.title} breadcrumb={pageHeader.breadcrumb}>
      </PageHeader>

      <CreateEdit />
    </>
  );
}
