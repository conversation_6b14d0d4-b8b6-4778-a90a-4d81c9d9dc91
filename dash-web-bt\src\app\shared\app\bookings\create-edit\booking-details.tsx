import {Controller, useFormContext} from 'react-hook-form';
import {Select, Input, RadioGroup, AdvancedRadio, Text, Checkbox} from 'rizzui';
import cn from '@/utils/class-names';
import NoSSR from '@/components/lib/no-ssr';
import FormGroup from '@/app/shared/lib/form-group';
import {DatePicker} from "@/components/lib/ui/datepicker";
import moment from "moment/moment";
import React, {useEffect, useState} from "react";
import {usePaginatedCustomers} from "@/services/query/customer/usePaginatedCustomers";
import {SearchableSelect} from "@/components/app/searchable-select";
import {CustomerSchemaType} from "@/utils/validators/app/entities";

interface BookingDetailsProps {
    className?: string;
}

export default function BookingDetails({
                                           className,
                                       }: BookingDetailsProps) {

    const {
        control,
        setValue,
        getValues,
        formState: {errors},
        register,
        watch,
    } = useFormContext();

    const [bookingFinishedAt, setBookingFinishedAt] = useState<Date | undefined>();
    const [selectedCustomer, setSelectedCustomer] = useState<CustomerSchemaType | null>(() => null);
    const [customerSearchString, setCustomerSearchString] = useState(() => '');
    const [createNewCustomer, setCreateNewCustomer] = useState<boolean>(() => false);

    const bookingCustomer = watch('customer');
    const scheduledAt = getValues('scheduled_at');

    function setBookingFinishedAtDate(date: Date) {
        setValue('booking_finished_at', moment(date).toISOString());
        setBookingFinishedAt(date);
    }

    const {
        isPending,
        isError,
        error,
        data: customers,
        isFetching,
        isPlaceholderData,
        refetch: refetchPaginatedCustomers,
        status,
    } = usePaginatedCustomers(1, 30, {
        name: customerSearchString,
    });

    useEffect(() => {
        refetchPaginatedCustomers().then(r => {
        });
    }, [customerSearchString])

    useEffect(() => {
        if (customers && customers.data) {
            setCreateNewCustomer(customers.count <= 0);
        }
    }, [customers]);

    const bookingStatuses = [
        {
            label: 'Pending',
            value: 'pending',
        },
        {
            label: 'Accepted',
            value: 'accepted',
        },
        {
            label: 'Rejected',
            value: 'rejected',
        },
        {
            label: 'Completed',
            value: 'completed',
        },
        {
            label: 'Cancelled',
            value: 'cancelled',
        }
    ]

    function searchCustomer(searchString: string) {
        setCustomerSearchString(searchString);
    }

    function onCustomerSelect(d: any[], onChange: any) {
        const customer = customers?.data.find((c) => c.id === d);
        setSelectedCustomer(customer);
        onChange(d)
    }

    return (
        <>
            <FormGroup
                title="Booking"
                description="General Booking Information"
                className={cn(className)}
            >
                <div>
                    <Text as="span"
                          className="mb-2 block text-sm rizzui-select-label block text-sm mb-1.5 font-medium text-gray-900">
                        Booking Finished At
                    </Text>

                    <Controller
                        control={control}
                        name="booking_finished_at"
                        render={({field: {value, onChange}}) => {
                            console.log('value', value);
                            return (
                                <DatePicker
                                    selected={(value && new Date().getTime() > new Date(scheduledAt).getTime()) || new Date()}
                                    onChange={(date: Date) => onChange(moment(date).toISOString())}
                                    minDate={moment().add(1, 'days').toDate()}
                                    placeholderText="Finished at"
                                    disabled={(value && new Date(value).getTime()) < new Date(scheduledAt).getTime()}
                                    showTimeSelect
                                    timeIntervals={15}
                                />
                            )
                        }}
                    />
                </div>

                <NoSSR>
                    <Controller
                        control={control}
                        name="status"
                        render={({field: {value, onChange}}) => (
                            <Select
                                label="Status"
                                className=""
                                labelClassName="text-gray-900"
                                dropdownClassName="p-2 gap-1 grid !z-10"
                                inPortal={false}
                                value={value}
                                onChange={onChange}
                                options={bookingStatuses}
                                getOptionValue={(option) => option.value}
                                displayValue={(selected: string) =>
                                    bookingStatuses?.find((c) => c.value === selected)?.label ?? ''
                                }
                                error={errors?.status?.message as string}
                            />
                        )}
                    />
                </NoSSR>

                <Input
                    label="Driver Observation"
                    placeholder=""
                    type={'text'}
                    labelClassName="font-medium text-gray-900"
                    {...register('driver_observation')}
                    error={errors.driver_observation?.message as string}
                />

                <NoSSR>
                    {
                        !createNewCustomer && customers && customers?.data.length ? (
                            <div className="w-full">
                                <Text as="span" className="mb-2 block text-sm">
                                    Pick a customer...
                                </Text>

                                <Controller
                                    control={control}
                                    name="customer"
                                    render={({field: {value, onChange}}) => (
                                        <SearchableSelect
                                            data={customers?.data.map((customer) => ({
                                                value: customer.id,
                                                label: customer.first_name + ' ' + customer.last_name,
                                            })) || []}
                                            disabled={!!value}
                                            isFetching={isFetching}
                                            value={value}
                                            onChange={(d) => {
                                                onCustomerSelect(d, onChange)
                                            }}
                                        />
                                    )}
                                />
                            </div>
                        ) : null
                    }
                </NoSSR>

                {!bookingCustomer ?
                        <div className="col-span-full">
                            <Checkbox
                                // disabled={customers && customers?.data.length < 1}
                                className="cursor-pointer"
                                label={createNewCustomer ? "Customer information" : "... or register a new one."}
                                checked={createNewCustomer}
                                onChange={() => {
                                    setValue('customer', null)
                                    setCreateNewCustomer(!createNewCustomer)
                                }}
                            />
                        </div>
                    : null
                }

                {
                    createNewCustomer ? (
                        <>
                            <Input
                                label="First Name"
                                placeholder=""
                                type={'text'}
                                labelClassName="font-medium text-gray-900"
                                {...register('customer_first_name')}
                                error={errors.customer_first_name?.message as string}
                            />

                            <Input
                                label="Last Name"
                                placeholder=""
                                type={'text'}
                                labelClassName="font-medium text-gray-900"
                                {...register('customer_last_name')}
                                error={errors.customer_last_name?.message as string}
                            />

                            <Input
                                label="Phone Number"
                                placeholder=""
                                type={'text'}
                                labelClassName="font-medium text-gray-900"
                                {...register('customer_phone_number')}
                                error={errors.customer_phone_number?.message as string}
                            />
                        </>
                    ) : null
                }

            </FormGroup>
        </>
    );
}
