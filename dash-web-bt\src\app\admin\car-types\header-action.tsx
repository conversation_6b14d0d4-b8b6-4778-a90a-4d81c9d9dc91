'use client';

import { Button, cn } from 'rizzu<PERSON>';
import { PiPlusBold, PiFolderBold, PiXBold } from 'react-icons/pi';

interface HeaderActionProps {
  title: 'car type';
  className?: string;
}

export default function HeaderAction({ title, className }: HeaderActionProps) {
  return (
    <>
      <div className={cn(className, 'mt-4 flex flex-wrap items-center gap-4')}>
        <Button
          className="w-full text-xs capitalize @lg:w-auto sm:text-sm"
        >
          <PiPlusBold className="me-1.5 h-[17px] w-[17px]" />
          Create {title}
        </Button>
      </div>
    </>
  );
}
