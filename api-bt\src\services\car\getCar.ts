import { getRepository } from 'typeorm';
import { Car } from '../../models/Car';
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const getCar = async (id: string) => {
  const carRepository = getRepository(Car);
  try {
    const car = await carRepository.findOne(id, {
      relations: ['type', 'drivers'],
    });
    if (!car) {
      return { success: false, data: null, error: 'Car not found' };
    }
    return { success: true, data: car, error: null };
  } catch (error) {
    const interpretedError = interpretDatabaseError(error);
    return { success: false, data: null, error: interpretedError };
  }
};
