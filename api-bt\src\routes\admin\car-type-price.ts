import express from 'express';

import {
    updateCarType<PERSON><PERSON><PERSON><PERSON><PERSON>,
    getCarType<PERSON><PERSON><PERSON><PERSON><PERSON>,
    createCarType<PERSON><PERSON><PERSON><PERSON><PERSON>,
    deleteCarType<PERSON><PERSON><PERSON>and<PERSON>,
    getAllCarTypePricesHandler,
} from '../../controllers/car-type-price'; // Adjust the path as necessary

export const carTypePriceRouter = express.Router();

carTypePriceRouter.post('/car-type-prices', createCarTypePriceHandler);
carTypePriceRouter.delete('/car-type-prices/:id', deleteCarTypePriceHandler);
carTypePriceRouter.get('/car-type-prices', getAllCarTypePricesHandler);
carTypePriceRouter.get('/car-type-prices/:id', getCarTypePriceHandler);
carTypePriceRouter.put('/car-type-prices/:id', updateCarTypePriceHandler);
