import { AxiosError } from 'axios';
import { ApiErrorResponse } from '../core/types';

/**
 * Handles API errors and standardizes error responses
 * @param error The error to handle
 * @returns Standardized API error response
 */
export function handleApiError(error: unknown): ApiErrorResponse {
  // If it's an Axios error
  if (isAxiosError(error)) {
    const response = error.response;
    
    // Server responded with an error
    if (response && response.data) {
      // Define a type for the expected shape of response data
      const data = response.data as Record<string, any>;
      
      // If the API already returned a structured error
      if (data.success === false && data.msg) {
        return {
          success: false,
          msg: data.msg as string,
          statusCode: response.status,
        };
      }
      
      // If the API returned a message field
      if (data.message) {
        return {
          success: false,
          msg: data.message as string,
          statusCode: response.status,
        };
      }
    }
    
    // No structured response or no response
    return {
      success: false,
      msg: error.message || 'An error occurred during the request',
      statusCode: response?.status || 500,
    };
  }
  
  // Not an Axios error, return a generic error
  return {
    success: false,
    msg: error instanceof Error ? error.message : 'Unknown error occurred',
    statusCode: 500,
  };
}

/**
 * Type guard to check if an error is an Axios error
 * @param error The error to check
 * @returns Whether the error is an Axios error
 */
function isAxiosError(error: unknown): error is AxiosError {
  return (
    typeof error === 'object' &&
    error !== null &&
    'isAxiosError' in error &&
    (error as any).isAxiosError === true
  );
} 