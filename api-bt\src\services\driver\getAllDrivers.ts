import {getRepository, Not, IsNull} from 'typeorm';
import {Driver} from '../../models/Driver';
import {paginate, PaginatedResult} from '../../utils/pagination';

const buildDriverFilter = (filterString: Record<string, any>, filterWithCar: boolean) => {
    const filter: any = {};

    if (filterString.first_name) {
        filter.first_name = filterString.first_name;
    }
    if (filterString.last_name) {
        filter.last_name = filterString.last_name;
    }

    if (filterWithCar) {
        filter.car = {id: Not(IsNull())};
    }

    return filter;
};

export const getAllDrivers = async (
    page = 1,
    limit = 10,
    filterString?: Record<string, any>,
    filterWithCar: boolean = false
): Promise<PaginatedResult<Driver>> => {
    const driverRepository = getRepository(Driver);

    const filters = buildDriverFilter(filterString || {}, filterWithCar);

    const options: any = {
        skip: (page - 1) * limit,
        take: limit,
        relations: ["car"],
        where: filters,
        order: {
            created_at: 'DESC',  // Order by created_at in descending order
        },
    };

    const [data, totalRecords] = await driverRepository.findAndCount(options);

    return paginate<Driver>(data, page, limit, totalRecords);
};
