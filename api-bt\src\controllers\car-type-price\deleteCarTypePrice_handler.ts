import { Request, Response } from 'express';
import {
    deleteCarTypePrice,
} from '../../services/car-type-price'; // Adjust the path as necessary

export const deleteCarTypePriceHandler = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        const result = await deleteCarTypePrice(id);
        if (!result.success) {
            return res.status(404).json(result);
        }
        return res.json(result);
    } catch (error: any) {
        return res.status(500).json({ error: error.message });
    }
};
