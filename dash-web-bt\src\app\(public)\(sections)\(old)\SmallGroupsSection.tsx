// SmallGroupsSection.tsx
import React from 'react';
import Container from '@/app/(public)/(sections)/(old)/container';
import { IoMdPeople } from 'react-icons/io'; // Icon for small groups

const SmallGroupsSection: React.FC = () => {
    return (
        <section className="py-16 bg-white text-gray-800">
            <Container>
                <div className="flex flex-col md:flex-row items-center gap-8">
                    <div className="md:w-1/2">
                        <IoMdPeople className="text-6xl text-primary-500 mb-4" />
                        <h2 className="text-3xl font-semibold mb-4">Grupos Pequeños</h2>
                        <p className="mb-4">
                            Nuestra empresa dispone de una flota de vehículos que se adaptan perfectamente a las necesidades de nuestros clientes. Para el traslado de grupos reducidos ofrecemos cuatro categorías: Económica, Estándar, Ejecutiva y Lujo.
                        </p>
                        <p>
                            También ofrecemos servicio de traslado de hasta 7 pasajeros con nuestro monovolumen Mercedes, perfecto para excursiones y excursiones de un día a Málaga y alrededores.
                        </p>
                    </div>
                    <div className="md:w-1/2">
                        <img src="/banners/main_5.png" alt="Small Group" className="rounded-lg shadow-lg" />
                    </div>
                </div>
            </Container>
        </section>
    );
};

export default SmallGroupsSection;
