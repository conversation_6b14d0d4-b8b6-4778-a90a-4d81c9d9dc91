import express from 'express';
import {
  createRole_handler,
  deleteRole_handler, getAllRoles_handler, getRole_handler, updateRole_handler,
} from '../../controllers/role';

export const roleRouter = express.Router();

roleRouter.post('/roles', createRole_handler);
roleRouter.get('/roles', getAllRoles_handler);
roleRouter.get('/roles/:id', getRole_handler);
roleRouter.put('/roles/:id', updateRole_handler);
roleRouter.delete('/roles/:id', deleteRole_handler);
