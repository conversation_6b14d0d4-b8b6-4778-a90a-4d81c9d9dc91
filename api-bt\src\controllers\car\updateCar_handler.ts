import { Request, Response } from 'express';
import { updateCar } from '../../services/car';

export const updateCar_handler = async (req: Request, res: Response) => {
  try {
    const carData = req.body;
    const driverIds = req.body.drivers;

    const result = await updateCar(req.params.id, carData, driverIds);

    if (result.success) {
      return res.json(result);
    } else {
      return res.status(400).json(result);
    }
  } catch (error: any) {
    return res.status(500).json({
      success: false,
      message: 'Failed to update car',
      errorType: 'InternalServerError',
      data: undefined,
    });
  }
};