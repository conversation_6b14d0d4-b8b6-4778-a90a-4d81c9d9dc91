import { ApiClientConfig } from './types';

/**
 * Default API client configuration
 */
export const DEFAULT_CONFIG: ApiClientConfig = {
  baseUrl: 'http://localhost:3000/api',
  timeout: 10000, // 10 seconds
  headers: {
    'Content-Type': 'application/json',
  },
};

/**
 * Global API configuration that can be modified at runtime
 */
export let apiConfig: ApiClientConfig = { ...DEFAULT_CONFIG };

/**
 * Configure the API client
 * @param config Configuration options to override defaults
 */
export function configureApi(config: Partial<ApiClientConfig>): void {
  apiConfig = {
    ...apiConfig,
    ...config,
    headers: {
      ...apiConfig.headers,
      ...config.headers,
    },
  };
}

/**
 * Reset API configuration to defaults
 */
export function resetApiConfig(): void {
  apiConfig = { ...DEFAULT_CONFIG };
} 