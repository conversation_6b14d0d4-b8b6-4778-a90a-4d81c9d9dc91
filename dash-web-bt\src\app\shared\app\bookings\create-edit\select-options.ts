export const countries = [
  { label: 'Bangladesh', value: 'BD' },
  { label: 'United States', value: 'US' },
  { label: 'Canada', value: 'CA' },
  { label: 'United Kingdom', value: 'UK' },
  { label: 'Australia', value: 'AU' },
  { label: 'Germany', value: 'DE' },
  { label: 'France', value: 'FR' },
  { label: 'Japan', value: 'JP' },
  { label: 'Brazil', value: 'BR' },
  { label: 'China', value: 'CN' },
];

export const agencies = [
  { label: 'CreativeWorks Agency', value: 'creativeworks' },
  { label: 'PixelPerfect Media', value: 'pixelperfect' },
  { label: 'Starlight Productions', value: 'starlight' },
  { label: 'Innovate Solutions', value: 'innovate' },
  { label: 'Fusion Marketing Group', value: 'fusion' },
  { label: 'ProMedia Experts', value: 'promedia' },
  { label: 'SwiftTech Studios', value: 'swifttech' },
  { label: 'Dynamic Digital Agency', value: 'dynamicdigital' },
  { label: 'Elite Advertising Co.', value: 'eliteadvertising' },
  { label: 'ThinkTank Designers', value: 'thinktank' },
];

export const offices = [
  { label: 'New York-New York/USA', value: 'NY-NY/USA' },
  { label: 'Los Angeles-California/USA', value: 'LA-CA/USA' },
  { label: 'Chicago-Illinois/USA', value: 'CH-IL/USA' },
  { label: 'Miami-Florida/USA', value: 'MI-FL/USA' },
  { label: 'London-England/UK', value: 'LD-ENG/UK' },
  { label: 'Paris-Île-de-France/France', value: 'PA-IDF/FRA' },
  { label: 'Sydney-New South Wales/Australia', value: 'SYD-NSW/AUS' },
  { label: 'Toronto-Ontario/Canada', value: 'TOR-ON/CAN' },
  { label: 'Tokyo-Tokyo/Japan', value: 'TK-TYO/JPN' },
  { label: 'São Paulo-São Paulo/Brazil', value: 'SP-SP/BRA' },
];

export const shippingMethods = [
  { label: 'Standard Shipping', value: 'standard' },
  { label: 'Express Shipping', value: 'express' },
  { label: 'Two-Day Shipping', value: 'two_day' },
  { label: 'Overnight Shipping', value: 'overnight' },
  { label: 'Economy Shipping', value: 'economy' },
  { label: 'Priority Shipping', value: 'priority' },
  { label: 'Ground Shipping', value: 'ground' },
  { label: 'Next Flight Shipping', value: 'next_flight' },
  { label: 'International Shipping', value: 'international' },
  { label: 'Free Shipping', value: 'free' },
];

export const packagingTypes = [
  { label: 'Box', value: 'box' },
  { label: 'Envelope', value: 'envelope' },
  { label: 'Tube', value: 'tube' },
  { label: 'Pallet', value: 'pallet' },
  { label: 'Bag', value: 'bag' },
  { label: 'Crate', value: 'crate' },
  { label: 'Carton', value: 'carton' },
  { label: 'Parcel', value: 'parcel' },
  { label: 'Barrel', value: 'barrel' },
  { label: 'Drum', value: 'drum' },
];

export const courierCompanies = [
  { label: 'UPS', value: 'ups' },
  { label: 'FedEx', value: 'fedex' },
  { label: 'DHL', value: 'dhl' },
  { label: 'USPS', value: 'usps' },
  { label: 'TNT Express', value: 'tnt' },
  { label: 'Royal Mail', value: 'royal_mail' },
  { label: 'Canada Post', value: 'canada_post' },
  { label: 'Australia Post', value: 'aus_post' },
  { label: 'DPD', value: 'dpd' },
  { label: 'EMS', value: 'ems' },
];

export const deliveryTimes = [
  { label: 'Standard Delivery (3-5 business days)', value: 'standard' },
  { label: 'Express Delivery (1-2 business days)', value: 'express' },
  { label: 'Next Day Delivery', value: 'next_day' },
  { label: 'Same Day Delivery', value: 'same_day' },
  { label: '2-Hour Delivery', value: '2_hour' },
  { label: 'Weekend Delivery', value: 'weekend' },
  { label: 'Economy Delivery (7-10 business days)', value: 'economy' },
  { label: 'International Delivery', value: 'international' },
  { label: 'Free Shipping (5-7 business days)', value: 'free' },
  { label: 'Custom Delivery', value: 'custom' },
];

export const paymentMethods = [
  { label: 'Credit Card', value: 'credit_card' },
  { label: 'Debit Card', value: 'debit_card' },
  { label: 'PayPal', value: 'paypal' },
  { label: 'Apple Pay', value: 'apple_pay' },
  { label: 'Google Pay', value: 'google_pay' },
  { label: 'Stripe', value: 'stripe' },
  { label: 'Venmo', value: 'venmo' },
  { label: 'Bank Transfer', value: 'bank_transfer' },
  { label: 'Cash on Delivery (COD)', value: 'cod' },
  { label: 'Cryptocurrency', value: 'cryptocurrency' },
];

export const paidBy = [
  { value: 'Sender', label: 'Sender' },
  { value: 'Receiver', label: 'Receiver' },
];
