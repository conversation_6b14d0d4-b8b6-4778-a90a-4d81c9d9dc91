import express from 'express';
import {
  createBooking<PERSON><PERSON><PERSON>,
  deleteBooking<PERSON><PERSON><PERSON>,
  getAllBookingsHandler,
  getBookingHandler,
  updateBookingHandler,
  getMonthlyBookingStatisticsHandler,
  getBookingByBookingCodeHandler,
} from '../../controllers/booking';

export const bookingRouter = express.Router();

bookingRouter.post('/bookings', createBookingHandler);
bookingRouter.get('/bookings', getAllBookingsHandler);
bookingRouter.get('/bookings/statistics/monthly', getMonthlyBookingStatisticsHandler);
bookingRouter.get('/bookings/:id', getBookingHandler);
bookingRouter.get('/bookings/by/:booking_code', getBookingByBookingCodeHandler);
bookingRouter.put('/bookings/:id', updateBookingHandler);
bookingRouter.delete('/bookings/:id', deleteBookingHandler);
