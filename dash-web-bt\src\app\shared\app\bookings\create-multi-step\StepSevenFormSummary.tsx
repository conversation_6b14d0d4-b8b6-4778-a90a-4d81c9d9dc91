import React, {HTMLAttributes, ReactNode, useEffect, useState} from 'react';
import { useAtom } from 'jotai';
import { formData<PERSON>tom, useStepperOne } from '@/app/shared/app/bookings/create-multi-step';
import { useForm } from 'react-hook-form';
import { RiEditLine, RiArrowRightLine, RiArrowLeftLine, RiCheckLine } from 'react-icons/ri';
import { HiOutlineLocationMarker, HiOutlineUserGroup, HiOutlineCreditCard, HiOutlineInformationCircle, HiOutlineCalendar } from 'react-icons/hi';
import { MdFlightTakeoff } from 'react-icons/md';
import { FaPaw, FaSuitcase } from 'react-icons/fa';
import { useCreateBooking } from "@/services/mutations/booking/public";
import toast from "react-hot-toast";
import moment from "moment";
import { BookingMutationResponseSchemaType } from "@/utils/validators/app/entities";
import { useBookingSession } from "@/hooks/use-booking-ls";
import { useActiveBookingsSession } from "@/hooks/use-active-bookings-ls";
import { useGetGlobalSettings } from "@/services/query/setting/public/useGetGlobalSettings";
import { Loader } from 'rizzui';

interface CardProps extends HTMLAttributes<HTMLDivElement> {
    children: ReactNode;
    className?: string;
}

interface CardHeaderProps extends HTMLAttributes<HTMLDivElement> {
    children: ReactNode;
    className?: string;
}

interface CardBodyProps extends HTMLAttributes<HTMLDivElement> {
    children: ReactNode;
    className?: string;
}

export const Card: React.FC<CardProps> = ({ children, className = '', ...props }) => {
    return (
        <div className={`rounded-lg shadow-md overflow-hidden ${className}`} {...props}>
            {children}
        </div>
    );
};

export const CardHeader: React.FC<CardHeaderProps> = ({ children, className = '', ...props }) => {
    return (
        <div className={`px-6 py-4 border-b border-gray-200 dark:border-gray-700 ${className}`} {...props}>
            {children}
        </div>
    );
};

export const CardBody: React.FC<CardBodyProps> = ({ children, className = '', ...props }) => {
    return (
        <div className={`px-6 py-4 ${className}`} {...props}>
            {children}
        </div>
    );
};

export const StepSevenFormSummary = () => {
    const payment_type = localStorage.getItem('payment_type');
    const [formData] = useAtom(formDataAtom);
    const activeBookings = useActiveBookingsSession();
    const { handleSubmit } = useForm({ defaultValues: formData });
    const { step, gotoNextStep, setStep } = useStepperOne();
    const { mutate: mutateCreate } = useCreateBooking();
    const bookingSession = useBookingSession();
    const { data: publicGlobalSettings } = useGetGlobalSettings('public');
    const [finalAmountToPay, setFinalAmountToPay] = useState<string | null>(null);
    const [loading, setLoading] = useState(false); // Add loading state

    const finalizeSession = (data: BookingMutationResponseSchemaType) => {
        bookingSession?.clearSession();
        let expirationDate = moment().add(1, 'days').toISOString();
        if (data.data.return_trip_time) {
            expirationDate = moment(data.data.return_trip_time).add(1, 'days').toISOString();
        }
        activeBookings?.addOrInitActiveBookings(data.data.id, expirationDate, data.data.payment.stripe_payment_id, false, new Date().toISOString(), data.data.booking_code);

        if (payment_type === 'Card' || payment_type === 'Cash') {
            localStorage.removeItem('payment_type');
            gotoNextStep();
            return;
        }
        setStep(8);
    }

    const onSubmit = (_data: any) => {
        setLoading(true); // Set loading to true
        mutateCreate({ payload: formData }, {
            onSuccess: (data) => {
                finalizeSession(data);
                setLoading(false); // Reset loading state
            },
            onError: (error: any) => {
                toast.error(error?.response?.data.error);
                setLoading(false); // Reset loading state
            }
        });
    };

    useEffect(() => {
        if (publicGlobalSettings && publicGlobalSettings.data && formData.final_amount_to_pay && formData.partial_payment) {
            if (formData.partial_payment === 'full') {
                setFinalAmountToPay(Number(formData.final_amount_to_pay).toFixed(2));
            } else {
                const partial_payment_deduction = publicGlobalSettings.data.find((pgs: any) => pgs.name === 'partial_payment_deduction');
                if (partial_payment_deduction) {
                    const val = (Number(formData.final_amount_to_pay) * (+partial_payment_deduction.value / 100) || 0.25).toFixed(2);
                    setFinalAmountToPay(val);
                }
            }
        }
    }, [publicGlobalSettings]);

    const handleEditStep = (step: number) => {
        // Implement navigation logic to respective step
        setStep(step);
    };

    return (
        <div className="col-span-full flex flex-col space-y-6 p-6 md:p-8 rounded-lg shadow-md text-chillGold">
            {
            
                loading ? (
                    <>
                        <div className="flex justify-center items-center mb-4">
                            <Loader variant="pulse" size="lg" />
                            <Loader variant="threeDot" size="lg" />
                            <Loader variant="pulse" size="lg" />
                            <Loader variant="threeDot" size="lg" />
                            <Loader variant="pulse" size="lg" />
                            <Loader variant="threeDot" size="lg" />
                            <Loader variant="pulse" size="lg" />
                            <Loader variant="threeDot" size="lg" />
                            <Loader variant="pulse" size="lg" />
                        </div>
                    </>
                ) : <>
                    <form
                        id={`rhf-${step.toString()}`}
                        onSubmit={handleSubmit(onSubmit)}>
                        <Card>
                            <CardHeader className="flex justify-between items-center">
                                <h2 className="text-chillGold text-2xl font-bold">Booking Summary</h2>
                                <button onClick={() => handleEditStep(1)}
                                        className="text-chillGold font-extrabold hover:underline flex items-center space-x-1">
                                    <RiEditLine className="text-lg"/>
                                    <span>Edit</span>
                                </button>
                            </CardHeader>
                            <CardBody className="space-y-4">
                                <div className="flex items-center space-x-4">
                                    <HiOutlineLocationMarker className="text-3xl"/>
                                    <div>
                                        <p className="text-creamyWhite text-lg font-medium">Pickup: {formData.pickup_address}</p>
                                        <p className="text-creamyWhite text-sm">Scheduled
                                            At: {moment(formData.scheduled_at).format('LLL')}</p>
                                    </div>
                                </div>
                                {formData.trip_type === 'transfer' && (
                                    <div className="flex items-center space-x-4">
                                        <RiArrowRightLine className="text-3xl"/>
                                        <div>
                                            <p className="text-creamyWhite text-lg font-medium">Destination: {formData.destination_address}</p>
                                            <p className="text-creamyWhite text-sm">Distance: {formData.trip_distance?.toFixed(2)} km</p>
                                            <p className="text-creamyWhite text-sm">Duration: {Math.floor((formData?.trip_duration || 0) / 60)}h {(formData?.trip_duration || 0) % 60}m</p>
                                        </div>
                                    </div>
                                )}
                                {formData.trip_type === 'hourly' && (
                                    <div className="flex items-center space-x-4">
                                        <HiOutlineCalendar className="text-3xl"/>
                                        <div>
                                            <p className="text-creamyWhite text-lg font-medium">Estimated Service
                                                Hours: {formData.estimated_service_hours}</p>
                                        </div>
                                    </div>
                                )}
                            </CardBody>
                        </Card>

                        <Card>
                            <CardHeader className="flex justify-between items-center">
                                <h2 className="text-chillGold text-2xl font-bold">Passenger Details</h2>
                                <button onClick={() => handleEditStep(2)}
                                        className="text-chillGold font-extrabold hover:underline flex items-center space-x-1">
                                    <RiEditLine className="text-lg"/>
                                    <span>Edit</span>
                                </button>
                            </CardHeader>
                            <CardBody className="space-y-4">
                                <div className="flex items-center space-x-4">
                                    <HiOutlineUserGroup className="text-creamyWhite text-3xl"/>
                                    <div>
                                        <p className="text-creamyWhite text-lg font-medium">Adults: {formData.adults}</p>
                                        <p className="text-creamyWhite text-sm">Children: {formData.children}</p>
                                    </div>
                                </div>
                                <div className="flex items-center space-x-4">
                                    <FaPaw className="text-3xl"/>
                                    <div>
                                        <p className="text-creamyWhite text-lg font-medium">Pets: {formData.pets}</p>
                                    </div>
                                </div>
                                {formData.flight_number && (
                                    <div className="flex items-center space-x-4">
                                        <MdFlightTakeoff className="text-3xl"/>
                                        <div>
                                            <p className="text-creamyWhite text-lg font-medium">Flight
                                                Number: {formData.flight_number}</p>
                                        </div>
                                    </div>
                                )}
                            </CardBody>
                        </Card>

                        <Card>
                            <CardHeader className="flex justify-between items-center">
                                <h2 className="text-chillGold text-2xl font-bold">Customer Details</h2>
                                <button onClick={() => handleEditStep(3)}
                                        className="text-chillGold font-extrabold hover:underline flex items-center space-x-1">
                                    <RiEditLine className="text-lg"/>
                                    <span>Edit</span>
                                </button>
                            </CardHeader>
                            <CardBody className="space-y-4">
                                <div className="flex items-center space-x-4">
                                    <HiOutlineInformationCircle className="text-3xl"/>
                                    <div>
                                        <p className="text-creamyWhite text-lg font-medium">Name: {formData.customer_first_name} {formData.customer_last_name}</p>
                                        <p className="text-creamyWhite text-sm">Phone: {formData.customer_phone_number}</p>
                                        <p className="text-creamyWhite text-sm">Email: {formData.customer_email}</p>
                                    </div>
                                </div>
                            </CardBody>
                        </Card>

                        <Card>
                            <CardHeader className="flex justify-between items-center">
                                <h2 className="text-chillGold text-2xl font-bold">Car Type</h2>
                                <button onClick={() => handleEditStep(4)}
                                        className="text-chillGold font-extrabold hover:underline flex items-center space-x-1">
                                    <RiEditLine className="text-lg"/>
                                    <span>Edit</span>
                                </button>
                            </CardHeader>
                            <CardBody className="space-y-4">
                                <div className="flex items-center space-x-4">
                                    <FaSuitcase className="text-3xl"/>
                                    <div>
                                        <p className="text-creamyWhite text-lg font-medium">Bags: {formData.bags.map(bag => bag.quantity).reduce((acc, quantity) => acc + quantity, 0)}</p>
                                    </div>
                                </div>
                            </CardBody>
                        </Card>

                        <Card>
                            <CardHeader className="flex justify-between items-center">
                                <h2 className="text-chillGold text-2xl font-bold">Payment Details</h2>
                                <button onClick={() => handleEditStep(5)}
                                        className="text-chillGold font-extrabold hover:underline flex items-center space-x-1">
                                    <RiEditLine className="text-lg"/>
                                    <span>Edit</span>
                                </button>
                            </CardHeader>
                            <CardBody className="space-y-4">
                                <div className="flex items-center space-x-4">
                                    <HiOutlineCreditCard className="text-3xl"/>
                                    <div>
                                        <p className="text-creamyWhite text-lg font-medium">Partial
                                            Payment: {formData.partial_payment === 'full' ? 'Full Payment' : 'Partial Payment'}</p>
                                        <p className="text-creamyWhite text-sm">Final Amount: {finalAmountToPay}€</p>
                                    </div>
                                </div>
                            </CardBody>
                        </Card>
                    </form>
                </>
            }
        </div>
    );
};

export default StepSevenFormSummary;
