'use client';

import {<PERSON><PERSON>, Loader} from 'rizzui';
import cn from '@/utils/class-names';
import { useScrollableSlider } from '@/hooks/use-scrollable-slider';
import {
  PiBank,
  PiCaretLeftBold,
  PiCaretRightBold,
  PiCube,
  PiCurrencyCircleDollar,
  PiFolder,
} from 'react-icons/pi';
import TransactionCard, {
  TransactionType,
} from '@/components/lib/cards/transaction-card';
import {MonthlyStatisticsSchemaType} from "@/utils/validators/app/entities";
import React from "react";

type FileStatsType = {
  className?: string;
  monthlyStats: MonthlyStatisticsSchemaType,
};

export function StatGrid({ monthlyStats }: { monthlyStats: MonthlyStatisticsSchemaType }) {
  const data = monthlyStats?.data;

  const statData: TransactionType[] | number[] = data
    ? [
        {
          id: 1,
          title: 'Total Income',
          amount: `€${data?.total_income?.current}`,
          increased: data.total_income?.increased_from_prev_month,
          percentage: data?.total_income?.percentage,
          icon: PiBank,
          iconWrapperFill: '#8A63D2',
        },
        {
          id: 2,
          title: 'Total Amount By Stripe',
          amount: `${data?.total_stripe_paid_amount?.current}`,
          increased: data?.total_stripe_paid_amount?.increased_from_prev_month,
          percentage: data?.total_stripe_paid_amount?.percentage,
          icon: PiBank,
          iconWrapperFill: '#F5A623',
        },
        {
          id: 3,
          title: 'Total Bookings',
          amount: `${data?.total_bookings?.current}`,
          increased: data?.total_bookings?.increased_from_prev_month,
          percentage: data?.total_bookings?.percentage,
          icon: PiCube,
          iconWrapperFill: '#00CEC9',
        },
        {
          id: 4,
          title: 'Paid Full By Stripe',
          amount: `${data?.full_stripe_payments?.current}`,
          increased: data?.full_stripe_payments?.increased_from_prev_month,
          percentage: data?.full_stripe_payments?.percentage,
          icon: PiCurrencyCircleDollar,
          iconWrapperFill: '#0070F3',
        },
      ]
    : [1, 2, 3, 4];

  return (
    <>
      {statData.map((stat: any) =>
        data ? (
          <TransactionCard key={stat.id} transaction={stat} className="min-w-[300px]" />
        ) : (
          <div
            key={`${stat}-loading`}
            className="grid h-32 flex-grow place-content-center items-center"
          >
            <Loader size="lg" />
          </div>
        )
      )}
    </>
  );
}

export default function FileStats({ className, monthlyStats }: FileStatsType) {
  const {
    sliderEl,
    sliderPrevBtn,
    sliderNextBtn,
    scrollToTheRight,
    scrollToTheLeft,
  } = useScrollableSlider();

  return (
    <div
      className={cn(
        'relative flex w-auto items-center overflow-hidden',
        className
      )}
    >
      <Button
        title="Prev"
        variant="text"
        ref={sliderPrevBtn}
        onClick={() => scrollToTheLeft()}
        className="!absolute -left-1 top-0 z-10 !h-full w-20 !justify-start rounded-none bg-gradient-to-r from-gray-0 via-gray-0/70 to-transparent px-0 ps-1 text-gray-500 hover:text-gray-900 dark:from-gray-50 dark:via-gray-50/70 3xl:hidden"
      >
        <PiCaretLeftBold className="h-5 w-5" />
      </Button>
      <div className="w-full overflow-hidden">
        <div
          ref={sliderEl}
          className="custom-scrollbar-x grid grid-flow-col gap-5 overflow-x-auto scroll-smooth 2xl:gap-6 "
        >
          <StatGrid monthlyStats={monthlyStats} />
        </div>
      </div>
      <Button
        title="Next"
        variant="text"
        ref={sliderNextBtn}
        onClick={() => scrollToTheRight()}
        className="dark: !absolute -right-2 top-0 z-10 !h-full w-20 !justify-end rounded-none bg-gradient-to-l from-gray-0 via-gray-0/70 to-transparent px-0 pe-2 text-gray-500 hover:text-gray-900 dark:from-gray-50 dark:via-gray-50/70 3xl:hidden "
      >
        <PiCaretRightBold className="h-5 w-5" />
      </Button>
    </div>
  );
}
