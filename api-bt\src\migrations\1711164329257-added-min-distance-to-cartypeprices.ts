import {MigrationInterface, QueryRunner, TableColumn} from "typeorm";

export class addedMinDistanceToCartypeprices1711164329257 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        const bookingTable = await queryRunner.getTable('car_type_price');
        if (bookingTable) {
            const columnExists = bookingTable.columns.some(column => column.name === 'min_distance');
            if (!columnExists) {
                // Step 1: Add the min_distance column as nullable
                await queryRunner.addColumn('car_type_price', new TableColumn({
                    name: 'min_distance',
                    type: 'integer',
                    isNullable: true,
                }));

                // Step 2: Update existing records to ensure no null values for the new column
                await queryRunner.query(`UPDATE "car_type_price" SET "min_distance" = 0 WHERE "min_distance" IS NULL;`);

                // Step 3: Alter the column to be non-nullable now that all rows have a value
                await queryRunner.changeColumn('car_type_price', 'min_distance', new TableColumn({
                    name: 'min_distance',
                    type: 'integer',
                    isNullable: false, // or you might omit isNullable for default non-nullable behavior
                }));
            }
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropColumn('car_type_price', 'min_distance');
    }

}
