'use client';

import { useEffect } from 'react';
import { useSet<PERSON>tom } from 'jotai';
import { useReset<PERSON>tom } from 'jotai/utils';
import { PiArrowUpLight, PiCheck } from 'react-icons/pi';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { Button } from 'rizzui';
import cn from '@/utils/class-names';
import {
  formDataAtom,
  stepperAtomOne,
  useStepperOne,
} from '@/app/shared/app/bookings/create-multi-step';
import { routes } from "@/config/routes";
import { useBookingSession } from "@/hooks/use-booking-ls";
import INITIAL_FORM_DATA from "@/app/shared/app/bookings/create-multi-step/initialFormData";

interface FooterProps {
  formId?: number;
  className?: string;
  isLoading?: boolean;
}

function buttonLabel(formId?: number) {
  if (formId === 6) {
    return (
      <>
        Submit <PiCheck />
      </>
    );
  }
  if (formId === 7) {
    return 'Next';
  }

  if (formId === 8) {
    return 'Finish';
  }
  return (
    <>
      Next <PiArrowUpLight className="rotate-90" />
    </>
  );
}

export default function Footer({ isLoading, className }: FooterProps) {
  const bookingSession = useBookingSession();
  const bookingSessionData = bookingSession?.getSessionIfValid() || {};
  const { push } = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const setFormData = useSetAtom(formDataAtom);
  const { step, gotoPrevStep } = useStepperOne();
  const resetLocation = useResetAtom(stepperAtomOne);

  useEffect(() => {
    resetLocation();
    setFormData({ ...INITIAL_FORM_DATA, ...bookingSessionData });
  }, [pathname, searchParams]);

  function buttonAttr() {
    return { form: `rhf-${step?.toString()}` };
  }

  return (
    <footer
      className={cn(
        'footer-container fixed bottom-0 left-0 right-0 flex items-center justify-between gap-4 px-6 py-4 lg:px-8 4xl:px-10',
        className
      )}
    >
      {step > 0 && step < 7 && (
        <Button
          rounded="md"
          variant="outline"
          onClick={gotoPrevStep}
          className="flex items-center gap-2 border-chillGold text-chillGold transition-transform duration-200 hover:border-chillGold hover:bg-chillGold hover:text-black"
        >
          <PiArrowUpLight className="-rotate-90" />
          Back
        </Button>
      )}

    <Button
        isLoading={isLoading}
        disabled={isLoading}
        variant="outline"
        rounded="md"
        {...buttonAttr()}
        type={'submit'}
        className="ml-auto flex items-center gap-2 border-chillGold text-chillGold transition-transform duration-200 hover:border-chillGold hover:bg-chillGold hover:text-black"
        onClick={() => {
          if (step > 7) {
            push(routes.landing);
          }
        }}
      >
        {buttonLabel(step)}
      </Button>

    </footer>
  );
}
