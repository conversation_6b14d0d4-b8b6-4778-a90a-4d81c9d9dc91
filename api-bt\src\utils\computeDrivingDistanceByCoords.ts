const axios = require('axios');

export async function computeDrivingDistanceByCoords(
    origin: { lat: number; lng: number },
    destination: { lat: number; lng: number }
): Promise<number> {
    const API_KEY = process.env.GOOGLE_MAPS_API_KEY;
    const url = `https://maps.googleapis.com/maps/api/directions/json?origin=${origin.lat},${origin.lng}&destination=${destination.lat},${destination.lng}&mode=driving&units=metric&key=${API_KEY}`;

    try {
        const response = await axios.get(url);

        if (response.data.status !== 'OK') {
            throw new Error(`Google Maps API Error: ${response.data.status}`);
        }

        // Extract the distance from the API response
        const distanceInMeters = response.data.routes[0].legs[0].distance.value;
        return distanceInMeters / 1000;

    } catch (error: any) {
        if (error.response) {
            // HTTP error from the API server
            console.error(`HTTP Error: ${error.response.status}`);
        } else {
            console.error(error);
        }
        throw error;
    }
}
