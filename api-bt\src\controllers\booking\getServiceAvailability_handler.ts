import {Request, Response} from 'express';
import {
    getAllCarTypes,
} from '../../services/car-type';
import {getAllSettings} from "../../services/setting";

export const getServiceAvailabilityHandler = async (_req: Request, res: Response) => {
    try {
        const globalSettings = await getAllSettings();
        let bookingsEnabled = false;
        if(globalSettings && globalSettings.data && globalSettings.data.length > 0) {
            bookingsEnabled = globalSettings.data.find((gs: any) => gs.name === 'public_bookings_enabled')?.value === 'yes';
        }
        const hasCarTypes = Number((await getAllCarTypes(1, 1)).data?.length) > 0

        return res.json({
            success: true,
            message: null,
            errorType: null,
            data: {
                hasCarTypes,
                bookingsEnabled,
                canBook: hasCarTypes && bookingsEnabled,
            },
        });
    } catch (error: any) {
        return res.status(500).json({
            success: false,
            message: error.message,
            errorType: error.code,
            data: null,
        });
    }
};
