import { getRepository } from 'typeorm';
import { Role } from '../../models/Role';
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const deleteRole = async (id: string) => {
  const roleRepository = getRepository(Role);

  try {
    // Delete the role
    const deleteResult = await roleRepository.delete(id);
    if (deleteResult.affected === 0) {
      return { success: false, data: null, error: 'Role not found' };
    }
    return { success: true, data: { id }, error: null };
  } catch (error) {
    const interpretedError = interpretDatabaseError(error);
    return { success: false, data: null, error: interpretedError };
  }
};