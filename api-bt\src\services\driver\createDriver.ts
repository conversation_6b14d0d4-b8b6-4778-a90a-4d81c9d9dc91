import { getRepository } from 'typeorm';
import { Driver } from '../../models/Driver';
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const createDriver = async (data: Partial<Driver>) => {
  const driverRepository = getRepository(Driver);
  try {
    const newDriver = driverRepository.create(data);
    await driverRepository.save(newDriver);
    return { success: true, data: newDriver, error: null };
  } catch (error) {
    const interpretedError = interpretDatabaseError(error);
    return { success: false, data: null, error: interpretedError };
  }
};
