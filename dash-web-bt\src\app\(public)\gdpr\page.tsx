'use client';

import dynamic from "next/dynamic";
const Gdpr = dynamic(
    () => import('./gdpr'),
    { ssr: false }
)
import CurrentBookingDraft from "@/app/(public)/(sections)/CurrentBookingDraft";
import React from "react";
import {FloatingNav} from "@/components/app/LandingNavbar_v2";
import {AiFillHome} from "react-icons/ai";
import {FaInfo} from "react-icons/fa6";
import Link from "next/link";
import {routes} from "@/config/routes";
import i18nTranslations from "@/app/(public)/i18n-translations.json";
import {I18nProvider} from "@/hooks/use-translation";

export default function MultiStepFormPage() {

    const navItems = [
        {
            name: 'home',
            link: '/',
            icon: <AiFillHome className="h-4 w-4 text-neutral-500 dark:text-white"/>,
        },
        {
            name: 'faq',
            link: '/faq',
            icon: <FaInfo className="h-4 w-4 text-neutral-500 dark:text-white"/>,
        },
        {
            name: 'policies',
            link: '/gdpr',
            icon: <FaInfo className="h-4 w-4 text-neutral-500 dark:text-white"/>,
        },
    ];

    return <>
        <I18nProvider translations={i18nTranslations} defaultLanguage="ES">
            {/*/!* Home *!/*/}

            {/* Show active booking, if any */}
            <CurrentBookingDraft/>

            <section className="top-5 inset-x-0 z-50 p-4">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex items-center justify-between h-16">
                        <div className="flex-shrink-0 p-1 cursor-pointer">
                            <Link href={routes.landing}>
                                <img src='/brand/click4transfer-logo.png' alt="Logo" className="h-16"/>
                            </Link>
                        </div>
                        <div className="flex-shrink-0 p-1 cursor-pointer">
                            <Link href={routes.landing}>
                                <h3>← Home</h3>
                            </Link>
                        </div>
                    </div>
                </div>
            </section>

            <div className="relative  w-full">
                <FloatingNav navItems={navItems} buttonLink={'/#book'}/>
            </div>

            <Gdpr/>
        </I18nProvider>
    </>
}
