import { apiClient } from '../core/api-client';
import { User, UserCreate, UserUpdate, UserSchema, UserCreateSchema, UserUpdateSchema } from '../models/user';
import { PaginatedResponse, PaginationParams, createPaginationParams } from '../utils/pagination';
import { ApiResponse } from '../core/types';

/**
 * User service for handling user operations
 */
export class UserService {
  private static instance: UserService;

  private constructor() {}

  /**
   * Get the singleton instance of the UserService
   */
  public static getInstance(): UserService {
    if (!UserService.instance) {
      UserService.instance = new UserService();
    }
    return UserService.instance;
  }

  /**
   * Get all users with pagination
   * @param params Pagination parameters
   * @returns Paginated list of users
   */
  public async getAllUsers(params: PaginationParams = {}): Promise<ApiResponse<PaginatedResponse<User>>> {
    try {
      const paginationParams = createPaginationParams(params);
      const response = await apiClient.get<PaginatedResponse<User>>('/users', paginationParams);
      
      if (response.success && response.data) {
        // Validate each user in the response
        const validatedUsers = response.data.data.map(user => UserSchema.parse(user));
        return {
          success: true,
          data: {
            ...response.data,
            data: validatedUsers,
          },
        };
      }
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: {
          success: false,
          msg: error instanceof Error ? error.message : 'Failed to fetch users',
        },
      };
    }
  }

  /**
   * Get a user by ID
   * @param id User ID
   * @returns User data
   */
  public async getUserById(id: number): Promise<ApiResponse<User>> {
    try {
      const response = await apiClient.get<User>(`/users/${id}`);
      
      if (response.success && response.data) {
        // Validate user data
        const validatedUser = UserSchema.parse(response.data);
        return {
          success: true,
          data: validatedUser,
        };
      }
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: {
          success: false,
          msg: error instanceof Error ? error.message : `Failed to fetch user with ID: ${id}`,
        },
      };
    }
  }

  /**
   * Create a new user
   * @param userData User creation data
   * @returns Created user data
   */
  public async createUser(userData: UserCreate): Promise<ApiResponse<User>> {
    try {
      // Validate user data
      const validatedData = UserCreateSchema.parse(userData);
      
      const response = await apiClient.post<User>('/users', validatedData);
      
      if (response.success && response.data) {
        // Validate response
        const validatedUser = UserSchema.parse(response.data);
        return {
          success: true,
          data: validatedUser,
        };
      }
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: {
          success: false,
          msg: error instanceof Error ? error.message : 'Failed to create user',
        },
      };
    }
  }

  /**
   * Update a user
   * @param id User ID
   * @param userData User update data
   * @returns Updated user data
   */
  public async updateUser(id: number, userData: UserUpdate): Promise<ApiResponse<User>> {
    try {
      // Validate user data
      const validatedData = UserUpdateSchema.parse(userData);
      
      const response = await apiClient.put<User>(`/users/${id}`, validatedData);
      
      if (response.success && response.data) {
        // Validate response
        const validatedUser = UserSchema.parse(response.data);
        return {
          success: true,
          data: validatedUser,
        };
      }
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: {
          success: false,
          msg: error instanceof Error ? error.message : `Failed to update user with ID: ${id}`,
        },
      };
    }
  }

  /**
   * Delete a user
   * @param id User ID
   * @returns API response
   */
  public async deleteUser(id: number): Promise<ApiResponse<{ success: boolean }>> {
    try {
      const response = await apiClient.delete<{ success: boolean }>(`/users/${id}`);
      return response;
    } catch (error) {
      return {
        success: false,
        error: {
          success: false,
          msg: error instanceof Error ? error.message : `Failed to delete user with ID: ${id}`,
        },
      };
    }
  }
}

// Export a singleton instance of the user service
export const userService = UserService.getInstance(); 