import {
  <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, OneToMany,
} from 'typeorm';
import { User } from './User';

@Entity()
export class Role {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date; // Renamed from 'date' to 'created_at'

  @OneToMany(() => User, (user: User) => user.role)
  users: User[];
}
