import {MigrationInterface, QueryRunner, TableColumn} from "typeorm";

export class addeedUseDefaultPpkToCarType1723985226381 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        const table = await queryRunner.getTable('car_type');

        // Check if the 'car_id' column already exists
        const useDefaultPpkColumn = table!.findColumnByName('use_default_ppk');
        if (!useDefaultPpkColumn) {
            // Add the 'car_id' column to the 'booking' table
            await queryRunner.addColumn('car_type', new TableColumn({
                name: 'use_default_ppk',
                type: 'boolean',
                isNullable: false,
                default: false,
            }));
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        const table = await queryRunner.getTable('car_type');

        // Check if the 'use_default_ppk' column exists before attempting to drop it
        const useDefaultPpkColumn = table!.findColumnByName('use_default_ppk');
        if (useDefaultPpkColumn) {
            // Remove the 'use_default_ppk' column from the 'booking' table
            await queryRunner.dropColumn('car_type', 'use_default_ppk');
        }
    }

}
