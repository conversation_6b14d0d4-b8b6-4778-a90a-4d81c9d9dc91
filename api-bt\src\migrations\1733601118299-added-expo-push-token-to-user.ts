import {MigrationInterface, QueryRunner, TableColumn} from "typeorm";

export class addedExpoPushTokenToUser1733601118299 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.addColumn('user', new TableColumn({
            name: 'expo_push_token',
            type: 'varchar',
            isNullable: true,
        }));
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropColumn('user', 'expo_push_token');
    }

}
