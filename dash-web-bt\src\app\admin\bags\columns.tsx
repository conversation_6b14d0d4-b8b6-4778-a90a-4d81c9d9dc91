'use client';

import Link from 'next/link';
import {Text, Badge, Tooltip, Checkbox, ActionIcon} from 'rizzui';
import {HeaderCell} from '@/components/lib/ui/table';
import EyeIcon from '@/components/lib/icons/eye';
import PencilIcon from '@/components/lib/icons/pencil';
import DateCell from '@/components/lib/ui/date-cell';
import DeletePopover from '@/app/shared/lib/delete-popover';
import {routes} from "@/config/routes";

type Columns = {
    data: any[];
    sortConfig?: any;
    handleSelectAll: any;
    checkedItems: string[];
    onDeleteItem: (id: string) => void;
    onHeaderCellClick: (value: string) => void;
    onChecked?: (id: string) => void;
};
export const getColumns = ({
                               data,
                               sortConfig,
                               checkedItems,
                               onDeleteItem,
                               onHeaderCellClick,
                               handleSelectAll,
                               onChecked,
                           }: Columns) => [
    {
        title: (
            <div className="ps-2">
                <Checkbox
                    title={'Select All'}
                    onChange={handleSelectAll}
                    checked={checkedItems.length === data.length}
                    className="cursor-pointer"
                />
            </div>
        ),
        dataIndex: 'checked',
        key: 'checked',
        width: 30,
        render: (_: any, row: any) => (
            <div className="inline-flex ps-2">
                <Checkbox
                    className="cursor-pointer"
                    checked={checkedItems.includes(row.id)}
                    {...(onChecked && { onChange: () => onChecked(row.id) })}
                />
            </div>
        ),
    },
    {
        title: (
            <HeaderCell
                title="Name"
                sortable
                ascending={
                    sortConfig?.direction === 'asc' && sortConfig?.key === 'name'
                }
            />
        ),
        onHeaderCell: () => onHeaderCellClick('name'),
        dataIndex: 'name',
        key: 'name',
        width: 200,
        render: (value: any, row: any) => {
            return <Text className="font-medium text-gray-700 dark:text-gray-600 text-center">
                {value}
            </Text>
        },
    },
    {
        title: (
            <HeaderCell
                title="Description"
                sortable
                ascending={
                    sortConfig?.direction === 'asc' && sortConfig?.key === 'description'
                }
            />
        ),
        onHeaderCell: () => onHeaderCellClick('description'),
        dataIndex: 'description',
        key: 'description',
        width: 200,
        render: (value: any, row: any) => {
            return <Text className="font-medium text-gray-700 dark:text-gray-600 text-center">
                {value}
            </Text>
        },
    },
    {
        title: (
            <HeaderCell
                title="Created At"
                sortable
                ascending={
                    sortConfig?.direction === 'asc' && sortConfig?.key === 'created_at'
                }
            />
        ),
        onHeaderCell: () => onHeaderCellClick('created_at'),
        dataIndex: 'created_at',
        key: 'created_at',
        width: 200,
        render: (value: Date) => <DateCell date={value}/>,
    },
    {
        title: (
            <HeaderCell
                title="Updated At"
                sortable
                ascending={
                    sortConfig?.direction === 'asc' && sortConfig?.key === 'updated_at'
                }
            />
        ),
        onHeaderCell: () => onHeaderCellClick('updated_at'),
        dataIndex: 'updated_at',
        key: 'updated_at',
        width: 200,
        render: (value: Date) => <DateCell date={value}/>,
    },
    {
        title: <></>,
        dataIndex: 'action',
        key: 'action',
        width: 140,
        render: (_: string, row: any) => (
            <div className="flex items-center justify-end gap-3 pe-3">
                <Tooltip
                    size="sm"
                    content={'Edit Bag'}
                    placement="top"
                    color="invert"
                >
                    <Link href={routes.bagEdit(row.id)}>
                        <ActionIcon
                            as="span"
                            size="sm"
                            variant="outline"
                            className="hover:!border-gray-900 hover:text-gray-700"
                        >
                            <PencilIcon className="h-4 w-4"/>
                        </ActionIcon>
                    </Link>
                </Tooltip>
                {/*<Tooltip*/}
                {/*    size="sm"*/}
                {/*    content={'View Bag'}*/}
                {/*    placement="top"*/}
                {/*    color="invert"*/}
                {/*>*/}
                {/*    <Link href={'/'}>*/}
                {/*        <ActionIcon*/}
                {/*            as="span"*/}
                {/*            size="sm"*/}
                {/*            variant="outline"*/}
                {/*            className="hover:!border-gray-900 hover:text-gray-700"*/}
                {/*        >*/}
                {/*            <EyeIcon className="h-4 w-4"/>*/}
                {/*        </ActionIcon>*/}
                {/*    </Link>*/}
                {/*</Tooltip>*/}
                <DeletePopover
                    title={`Delete the bag`}
                    description={`Are you sure you want to delete this #${row.id} bag?`}
                    onDelete={() => onDeleteItem(row.id)}
                />
            </div>
        ),
    },
];
