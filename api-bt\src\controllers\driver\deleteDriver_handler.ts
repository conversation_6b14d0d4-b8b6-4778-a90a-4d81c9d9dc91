// controllers/driver/deleteTicket_handler.ts
import { Request, Response } from 'express';
import { deleteDriver } from '../../services/driver';

export const deleteDriver_handler = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const result = await deleteDriver(id);
    if (!result.success) {
      return res.status(404).json(result);
    }
    return res.json(result);
  } catch (error: any) {
    return res.status(500).json({ error: error.message });
  }
};
