import { z } from 'zod';

/**
 * Customer schema with validation
 */
export const CustomerSchema = z.object({
  id: z.string().uuid(),
  first_name: z.string(),
  last_name: z.string(),
  email: z.string().email(),
  phone: z.string(),
  country_code: z.string().nullable().optional(),
  country: z.string().nullable().optional(),
  city: z.string().nullable().optional(),
  address: z.string().nullable().optional(),
  special_instructions: z.string().nullable().optional(),
  created_at: z.string().or(z.date()).transform(val => new Date(val)),
  updated_at: z.string().or(z.date()).transform(val => new Date(val)),
  updated_by: z.number().nullable().optional(),
});

/**
 * Customer creation schema
 */
export const CustomerCreateSchema = CustomerSchema.omit({ 
  id: true, 
  created_at: true, 
  updated_at: true 
});

/**
 * Customer update schema
 */
export const CustomerUpdateSchema = CustomerSchema
  .partial()
  .omit({ 
    id: true, 
    created_at: true, 
    updated_at: true 
  });

// Type definitions derived from Zod schemas
export type Customer = z.infer<typeof CustomerSchema>;
export type CustomerCreate = z.infer<typeof CustomerCreateSchema>;
export type CustomerUpdate = z.infer<typeof CustomerUpdateSchema>; 