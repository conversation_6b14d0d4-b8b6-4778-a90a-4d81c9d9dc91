import express from 'express';
import {
  createBookingHandler_public,
  getAllBookingsHandler,
  getServiceAvailabilityHandler,
} from '../../controllers/booking';

export const publicBookingRouter = express.Router();

publicBookingRouter.post('/bookings', createBookingHandler_public);
publicBookingRouter.get('/bookings', getAllBookingsHandler);
publicBookingRouter.get('/bookings/service-availability', getServiceAvailabilityHandler);
