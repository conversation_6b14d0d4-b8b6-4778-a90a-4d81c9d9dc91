import { Request, Response } from 'express';
import {
  createBooking,
} from '../../services/booking';

export const createBookingHandler = async (req: Request, res: Response) => {
  try {
    const data = req.body;
    const result = await createBooking(data);
    if (!result.success) {
      return res.status(404).json(result);
    }
    return res.json(result);
  } catch (error: any) {
    return res.status(500).json({
      success: false,
      message: 'Failed to create booking',
      errorType: 'InternalServerError',
      data: undefined,
    });
  }
};
