// services/payment.ts
import { getRepository } from 'typeorm';
import { Payment } from '../../models/Payment'; // Adjust the path as necessary

export const deletePayment = async (id: string) => {
  const paymentRepository = getRepository(Payment);
  try {
    const deleteResult = await paymentRepository.delete(id);
    if (deleteResult.affected === 0) {
      return { success: false, data: null, error: 'Payment not found' };
    }
    return { success: true, data: { id }, error: null };
  } catch (error: any) {
    return { success: false, data: null, error: error.message };
  }
};
