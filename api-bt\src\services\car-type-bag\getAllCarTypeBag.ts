import { getRepository } from 'typeorm';
import { CarTypeBag } from '../../models/CarTypeBag';
import { paginate, PaginatedResult } from '../../utils/pagination';

export const getAllCarType_Bags = async (
    page = 1,
    limit = 10,
): Promise<PaginatedResult<CarTypeBag>> => {
    const carType_BagRepository = getRepository(CarTypeBag);
    const options: any = {
        skip: (page - 1) * limit,
        take: limit,
        order: {
            created_at: 'DESC',  // Order by created_at in descending order
        },
    };

    const [data, totalRecords] = await carType_BagRepository.findAndCount(options);

    return paginate<CarTypeBag>(data, page, limit, totalRecords);
};
