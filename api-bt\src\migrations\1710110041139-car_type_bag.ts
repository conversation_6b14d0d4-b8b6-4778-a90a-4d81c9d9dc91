import {MigrationInterface, QueryRunner, Table, TableForeignKey} from "typeorm";

export class carTypeBag1710110041139 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(
            new Table({
                name: 'car_type_bag',
                columns: [
                    {
                        name: 'id',
                        type: 'uuid',
                        isPrimary: true,
                        isGenerated: true,
                        generationStrategy: 'uuid',
                    },
                    {
                        name: 'bag_id',
                        type: 'uuid',
                    },
                    {
                        name: 'car_type_id',
                        type: 'uuid',
                    },
                    {
                        name: 'quantity',
                        type: 'int',
                    },
                    {
                        name: 'created_at',
                        type: 'timestamp',
                        default: 'CURRENT_TIMESTAMP',
                    },
                    {
                        name: 'updated_at',
                        type: 'timestamp',
                        default: 'CURRENT_TIMESTAMP',
                    },
                ],
            }),
            true,
        );

        await queryRunner.createForeignKey(
            'car_type_bag',
            new TableForeignKey({
                columnNames: ['bag_id'],
                referencedTableName: 'bag',
                referencedColumnNames: ['id'],
                onDelete: 'CASCADE',
            }),
        );

        await queryRunner.createForeignKey(
            'car_type_bag',
            new TableForeignKey({
                columnNames: ['car_type_id'],
                referencedTableName: 'car_type',
                referencedColumnNames: ['id'],
                onDelete: 'CASCADE',
            }),
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropTable('car_type_bag');
    }

}
