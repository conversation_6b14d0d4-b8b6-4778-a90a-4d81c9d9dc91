import { Router } from 'express';
import {
  // createCustomer_handler,
  // deleteCustomer_handler,
  getCustomer_handler,
  getAllCustomers_handler,
  updateCustomer_handler,
} from '../../controllers/customer';

export const driverCustomerRouter = Router();

// driverCustomerRouter.post('/customers', createCustomer_handler);
// driverCustomerRouter.delete('/customers/:id', deleteCustomer_handler);
driverCustomerRouter.get('/customers/:id', getCustomer_handler);
driverCustomerRouter.get('/customers', getAllCustomers_handler);
driverCustomerRouter.put('/customers/:id', updateCustomer_handler);
