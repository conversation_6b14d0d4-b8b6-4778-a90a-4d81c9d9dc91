'use client';

import { routes } from '@/config/routes';
import PageHeader from '@/app/shared/lib/page-header';
import { useRouter } from 'next/navigation';

const pageHeader = {
  title: 'Bookings List',
  breadcrumb: [
    {
      href: routes.dashboard,
      name: 'Home',
    },
    {
      name: 'Bookings List',
    },
  ],
};

interface HeaderProps {
  className?: string;
}

export default function BookingsListPageHeader({ className }: HeaderProps) {
  const router = useRouter();

  let handleCreateBooking = () => {
    router.push(routes.bookingCreate); // Replace '/path-to-navigate' with your target path
  };

  return (
    <PageHeader title={pageHeader.title} breadcrumb={pageHeader.breadcrumb}>
      <div className="mt-4 flex flex-col items-center gap-3 @sm:flex-row @lg:mt-0">
        {/*<ExportButton*/}
        {/*  data={appointmentData}*/}
        {/*  fileName="appointment_data"*/}
        {/*  header="ID,Patient,Doctor,Service Type,Date,Status,Payment,Duration"*/}
        {/*/>*/}
        {/*<Button className="w-full @lg:w-auto" onClick={handleCreateBooking}>*/}
        {/*  <PiPlusBold className="me-1.5 h-[17px] w-[17px]" />*/}
        {/*  Create Booking*/}
        {/*</Button>*/}
      </div>
    </PageHeader>
  );
}
