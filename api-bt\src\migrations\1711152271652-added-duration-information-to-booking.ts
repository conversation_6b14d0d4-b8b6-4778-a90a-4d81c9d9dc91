import {MigrationInterface, QueryRunner, TableColumn} from "typeorm";

export class addedDurationInformationToBooking1711152271652 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        const bookingTable = await queryRunner.getTable('booking');
        if (bookingTable) {
            if (!bookingTable.columns.find(column => column.name === 'trip_duration')) {
                await queryRunner.addColumn('booking', new TableColumn({
                    name: 'trip_duration',
                    type: 'integer',
                }));
            }
            if (!bookingTable.columns.find(column => column.name === 'return_trip_duration')) {
                await queryRunner.addColumn('booking', new TableColumn({
                    name: 'return_trip_duration',
                    type: 'integer',
                }));
            }
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropColumn('booking', 'trip_duration');
        await queryRunner.dropColumn('booking', 'return_trip_duration');
    }

}
