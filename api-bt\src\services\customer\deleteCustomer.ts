import { getRepository } from 'typeorm';
import { Customer } from '../../models/Customer'; // Update the path according to your project structure
import { interpretDatabaseError } from '../../utils/interpretDatabaseError'; // Adjust import path as needed

export const deleteCustomer = async (id: string) => {
  const customerRepository = getRepository(Customer);
  try {
    const deleteResult = await customerRepository.delete(id);
    if (deleteResult.affected === 0) {
      return { success: false, error: 'Customer not found' };
    }
    return { success: true, id };
  } catch (error) {
    return { success: false, error: interpretDatabaseError(error) };
  }
};
