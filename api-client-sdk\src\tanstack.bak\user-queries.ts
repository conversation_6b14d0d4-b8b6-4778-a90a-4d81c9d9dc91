import { 
  useQuery, 
  useMutation, 
  useQueryClient,
  UseQueryOptions,
  UseQueryResult,
  UseMutationOptions,
  UseMutationResult,
} from 'react-query';
import { userService } from '../services/user-service';
import { User, UserCreate, UserUpdate } from '../models/user';
import { PaginatedResponse, PaginationParams } from '../utils/pagination';
import { ApiResponse } from '../core/types';

// Query keys
export const userKeys = {
  all: ['users'] as const,
  lists: () => [...userKeys.all, 'list'] as const,
  list: (params: PaginationParams) => [...userKeys.lists(), params] as const,
  details: () => [...userKeys.all, 'detail'] as const,
  detail: (id: number) => [...userKeys.details(), id] as const,
};

/**
 * React Query hook for fetching users with pagination
 * @param params Pagination parameters
 * @param options Query options
 * @returns Query result for paginated users
 */
export const useUsers = (
  params: PaginationParams = {},
  options?: UseQueryOptions<ApiResponse<PaginatedResponse<User>>, Error>
): UseQueryResult<ApiResponse<PaginatedResponse<User>>, Error> => {
  return useQuery<ApiResponse<PaginatedResponse<User>>, Error>(
    userKeys.list(params),
    () => userService.getAllUsers(params),
    {
      keepPreviousData: true,
      ...options,
    }
  );
};

/**
 * React Query hook for fetching a user by ID
 * @param id User ID
 * @param options Query options
 * @returns Query result for user details
 */
export const useUser = (
  id: number,
  options?: UseQueryOptions<ApiResponse<User>, Error>
): UseQueryResult<ApiResponse<User>, Error> => {
  return useQuery<ApiResponse<User>, Error>(
    userKeys.detail(id),
    () => userService.getUserById(id),
    {
      enabled: !!id,
      ...options,
    }
  );
};

/**
 * React Query hook for creating a user
 * @param options Mutation options
 * @returns Mutation result for user creation
 */
export const useCreateUser = (
  options?: UseMutationOptions<ApiResponse<User>, Error, UserCreate>
): UseMutationResult<ApiResponse<User>, Error, UserCreate> => {
  const queryClient = useQueryClient();
  
  return useMutation<ApiResponse<User>, Error, UserCreate>(
    (userData) => userService.createUser(userData),
    {
      ...options,
      onSuccess: (data, variables, context) => {
        // Invalidate users list queries
        queryClient.invalidateQueries(userKeys.lists());
        
        // Call the provided onSuccess if it exists
        if (options?.onSuccess) {
          options.onSuccess(data, variables, context);
        }
      },
    }
  );
};

/**
 * React Query hook for updating a user
 * @param options Mutation options
 * @returns Mutation result for user update
 */
export const useUpdateUser = (
  options?: UseMutationOptions<ApiResponse<User>, Error, { id: number; data: UserUpdate }>
): UseMutationResult<ApiResponse<User>, Error, { id: number; data: UserUpdate }> => {
  const queryClient = useQueryClient();
  
  return useMutation<ApiResponse<User>, Error, { id: number; data: UserUpdate }>(
    ({ id, data }) => userService.updateUser(id, data),
    {
      ...options,
      onSuccess: (data, variables, context) => {
        // Invalidate specific user query
        queryClient.invalidateQueries(userKeys.detail(variables.id));
        // Invalidate users list queries
        queryClient.invalidateQueries(userKeys.lists());
        
        // Call the provided onSuccess if it exists
        if (options?.onSuccess) {
          options.onSuccess(data, variables, context);
        }
      },
    }
  );
};

/**
 * React Query hook for deleting a user
 * @param options Mutation options
 * @returns Mutation result for user deletion
 */
export const useDeleteUser = (
  options?: UseMutationOptions<ApiResponse<{ success: boolean }>, Error, number>
): UseMutationResult<ApiResponse<{ success: boolean }>, Error, number> => {
  const queryClient = useQueryClient();
  
  return useMutation<ApiResponse<{ success: boolean }>, Error, number>(
    (id) => userService.deleteUser(id),
    {
      ...options,
      onSuccess: (data, variables, context) => {
        // Invalidate deleted user query
        queryClient.invalidateQueries(userKeys.detail(variables));
        // Invalidate users list queries
        queryClient.invalidateQueries(userKeys.lists());
        
        // Call the provided onSuccess if it exists
        if (options?.onSuccess) {
          options.onSuccess(data, variables, context);
        }
      },
    }
  );
}; 