import {useUpdateTicket} from '@/services/mutations/ticket/admin';
import toast from 'react-hot-toast';
import {PiTicket} from 'react-icons/pi';
import {Title, Text, Button, Loader} from 'rizzui';
import {useGetBookingByBookingCode} from "@/services/query/booking/admin/useGetBookingByBookingCode";
import React, {useEffect, useState} from "react";
import {routes} from "@/config/routes";

export default function ExpandedOrderRow({
                                             rowData: {
                                                 customer,
                                                 car,
                                                 message,
                                                 canceled,
                                                 resolved,
                                                 id,
                                                 booking_code
                                             }
                                         }: any) {

    const [displayBookingInfo, setDisplayBookingInfo] = useState(false);

    const {
        mutate: updateMutation,
        isPending: isLoading,
    } = useUpdateTicket();

    const {
        // isPending,
        // isError,
        // error,
        data: bookingData,
        isFetching: isBookingFetching,
        // isPlaceholderData,
        // refetch,
        // status,
    } = useGetBookingByBookingCode(booking_code);

    const resolveTicket = () => {
        updateMutation({
                id, payload: {
                    resolved: true,
                }
            },
            {
                onSuccess: () => {
                    toast.success('Ticket Updated Successfully');
                },
                onError: (error) => {
                    console.log('error', error)
                    toast.error('Error updating ticket');
                }
            }
        )
    }

    const triggerDisplayBookingInfo = () => {
        setDisplayBookingInfo(!displayBookingInfo);
    }

    const editBooking = () => {
        bookingData && bookingData.data && routes.bookingEdit(bookingData.data.id)
    }

    if (!customer) {
        return <Text>No customer data available.</Text>;
    }

    return (
        <div className="grid grid-cols-1 divide-y bg-gray-0 px-3.5 dark:bg-gray-50">
            <article
                key={customer.id}
                className="flex items-center justify-between py-6 first-of-type:pt-2.5 last-of-type:pb-2.5"
            >
                <div className="flex items-start">
                    <header>
                        <Title as="h4" className="mb-0.5 text-sm font-medium mt-2">
                            Name: {customer.first_name} {customer.last_name}
                        </Title>
                        <Text className="mb-1 text-gray-500 font-medium mt-2">{customer.stripe_customer_id}</Text>
                        <Text className="text-xs text-gray-500 font-medium mt-2">
                            Phone number: {customer.phone_number}
                        </Text>
                        {
                            booking_code ? (
                                <Text className="text-xs text-deepOrange font-medium mt-2">
                                    Booking Code: {booking_code}
                                </Text>
                            ) : null
                        }
                        <Text className="text-xs text-gray-500 mt-2 font-medium">
                            Canceled: {canceled ? <span className="text-red-500">Yes</span> :
                            <span className="text-green-500">No</span>}
                        </Text>
                        <Text className="text-xs text-gray-500 mt-2">
                            Resolved: {resolved ? <span className="text-green-500">Yes</span> :
                            <span className="text-red-500">No</span>}
                        </Text>
                        {
                            displayBookingInfo && bookingData ? (
                                <div className="mt-4">
                                    <Title as="h5" className="text-sm font-medium">
                                        Booking Details
                                    </Title>
                                    <Text className="text-xs text-gray-500 mt-2">
                                        Pickup Address: {bookingData.data.pickup_address}
                                    </Text>
                                    <Text className="text-xs text-gray-500 mt-2">
                                        Destination Address: {bookingData.data.destination_address}
                                    </Text>
                                    <Text className="text-xs text-gray-500 mt-2">
                                        Trip Type: {bookingData.data.trip_type}
                                    </Text>
                                    <Text className="text-xs text-gray-500 mt-2">
                                        Scheduled At: {new Date(bookingData.data.scheduled_at).toLocaleString()}
                                    </Text>
                                    <Text className="text-xs text-gray-500 mt-2">
                                        Status: {bookingData.data.status}
                                    </Text>
                                    <a className="text-xs text-blue-500 mt-2" onClick={editBooking}>
                                        Edit Booking
                                    </a>
                                </div>
                            ) : null
                        }
                        <Text className="text-xs text-gray-500 font-medium mt-2">
                            Message: {message}
                        </Text>
                        {
                            resolved ? null : (
                                <>
                                    <div>
                                        <Button
                                            type="button"
                                            isLoading={isLoading}
                                            onClick={resolveTicket}
                                            className="w-full md:w-auto bg-transparent border-dashed-deepOrange text-sky-950 hover:bg-black hover:text-white mr-2 mt-6">
                                            <PiTicket className="me-1 h-[17px] w-[17px]"/>
                                            Resolve
                                        </Button>
                                    </div>
                                    {
                                        booking_code ? (
                                            <>
                                                {
                                                    !isBookingFetching ? (
                                                        <div>
                                                            <Button
                                                                type="button"
                                                                isLoading={isLoading}
                                                                onClick={triggerDisplayBookingInfo}
                                                                className="w-full md:w-auto bg-transparent border-dashed-deepOrange text-sky-950 hover:bg-black hover:text-white mr-2 mt-6">
                                                                <PiTicket className="me-1 h-[17px] w-[17px]"/>
                                                                Show Booking
                                                            </Button>
                                                        </div>
                                                    ) : <div className="grid h-32 flex-grow place-content-center items-center">
                                                        <Loader size="sm"/>
                                                    </div>
                                                }
                                            </>
                                        ) : null
                                    }
                                </>
                            )
                        }
                    </header>
                </div>
            </article>
        </div>
    );
}
