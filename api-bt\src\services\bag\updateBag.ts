import { getRepository } from 'typeorm';
import { Bag } from '../../models/Bag'; // Adjust the path as necessary
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const updateBag = async (id: string, data: Partial<Bag>) => {
  const bagRepository = getRepository(Bag);
  try {
    const bag = await bagRepository.findOne(id);
    if (!bag) {
      return { success: false, data: null, error: 'Bag not found' };
    }

    bagRepository.merge(bag, data);
    const updatedBag = await bagRepository.save(bag);
    return { success: true, data: updatedBag, error: null };
  } catch (error) {
    const interpretedError = interpretDatabaseError(error);
    return { success: false, data: null, error: interpretedError };
  }
};
