import {Controller, useFormContext} from 'react-hook-form';
import FormGroup from '@/app/shared/lib/form-group';
import {Checkbox, Input, Select, Text} from 'rizzui';
import NoSSR from '@/components/lib/no-ssr';
import {
    tripTypes,
} from '@/app/shared/lib/logistics/shipment/create-edit/select-options';
import {PiCar} from "react-icons/pi";
import {DatePicker} from "@/components/lib/ui/datepicker";
import moment from "moment";
import React, {useEffect, useState} from "react";
import Autocomplete from "@/components/lib/google-map/autocomplete";
import {SearchableSelect} from "@/components/app/searchable-select";
import {usePaginatedCarTypes} from "@/services/query/car-type/admin/usePaginatedCarTypes";
import {useModal} from '@/app/shared/lib/modal-views/use-modal';
import cn from "@/utils/class-names";
import { CarTypeSchemaType } from "@/utils/validators/app/entities";
import BagSelectionComponent, { BagWithQuantity, ReturnBagQuantities } from "@/app/shared/app/bookings/create-edit/sections/BagSelectionComponent";

interface JourneyDetailsProps {
    className?: string;
}

export default function JourneyDetails({
                                           className,
                                       }: JourneyDetailsProps) {
    const {
        control,
        setValue,
        getValues,
        formState: {errors},
        register,
        watch,
    } = useFormContext();

    const [isTwoWayTrip, setIsTwoWayTrip] = useState(() => false);
    const [destinationLatLng, setDestinationLatLng] = useState(() => [0, 0]);
    const [pickupLatLng, setPickupLatLng] = useState(() => [0, 0]);
    const [carTypeSearchString, setCarTypeSearchString] = useState(() => '');
    const [selectedCarType, setSelectedCarType] = useState<CarTypeSchemaType | null>(() => null);

    const [scheduledAt, setScheduledAt] = useState<Date | null>(null);
    const [returnTripTime, setReturnTripTime] = useState<Date | null>(null);

    const tripType = watch('trip_type');

    const {
        isPending,
        isError,
        error,
        data: carTypes,
        isFetching,
        isPlaceholderData,
        refetch,
        status,
    } = usePaginatedCarTypes(1, 30, {
        name: carTypeSearchString,
    });

    const carTypesOptions = carTypes?.data.map((val) => {
        return {
            value: val.id,
            label: val.name
        }
    }) || [];

    const searchCarType = (searchVal: string) => {
        setCarTypeSearchString(searchVal);
    }
    const setAddress = (type: string, place: any) => {
        setValue(`${type}_address`, place.formatted_address);
        setValue(`${type}_lat`, place.geometry.location.lat().toString());
        setValue(`${type}_long`, place.geometry.location.lng().toString());

        if (type === 'pickup') {
            setPickupLatLng([place.geometry.location.lat(), place.geometry.location.lng()]);
        } else {
            setDestinationLatLng([place.geometry.location.lat(), place.geometry.location.lng()]);
        }
    };

    function setScheduleBookingDate(date: Date) {
        setValue('scheduled_at', moment(date).toISOString());
        setScheduledAt(date);
    }

    function setReturnTripDate(date: Date) {
        setValue('return_trip_time', moment(date).toISOString());
        setReturnTripTime(date);
    }

    function onRouteComputed() {
        const route = arguments[0];
        // distance in km rounded to 2 decimals
        const distance = +(route.legs[0].distance.value / 1000).toFixed(2);
        setValue('trip_distance', distance);
        setValue('return_trip_distance', distance);
        // duration in minutes rounded
        const duration = Math.round(route.legs[0].duration.value / 60);
        setValue('trip_duration', duration);
        setValue('return_trip_duration', duration);
    }

    function onCarTypeSelect(d: any[], onChange: any) {
        const carType = carTypes?.data.find((c) => c.id === d);
        setSelectedCarType(carType);
        onChange(d)
    }

    function onSelectionChange(selectedBags: ReturnBagQuantities[]) {
        setValue('bags', selectedBags);
    }

    useEffect(() => {
        refetch().then(r => {});
    }, [carTypeSearchString])

    useEffect(() => {
        const pickupLat = getValues('pickup_lat');
        const pickupLong = getValues('pickup_long');
        const destinationLat = getValues('destination_lat');
        const destinationLong = getValues('destination_long');

        if(pickupLat && pickupLong) {
            setPickupLatLng([pickupLat, pickupLong])
        }

        if(destinationLat && destinationLong) {
            setDestinationLatLng([destinationLat, destinationLong])
        }
    }, [])

    return (
        <FormGroup
            title="Journey Details"
            description="Journey details here"
            className={cn(className)}
        >
            <NoSSR>
                <Controller
                    control={control}
                    name="trip_type"
                    render={({field: {value, onChange}}) => (
                        <Select
                            disabled={true}
                            label="Trip Type"
                            className="col-span-full"
                            labelClassName="text-gray-900"
                            dropdownClassName="p-2 gap-1 grid !z-10"
                            inPortal={false}
                            value={value}
                            onChange={(d) => {
                                setValue('estimated_service_hours', null);
                                onChange(d);
                            }}
                            options={tripTypes}
                            getOptionValue={(option) => option.value}
                            displayValue={(selected: string) =>
                                tripTypes?.find((c) => c.value === selected)?.label ?? ''
                            }
                            error={errors?.trip_type?.message as string}
                        />
                    )}
                />
            </NoSSR>

            {
                tripType === 'transfer' && (
                    <NoSSR>
                        <div className={'mt-3 col-span-full'}>
                            <Controller
                                name="one_way_trip"
                                render={({field: {value, onChange}}) => {
                                    return (
                                        <Checkbox
                                            disabled={true}
                                            checked={value}
                                            value={value}
                                            onChange={(v) => {
                                                setIsTwoWayTrip(value);
                                                onChange(v);
                                            }}
                                            label={
                                                <span className="flex items-center gap-1">
                                        One-Way Trip
                                        <PiCar className="h-6 w-6"/>
                                      </span>
                                            }
                                            size="sm"
                                            className="-mt-2 [&_svg]:top-0"
                                        />
                                    )
                                }}
                            />
                        </div>

                        <div className="flex flex-col space-y-2">
                            <label htmlFor="autocomplete" className="text-sm font-medium text-gray-700">
                                Pickup Address</label>
                            <Autocomplete
                                disabled={true}
                                apiKey={process.env.NEXT_PUBLIC_GOOGLE_MAP_API_KEY as string}
                                mapClassName="rounded-lg"
                                spinnerClassName="grid h-full w-full place-content-center"
                                className="rounded-lg bg-gray-50"
                                hideMap={true}
                                onPlaceSelect={(place) => setAddress('pickup', place)}
                                defaultInputValue={getValues('pickup_address')}
                                startLocation={[getValues('pickup_lat'), getValues('pickup_long')]}
                            />
                        </div>

                        <div className="flex flex-col space-y-2">
                            <label htmlFor="autocomplete" className="text-sm font-medium text-gray-700">
                                Destination Address</label>
                            <Autocomplete
                                disabled={true}
                                apiKey={process.env.NEXT_PUBLIC_GOOGLE_MAP_API_KEY as string}
                                mapClassName="rounded-lg"
                                spinnerClassName="grid h-full w-full place-content-center"
                                className="rounded-lg bg-gray-50"
                                hideMap={true}
                                onPlaceSelect={(place) => setAddress('destination', place)}
                                defaultInputValue={getValues('destination_address')}
                                startLocation={[getValues('destination_lat'), getValues('destination_long')]}
                            />
                        </div>

                        {
                            !!destinationLatLng[0] && !!pickupLatLng[0] && (
                                <Autocomplete
                                    disabled={true}
                                    apiKey={process.env.NEXT_PUBLIC_GOOGLE_MAP_API_KEY as string}
                                    hideInput={true}
                                    mapClassName="rounded-lg"
                                    spinnerClassName="grid h-full w-full place-content-center"
                                    className="relative h-[500px] w-full flex-grow rounded-lg bg-gray-50 col-span-full"
                                    startLocation={pickupLatLng}
                                    endLocation={destinationLatLng}
                                    onRouteComputed={onRouteComputed}
                                />
                            )
                        }


                        <Input
                            step={0.01}
                            label="Calculated Trip Distance (KM)"
                            labelClassName="font-medium text-gray-900"
                            placeholder="0"
                            type={'number'}
                            disabled={true}
                            {...register('trip_distance', {valueAsNumber: true})}
                            error={errors.trip_distance?.message as string}
                        />

                        <Input
                            step={0.01}
                            label="Calculated Trip Duration (minutes)"
                            labelClassName="font-medium text-gray-900"
                            placeholder="0"
                            type={'number'}
                            disabled={true}
                            {...register('trip_duration', {valueAsNumber: true})}
                            error={errors.trip_duration?.message as string}
                        />

                        {
                            isTwoWayTrip && (
                                <>
                                    <Input
                                        step={0.01}
                                        label="Calculated Return Trip Distance (KM)"
                                        labelClassName="font-medium text-gray-900"
                                        placeholder="0"
                                        type={'number'}
                                        disabled={true}
                                        {...register('return_trip_distance', {valueAsNumber: true})}
                                        error={errors.return_trip_distance?.message as string}
                                    />

                                    <Input
                                        step={0.01}
                                        label="Calculated Return Trip Duration (minutes)"
                                        labelClassName="font-medium text-gray-900"
                                        placeholder="0"
                                        type={'number'}
                                        disabled={true}
                                        {...register('return_trip_duration', {valueAsNumber: true})}
                                        error={errors.return_trip_duration?.message as string}
                                    />
                                </>
                            )
                        }
                    </NoSSR>
                )
            }

            <div>
                <Text as="span" className="mb-2 block text-sm rizzui-select-label block text-sm mb-1.5 font-medium text-gray-900">
                    Schedule Booking Date
                </Text>
                <Controller
                    name="scheduled_at"
                    render={({field: {value, onChange}}) => {
                        return (
                            <>
                                <DatePicker
                                    id={'scheduled_at'}
                                    // disabled={true}
                                    selected={new Date(value)}
                                    onChange={(date: Date) => {
                                        setScheduleBookingDate(date)
                                        onChange(date.toISOString())
                                    }}
                                    // startDate={new Date()}
                                    minDate={moment().add(1, 'days').toDate()}
                                    placeholderText="Schedule Booking"
                                    showTimeSelect
                                    dateFormat='d MMMM yyyy h:mm aa'
                                    timeFormat='HH:mm'
                                    timeIntervals={15}
                                />
                                <label className='text-red text-[13px] mt-0.5 rizzui-input-error-text' htmlFor="scheduled_at">{errors?.scheduled_at?.message as string}</label>
                            </>
                        )
                    }}
                />

            </div>

            {
                isTwoWayTrip && tripType === 'transfer' && (
                    <div>
                        <Text as="span" className="mb-2 block text-sm">
                            Return Trip Date
                        </Text>
                        <DatePicker
                            selected={returnTripTime}
                            onChange={(date: Date) => setReturnTripDate(moment(date).add(2, 'hours').toDate())}
                            startDate={new Date()}
                            minDate={moment().add(1, 'days').toDate()}
                            placeholderText="Select Return Trip Date"
                            showTimeSelect
                            timeIntervals={15}
                        />
                    </div>
                )
            }

            {
                tripType === 'hourly' && (
                    <Input
                        label="Estimated Service Hours"
                        labelClassName="font-medium text-gray-900"
                        placeholder="0"
                        type={'number'}
                        {...register('estimated_service_hours', {valueAsNumber: true})}
                        error={errors.estimated_service_hours?.message as string}
                    />
                )
            }

            <div>
                <Text as="span" className="rizzui-select-label block text-sm mb-1.5 font-medium text-gray-900">
                    Car Type
                </Text>

                <Controller
                    control={control}
                    name="car_type"
                    render={({field: {value, onChange}}) => (
                        <SearchableSelect
                            disabled={true}
                            data={carTypesOptions}
                            isFetching={isFetching}
                            value={value}
                            onChange={(d) => { onCarTypeSelect(d, onChange) }}
                        />
                    )}
                />
            </div>

            <div className="col-span-full">
                <BagSelectionComponent bags={selectedCarType?.bags as BagWithQuantity[]} onSelectionChange={onSelectionChange} />
            </div>

        </FormGroup>
    );
}
