// FeaturesBenefitsSection.tsx
import React from 'react';
import Container from '@/app/(public)/(sections)/(old)/container';
import { TbPremiumRights } from "react-icons/tb";
import { FaLanguage, FaCarAlt } from 'react-icons/fa'; // Multilingual and car icons
import { GiLargePaintBrush } from 'react-icons/gi'; // Customization icon

const FeaturesBenefitsSection: React.FC = () => {
    return (
        <section className="py-16 text-white">
            <Container>
                <div className="text-center mb-12">
                    <h2 className="text-3xl font-semibold mb-4 text-chillGold">
                        Por qué elegirnos
                    </h2>
                    <p className="max-w-xl mx-auto">
                        Descubre las ventajas de viajar con nosotros.
                    </p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <div className="text-center">
                        <TbPremiumRights className="mx-auto text-5xl text-primary-500 mb-4" />
                        <h3 className="text-xl font-semibold mb-2 text-chillGold" >Calidad Superior</h3>
                        <p>Mantenemos los más altos estándares en cada viaje.</p>
                    </div>
                    <div className="text-center">
                        <FaLanguage className="mx-auto text-5xl text-primary-500 mb-4" />
                        <h3 className="text-xl font-semibold mb-2 text-chillGold">Multilingües</h3>
                        <p>Nuestro equipo habla varios idiomas para su comodidad.</p>
                    </div>
                    <div className="text-center">
                        <FaCarAlt className="mx-auto text-5xl text-primary-500 mb-4" />
                        <h3 className="text-xl font-semibold mb-2 text-chillGold">Flota Moderna</h3>
                        <p>Vehículos de lujo equipados con la última tecnología.</p>
                    </div>
                    <div className="text-center">
                        <GiLargePaintBrush className="mx-auto text-5xl text-primary-500 mb-4" />
                        <h3 className="text-xl font-semibold mb-2 text-chillGold">Personalización</h3>
                        <p>Adaptamos nuestros servicios a sus necesidades únicas.</p>
                    </div>
                </div>
            </Container>
        </section>
    );
};

export default FeaturesBenefitsSection;
