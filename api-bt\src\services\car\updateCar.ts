import { getRepository } from 'typeorm';
import { Car } from '../../models/Car';
import { Driver } from '../../models/Driver';
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const updateCar = async (id: string, data: Partial<Car>, driverIds: string[]) => {
  const carRepository = getRepository(Car);
  const driverRepository = getRepository(Driver);

  try {
    const car = await carRepository.findOne(id, { relations: ['drivers'] });
    if (!car) {
      return { success: false, data: null, error: 'Car not found' };
    }

    // Merge new data into the existing car
    carRepository.merge(car, data);

    // Retrieve and associate drivers by their IDs
    if (driverIds && driverIds.length > 0) {
      const drivers = await driverRepository.findByIds(driverIds);
      car.drivers = drivers;
    }

    const updatedCar = await carRepository.save(car);
    return { success: true, data: updatedCar, error: null };
  } catch (error) {
    const interpretedError = interpretDatabaseError(error);
    return { success: false, data: null, error: interpretedError };
  }
};