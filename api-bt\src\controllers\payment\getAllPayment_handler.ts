import { Request, Response } from 'express';
import {
    getAllPayments,
} from '../../services/payment'; // Adjust the import path as necessary

export const getAllPaymentsHandler = async (req: Request, res: Response) => {
    try {
        const page = parseInt(req.query.page as string, 10) || 1;
        const limit = parseInt(req.query.limit as string, 10) || 10;
        const result = await getAllPayments(page, limit);
        return res.json(result);
    } catch (error: any) {
        return res.status(500).json({
            success: false,
            message: 'Failed to get payments',
            error: error.message,
        });
    }
};
