import {connection} from "../../server/database";
import { Setting } from '../../models/Setting'; // Adjust the path as necessary

export const resetAllSettings = async (): Promise<{
  success: boolean,
  message: string,
}> => {
  try {
    if(connection) {
      await connection
          .createQueryBuilder()
          .delete()
          .from(Setting)
          .execute();
      return {
        success: true,
        message: 'Cleaned all settings.',
      }
    } else {
      return {
        success: false,
        message: 'Database connection failed.',
      }
    }
  } catch(e: any) {
    console.log('ERROR in resetting settings: ', e);
    return {
      success: false,
      message:  `Error: ${e.message}`,
    }
  }
};




