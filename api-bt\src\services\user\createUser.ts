import { getRepository } from 'typeorm';
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';
import { User } from '../../models/User';
import bcrypt from 'bcryptjs';
import { userSchema } from '../../routes/admin/auth'; // Assuming you have a validation schema  
import { Role } from '../../models/Role';
import { Driver } from '../../models/Driver';
import { sendMail } from './sendMail';

export const createUser = async (data: Partial<User & { confirmed_password: string, role?: string }>) => {
  const userRepository = getRepository(User);
  const driverRepository = getRepository(Driver);

  // Joy Validation
  const result = userSchema.validate(data);

  if (result.error) {
    return { success: false, data: null, error: `Validation err: ${result.error.details[0].message}` };
  }

  if (!data.password || !data.confirmed_password) {
    return { success: false, data: null, error: 'Password and Confirm Password are required' };
  }

  if (data.password !== data.confirmed_password) {
    return { success: false, data: null, error: 'Password and Confirm Password do not match' };
  }

  delete data.confirmed_password;

  const { email, password } = data;

  try {

    const existingUser = await userRepository.findOne({ email });

    if (existingUser) {
      return { success: false, data: null, error: 'Email already exists' };
    }
    const roleRepository = getRepository(Role);

    if(!data.role) {
      return { success: false, data: null, error: 'Role is required' };
    }

    const role = await roleRepository.findOne(data.role?.id);

    if (!role) {
      return { success: false, data: null, error: 'Role not found' };
    }

    const salt = await bcrypt.genSalt(10);
    const hash = await bcrypt.hash(password, salt);

    const query = {
      ...data,
      role_id: role.id,
      password: hash,
    };

    const newUser = await userRepository.save(query);

    if(newUser) {
      await sendMail(newUser.email, newUser.first_name, password);
    }

    if (newUser.role.name === 'driver') {
      const newDriver = driverRepository.create({
        first_name: data.first_name,
        last_name: data.last_name,
        user: newUser,
      });

      await driverRepository.save(newDriver);

      newUser.driver = newDriver;
      await userRepository.save(newUser);
    }

    return { success: true, data: newUser, error: null };

  } catch (error) {
    const interpretedError = interpretDatabaseError(error);
    return { success: false, data: null, error: interpretedError.message };
  }
};
