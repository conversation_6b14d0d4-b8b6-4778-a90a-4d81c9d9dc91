// Make Jest available globally for Bun
import { jest } from '@jest/globals';
global.jest = jest;

// Setup Axios mock
jest.mock('axios', () => ({
  create: jest.fn(() => ({
    defaults: {
      headers: {
        common: {}
      }
    },
    interceptors: {
      request: {
        use: jest.fn(),
        eject: jest.fn()
      },
      response: {
        use: jest.fn(),
        eject: jest.fn()
      }
    },
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
    patch: jest.fn()
  }))
}));

// Add a longer timeout for tests to account for async operations
jest.setTimeout(10000);

// Add global mocks or setup code here
global.console = {
  ...console,
  // Uncomment to ignore specific console logs during tests
  // log: jest.fn(),
  // error: jest.fn(),
  // warn: jest.fn(),
}; 