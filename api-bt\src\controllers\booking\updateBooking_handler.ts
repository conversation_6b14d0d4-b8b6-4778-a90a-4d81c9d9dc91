import { Request, Response } from 'express';
import {
  updateBooking,
} from '../../services/booking';

export const updateBookingHandler = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    const result = await updateBooking(id, updateData);
    return res.status(200).json(result);
  } catch (error: any) {
    return res.status(500).json({ error: error.message });
  }
};
