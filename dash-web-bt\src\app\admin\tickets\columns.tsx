'use client';

import Link from 'next/link';
import {Text, Badge, Tooltip, Checkbox, ActionIcon} from 'rizzui';
import {HeaderCell} from '@/components/lib/ui/table';
import EyeIcon from '@/components/lib/icons/eye';
import PencilIcon from '@/components/lib/icons/pencil';
import DateCell from '@/components/lib/ui/date-cell';
import DeletePopover from '@/app/shared/lib/delete-popover';

type Columns = {
    data: any[];
    sortConfig?: any;
    handleSelectAll: any;
    checkedItems: string[];
    onDeleteItem: (id: string) => void;
    onHeaderCellClick: (value: string) => void;
    onChecked?: (id: string) => void;
};

function getResolvedBadge(status: boolean) {
    switch (status) {
        case true:
            return (
                <div className="flex items-center">
                    <Badge color="success" renderAsDot />
                    <Text className="ms-2 font-medium text-green-dark">{status} Resolved</Text>
                </div>
            );
        case false:
            return (
                <div className="flex items-center">
                    <Badge color="danger" renderAsDot />
                    <Text className="ms-2 font-medium text-red-dark">{status} Unresolved</Text>
                </div>
            );
        default:
            return (
                <div className="flex items-center">
                    <Badge renderAsDot className="bg-gray-400" />
                    <Text className="ms-2 font-medium text-gray-600">{status} No Status</Text>
                </div>
            );
    }
}

function getCanceledBadge(status: boolean) {
    switch (status) {
        case true:
            return (
                <div className="flex items-center">
                    <Badge color="danger" renderAsDot />
                    <Text className="ms-2 font-medium text-red-dark">{status} Canceled</Text>
                </div>
            );
        case false:
            return (
                <div className="flex items-center">
                    <Badge color="success" renderAsDot />
                    <Text className="ms-2 font-medium text-green-dark">{status} Allowed</Text>
                </div>
            );
        default:
            return (
                <div className="flex items-center">
                    <Badge renderAsDot className="bg-gray-400" />
                    <Text className="ms-2 font-medium text-gray-600">{status} No Status</Text>
                </div>
            );
    }
}
export const getColumns = ({
                               data,
                               sortConfig,
                               checkedItems,
                               onDeleteItem,
                               onHeaderCellClick,
                               handleSelectAll,
                               onChecked,
                           }: Columns) => [
    // {
    //     title: (
    //         <div className="ps-2">
    //             <Checkbox
    //                 title={'Select All'}
    //                 onChange={handleSelectAll}
    //                 checked={checkedItems.length === data.length}
    //                 className="cursor-pointer"
    //             />
    //         </div>
    //     ),
    //     dataIndex: 'checked',
    //     key: 'checked',
    //     width: 30,
    //     render: (_: any, row: any) => (
    //         <div className="inline-flex ps-2">
    //             <Checkbox
    //                 className="cursor-pointer"
    //                 checked={checkedItems.includes(row.id)}
    //                 {...(onChecked && { onChange: () => onChecked(row.id) })}
    //             />
    //         </div>
    //     ),
    // },
    {
        title: (
            <HeaderCell
                title="Customer Name"
                sortable
                ascending={
                    sortConfig?.direction === 'asc' && sortConfig?.key === 'customer.first_name'
                }
            />
        ),
        onHeaderCell: () => onHeaderCellClick('customer'),
        dataIndex: 'customer',
        key: 'customer',
        width: 200,
        render: (value: any, row: any) => {
            return <Text className="font-medium text-gray-700 dark:text-gray-600 text-center">
                {value.first_name} {value.last_name}
            </Text>
        },
    },
    // {
    //     title: (
    //         <HeaderCell
    //             title="Customer Email"
    //             sortable
    //             ascending={
    //                 sortConfig?.direction === 'asc' && sortConfig?.key === 'email'
    //             }
    //         />
    //     ),
    //     onHeaderCell: () => onHeaderCellClick('email'),
    //     dataIndex: 'customer',
    //     key: 'customer',
    //     width: 200,
    //     render: (value: any, row: any) => {
    //         return <Text className="font-medium text-gray-700 dark:text-gray-600 text-center">
    //             {value.email}
    //         </Text>
    //     },
    // },
    {
        title: (
            <HeaderCell
                title="Customer Phone"
                sortable
                ascending={
                    sortConfig?.direction === 'asc' && sortConfig?.key === 'phone_number'
                }
            />
        ),
        onHeaderCell: () => onHeaderCellClick('phone_number'),
        dataIndex: 'customer',
        key: 'customer',
        width: 200,
        render: (value: any, row: any) => {
            return <Text className="font-medium text-gray-700 dark:text-gray-600 text-center">
                {value.phone_number}
            </Text>
        },
    },
    {
        title: (
            <HeaderCell
                title="Booking Code"
                sortable
                ascending={
                    sortConfig?.direction === 'asc' && sortConfig?.key === 'booking_code'
                }
            />
        ),
        onHeaderCell: () => onHeaderCellClick('booking_code'),
        dataIndex: 'booking_code',
        key: 'booking_code',
        width: 200,
        render: (value: any, row: any) => {
            return <Text className="font-medium text-deepOrange text-center">
                {value.booking_code}
            </Text>
        },
    },
    {
        title: <HeaderCell title="Resolved Status"/>,
        dataIndex: 'resolved',
        key: 'resolved',
        width: 120,
        render: (value: boolean) => getResolvedBadge(value),
    },
    {
        title: <HeaderCell title="Canceled Status"/>,
        dataIndex: 'canceled',
        key: 'canceled',
        width: 120,
        render: (value: boolean) => getCanceledBadge(value),
    },
    {
        title: (
            <HeaderCell
                title="Created At"
                sortable
                ascending={
                    sortConfig?.direction === 'asc' && sortConfig?.key === 'created_at'
                }
            />
        ),
        onHeaderCell: () => onHeaderCellClick('created_at'),
        dataIndex: 'created_at',
        key: 'created_at',
        width: 200,
        render: (value: Date) => <DateCell date={value}/>,
    },
    // {
    //     title: (
    //         <HeaderCell
    //             title="Updated At"
    //             sortable
    //             ascending={
    //                 sortConfig?.direction === 'asc' && sortConfig?.key === 'updated_at'
    //             }
    //         />
    //     ),
    //     onHeaderCell: () => onHeaderCellClick('updated_at'),
    //     dataIndex: 'updated_at',
    //     key: 'updated_at',
    //     width: 200,
    //     render: (value: Date) => <DateCell date={value}/>,
    // },
    {
        title: <></>,
        dataIndex: 'action',
        key: 'action',
        width: 140,
        render: (_: string, row: any) => (
            <div className="flex items-center justify-end gap-3 pe-3">
                <DeletePopover
                    title={`Delete the invoice`}
                    description={`Are you sure you want to delete this #${row.id} invoice?`}
                    onDelete={() => onDeleteItem(row.id)}
                />
            </div>
        ),
    },
];
