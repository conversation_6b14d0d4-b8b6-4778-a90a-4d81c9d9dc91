'use client';

import {useAtom} from 'jotai';
import {zodResolver} from '@hookform/resolvers/zod';
import {Controller, SubmitHandler, useForm} from 'react-hook-form';
import FormSummary from '@/app/shared/app/bookings/create-multi-step/form-summary';
import {
    formDataAtom,
    useStepperOne,
} from '@/app/shared/app/bookings/create-multi-step';
import {
    PassengerDetailsSchema,
    passengerDetailsSchema,
} from './form/multistep-form.schema';
import {Input, Select, Text} from "rizzui";
import FormGroup from "@/app/shared/lib/form-group";
import RangeSlider from "@/components/lib/ui/range-slider";
import React, {useEffect, useState} from "react";
import {useBookingSession} from "@/hooks/use-booking-ls";
import {useHasCarTypes} from "@/services/query/car-type/public/useHasCarTypes";
import toast from "react-hot-toast";

export default function StepThreePassengerDetails() {
    const {step, gotoNextStep} = useStepperOne();
    const [formData, setFormData] = useAtom(formDataAtom);
    const currentStep = 3;
    const bookingSession = useBookingSession();
    const [childrenChairs, setChildrenChairs] = useState({
        babySeat: formData.children_chairs_under_five || 0,
        boosterSeat: formData.children_chairs_above_five || 0,
    });
    const [maxChildrenChairs, setMaxChildrenChairs] = useState({
        babySeat: formData.children || 0,
        boosterSeat: formData.children || 0,
    });

    const {
        handleSubmit,
        register,
        formState: {errors},
        setValue,
        getValues,
        control,
        watch,
    } = useForm<PassengerDetailsSchema>({
        resolver: zodResolver(passengerDetailsSchema),
        defaultValues: {
            adults: formData.adults ?? undefined,
            children: formData.children ?? undefined,
            children_chairs_under_five: formData.children_chairs_under_five ?? undefined,
            children_chairs_above_five: formData.children_chairs_above_five ?? undefined,
            pets: formData.pets ?? undefined,
            flight_number: formData.flight_number ?? undefined,
            other_details: formData.other_details ?? undefined,
        },
    });

    const children = watch('children');
    const adults = watch('adults');
    const totalSeats = (children || 0) + (adults || 0);

    const {
        // isPending,
        // isError,
        // error,
        data: hasCarTypes,
        isFetching,
        // isPlaceholderData,
        // refetch,
        // status,
    } = useHasCarTypes(totalSeats);

    const onSubmit: SubmitHandler<PassengerDetailsSchema> = (data) => {
        console.log('data', data);

        if (hasCarTypes && !hasCarTypes.hasCarTypes) {
            toast.error(hasCarTypes.error);
            return;
        }

        bookingSession?.updateOrInitSession(currentStep + 1, data);

        setFormData((prev) => ({
            ...prev,
            ...data,
        }));
        gotoNextStep();
    };

    function handleRangeChange(key: 'babySeat' | 'boosterSeat', value: any, onChange: (val: number) => void) {
        const chairs = {...childrenChairs}
        chairs[key] = value;

        setChildrenChairs(chairs);

        if(children && typeof children === 'number') {
            let computedBoosterSeats = children - chairs.babySeat;
            let computedBabySeats = children - chairs.boosterSeat;

            setMaxChildrenChairs({
                boosterSeat: computedBoosterSeats,
                babySeat: computedBabySeats,
            });
            if((chairs.babySeat + chairs.boosterSeat) > children) {
                computedBoosterSeats = 0;
                computedBabySeats = 0;
                setChildrenChairs({
                    boosterSeat: computedBoosterSeats,
                    babySeat: computedBabySeats,
                });
            }

            onChange(key === 'babySeat' ? computedBabySeats : computedBoosterSeats);
        }
    }

    const createQuantitySelectorOptions = (max: number = 8) => {
        const quantity = []
        for (let i = 0; i <= max; i++) {
            quantity.push({
                value: i,
                label: i.toString(),
            })
        }

        return quantity;
    };


    useEffect(() => {
        setValue('children_chairs_under_five', childrenChairs.babySeat)
        setValue('children_chairs_above_five', childrenChairs.boosterSeat)
    }, [childrenChairs]);

    useEffect(() => {
        if(
            children &&
            typeof children === 'number'
        ) {
            setMaxChildrenChairs({
                boosterSeat: children,
                babySeat: children,
            })

            if((maxChildrenChairs.babySeat + maxChildrenChairs.boosterSeat) > children) {
                setChildrenChairs({
                    boosterSeat: 0,
                    babySeat: 0,
                });
            }

        }
    }, [children]);

    useEffect(() => {
        if (hasCarTypes && !hasCarTypes.hasCarTypes) {
            toast.error(hasCarTypes.error);
            return;
        }
    }, [hasCarTypes]);

    return (
        <>
            <div className="col-span-full flex flex-col justify-center @5xl:col-span-5">
                <FormSummary
                    title="Passenger Details"
                    description="Please provide us with the details of the passengers."
                />
            </div>

            <div className="col-span-full items-center justify-center text-creamyWhite">
                <form
                    id={`rhf-${step.toString()}`}
                    onSubmit={handleSubmit(onSubmit)}
                    className="flex-grow rounded-lg mb-16 text-creamyWhite">
                    <FormGroup
                        headingColor='text-creamyWhite'
                        title="Who are you traveling with ?"
                        description="It helps us to provide you with the best service."
                    >

                        <Controller
                            control={control}
                            name="adults"
                            render={({field: {value, onChange}}) => {
                                return (
                                    <Select
                                        label="Adults"
                                        className="text-creamyWhite"
                                        labelClassName="text-creamyWhite"
                                        dropdownClassName="p-2 gap-1 grid !z-10"
                                        inPortal={false}
                                        value={value}
                                        onChange={onChange}
                                        options={createQuantitySelectorOptions(8)}
                                        getOptionValue={(option) => option.value}
                                        displayValue={(selected: number) => {
                                            return createQuantitySelectorOptions(8).find((c) => c.value === selected)?.label ?? ''
                                        }
                                        }
                                        error={errors?.adults?.message as string}
                                    />
                                )
                            }}
                        />

                        <Controller
                            control={control}
                            name="children"
                            render={({field: {value, onChange}}) => {
                                return (
                                    <Select
                                        label="Children"
                                        className="text-creamyWhite"
                                        labelClassName="text-creamyWhite"
                                        dropdownClassName="p-2 gap-1 grid !z-10"
                                        inPortal={false}
                                        value={value}
                                        onChange={onChange}
                                        options={createQuantitySelectorOptions(8)}
                                        getOptionValue={(option) => option.value}
                                        displayValue={(selected: number) => {
                                            return createQuantitySelectorOptions(8).find((c) => c.value === selected)?.label ?? ''
                                        }
                                        }
                                        error={errors?.children?.message as string}
                                    />
                                )
                            }}
                        />

                        <div className='col-span-1'>
                            <div className="container mx-auto">
                                <div className="flex justify-between gap-4">
                                    <Text as="span"
                                          className="block text-sm mb-1.5 font-medium text-creamyWhite">
                                        Baby seat (3 years old or under)
                                    </Text>
                                    <Text as="span"
                                          className="block text-sm mb-1.5 font-medium text-creamyWhite">
                                        <strong>{childrenChairs?.babySeat}</strong>
                                    </Text>
                                </div>
                            </div>
                            <Controller
                                control={control}
                                name="children_chairs_under_five"
                                render={({field: {value, onChange}}) => {
                                    // console.log('FUCKING VAL', value);
                                    return (
                                        // <RangeSlider
                                        //     count={1}
                                        //     max={maxChildrenChairs.babySeat}
                                        //     dots={true}
                                        //     value={value || childrenChairs.babySeat}
                                        //     onChange={(value: any) => {
                                        //         handleRangeChange('babySeat', value, onChange);
                                        //     }}
                                        // />
                                        <Select
                                            className="text-creamyWhite"
                                            labelClassName="text-creamyWhite"
                                            dropdownClassName="p-2 gap-1 grid !z-10"
                                            inPortal={false}
                                            value={value}
                                            onChange={(value: any) => {
                                                handleRangeChange('babySeat', value, onChange);
                                            }}
                                            options={createQuantitySelectorOptions(maxChildrenChairs.babySeat)}
                                            getOptionValue={(option) => option.value}
                                            displayValue={(selected: number) => {
                                                    return createQuantitySelectorOptions(maxChildrenChairs.babySeat).find((c) => c.value === selected)?.label ?? ''
                                                }
                                            }
                                            error={errors?.pets?.message as string}
                                        />
                                    )
                                }}
                            />
                            <div role="alert"
                                 className="text-red text-[13px] mt-0.5 rizzui-input-error-text">
                                {errors.children_chairs_under_five?.message as string}
                            </div>
                        </div>

                        <div className='col-span-1'>
                            <div className="container mx-auto">
                                <div className="flex justify-between gap-4">
                                    <Text as="span"
                                          className="block text-sm mb-1.5 font-medium text-creamyWhite">
                                        Booster Seat (older than 3 years old)
                                    </Text>
                                    <Text as="span"
                                          className="block text-sm mb-1.5 font-medium text-creamyWhite">
                                        <strong>{childrenChairs?.boosterSeat}</strong>
                                    </Text>
                                </div>
                            </div>
                            <Controller
                                control={control}
                                name="children_chairs_above_five"
                                render={({field: {value, onChange}}) => {
                                    // console.log('FUCKING VAL BOOSTER', value);

                                    return (
                                        // <RangeSlider
                                        //     count={1}
                                        //     dots={true}
                                        //     min={0}
                                        //     max={maxChildrenChairs.boosterSeat}
                                        //     value={value || childrenChairs.boosterSeat}
                                        //     onChange={(value: any) => {
                                        //         handleRangeChange('boosterSeat', value, onChange);
                                        //     }}
                                        // />
                                        <Select
                                            className="text-creamyWhite"
                                            labelClassName="text-creamyWhite"
                                            dropdownClassName="p-2 gap-1 grid !z-10"
                                            inPortal={false}
                                            value={value}
                                            onChange={(value: any) => {
                                                handleRangeChange('boosterSeat', value, onChange);
                                            }}
                                            options={createQuantitySelectorOptions(maxChildrenChairs.boosterSeat)}
                                            getOptionValue={(option) => option.value}
                                            displayValue={(selected: number) => {
                                                return createQuantitySelectorOptions(maxChildrenChairs.boosterSeat).find((c) => c.value === selected)?.label ?? ''
                                            }
                                            }
                                            error={errors?.pets?.message as string}
                                        />
                                    )
                                }}
                            />
                            <div role="alert"
                                 className="text-red text-[13px] mt-0.5 rizzui-input-error-text">
                                {errors.children_chairs_under_five?.message as string}
                            </div>
                        </div>

                        <Controller
                            control={control}
                            name="pets"
                            render={({field: {value, onChange}}) => {
                                return (
                                    <Select
                                        label="Pets"
                                        className="text-creamyWhite"
                                        labelClassName="text-creamyWhite"
                                        dropdownClassName="p-2 gap-1 grid !z-10"
                                        inPortal={false}
                                        value={value}
                                        onChange={onChange}
                                        options={createQuantitySelectorOptions(8)}
                                        getOptionValue={(option) => option.value}
                                        displayValue={(selected: number) => {
                                            return createQuantitySelectorOptions(8).find((c) => c.value === selected)?.label ?? ''
                                        }
                                        }
                                        error={errors?.pets?.message as string}
                                    />
                                )
                            }}
                        />

                        <Input
                            label="Flight Number"
                            placeholder="ie: BA 222"
                            labelClassName="font-medium text-creamyWhite"
                            {...register('flight_number')}
                            error={errors.flight_number?.message as string}
                        />
                        <Input
                            label="Other Details"
                            placeholder="..."
                            labelClassName="font-medium text-creamyWhite"
                            {...register('other_details')}
                            error={errors.other_details?.message as string}
                        />
                    </FormGroup>
                </form>
            </div>
        </>
    );
}
