// controllers/carType.ts

import { Request, Response } from 'express';
import {
  createCarType,
} from '../../services/car-type'; // Adjust the path as necessary

export const createCarTypeHandler = async (req: Request, res: Response) => {
  try {
    const data = req.body;
    const result = await createCarType(data);
    if (!result.success) {
      return res.status(404).json(result);
    }
    return res.json(result);
  } catch (error: any) {
    return res.status(500).json({
      success: false,
      message: 'Failed to create car type',
      errorType: 'InternalServerError',
      data: undefined,
    });
  }
};
