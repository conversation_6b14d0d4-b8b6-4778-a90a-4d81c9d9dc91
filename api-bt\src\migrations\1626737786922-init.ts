import { MigrationInterface, QueryRunner, Table, TableForeignKey } from 'typeorm';

export class init1626737786922 implements MigrationInterface {
  name = 'init1626737786922';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Role table
    await queryRunner.createTable(
        new Table({
          name: 'role',
          columns: [
            {
              name: 'id',
              type: 'int',
              isPrimary: true,
              isGenerated: true,
              generationStrategy: 'increment',
            },
            {
              name: 'name',
              type: 'varchar',
            },
            {
              name: 'created_at',
              type: 'timestamp',
              default: 'CURRENT_TIMESTAMP',
            },
          ],
        }),
        true,
    );

    // User table
    // User Table
    await queryRunner.createTable(
        new Table({
          name: 'user',
          columns: [
            {
              name: 'id',
              type: 'int',
              isPrimary: true,
              isGenerated: true,
              generationStrategy: 'increment',
            },
            {
              name: 'username',
              type: 'varchar',
            },
            {
              name: 'email',
              type: 'varchar',
              isUnique: true,
            },
            {
              name: 'password',
              type: 'varchar',
            },
            {
              name: 'avatar_url',
              type: 'varchar',
              isNullable: true,
            },
            {
              name: 'country',
              type: 'varchar',
              isNullable: true,
            },
            {
              name: 'bio',
              type: 'text',
              isNullable: true,
            },
            {
              name: 'created_at',
              type: 'timestamp',
              default: 'CURRENT_TIMESTAMP',
            },
            {
              name: 'first_name',
              type: 'varchar',
            },
            {
              name: 'last_name',
              type: 'varchar',
            },
            {
              name: 'user_role',
              type: 'int',
            },
          ],
        }),
        true,
    );

    // Foreign Key for User Table
    await queryRunner.createForeignKey(
        'user',
        new TableForeignKey({
          columnNames: ['user_role'],
          referencedTableName: 'role',
          referencedColumnNames: ['id'],
        }),
    );

    // active_session table
    await queryRunner.createTable(new Table({
      name: 'active_session',
      columns: [
        {
          name: 'id',
          type: 'int',
          isPrimary: true,
          isGenerated: true,
          generationStrategy: 'increment',
        },
        {
          name: 'token',
          type: 'varchar',
        },
        {
          name: 'userId',
          type: 'int',
        },
        {
          name: 'date',
          type: 'timestamp',
          default: 'now()',
        },
      ],
      foreignKeys: [
        {
          columnNames: ['userId'],
          referencedTableName: 'user',
          referencedColumnNames: ['id'],
        },
      ],
    }), true);

    await queryRunner.createTable(
        new Table({
          name: 'payment_type',
          columns: [
            {
              name: 'id',
              type: 'uuid',
              isPrimary: true,
              isGenerated: true,
              generationStrategy: 'uuid',
            },
            {
              name: 'name',
              type: 'varchar',
              length: '50',
            },
            {
              name: 'description',
              type: 'varchar',
              length: '500',
            },
            {
              name: 'created_at',
              type: 'timestamp',
              default: 'CURRENT_TIMESTAMP',
            },
            {
              name: 'updated_at',
              type: 'timestamp',
              default: 'CURRENT_TIMESTAMP',
            },
            {
              name: 'updated_by',
              type: 'int',
              isNullable: true,
            },
          ],
        }),
        true,
    );

    await queryRunner.createTable(
        new Table({
          name: 'payment',
          columns: [
            {
              name: 'id',
              type: 'uuid',
              isPrimary: true,
              isGenerated: true,
              generationStrategy: 'uuid',
            },
            {
              name: 'name',
              type: 'varchar',
              length: '50',
            },
            {
              name: 'amount',
              type: 'int',
            },
            {
              name: 'payment_type_id',
              type: 'uuid',
            },
            {
              name: 'stripe_payment_id',
              type: 'text',
              isNullable: true,
            },
            {
              name: 'amount_paid',
              type: 'int',
              isNullable: true,
            },
            {
              name: 'paid_status',
              type: 'boolean',
              default: false,
            },
            {
              name: 'created_at',
              type: 'timestamp',
              default: 'CURRENT_TIMESTAMP',
            },
            {
              name: 'updated_at',
              type: 'timestamp',
              default: 'CURRENT_TIMESTAMP',
            },
            {
              name: 'updated_by',
              type: 'int',
              isNullable: true,
            },
          ],
        }),
        true,
    );

    await queryRunner.createForeignKey(
        'payment',
        new TableForeignKey({
          columnNames: ['payment_type_id'],
          referencedTableName: 'payment_type',
          referencedColumnNames: ['id'],
          onDelete: 'SET NULL',
        }),
    );

    await queryRunner.createTable(
        new Table({
          name: 'car_type',
          columns: [
            {
              name: 'id',
              type: 'uuid',
              isPrimary: true,
              isGenerated: true,
              generationStrategy: 'uuid',
            },
            {
              name: 'name',
              type: 'varchar',
              length: '50',
            },
            {
              name: 'seats',
              type: 'int',
            },
            {
              name: 'created_at',
              type: 'timestamp',
              default: 'CURRENT_TIMESTAMP',
            },
            {
              name: 'updated_at',
              type: 'timestamp',
              default: 'CURRENT_TIMESTAMP',
            },
            {
              name: 'updated_by',
              type: 'int',
              isNullable: true,
            },
          ],
        }),
        true,
    );

    await queryRunner.createTable(
        new Table({
          name: 'car',
          columns: [
            {
              name: 'id',
              type: 'uuid',
              isPrimary: true,
              isGenerated: true,
              generationStrategy: 'uuid',
            },
            {
              name: 'name',
              type: 'varchar',
              length: '50',
            },
            {
              name: 'image',
              type: 'text',
              isNullable: true,
              default: "'https://via.placeholder.com/150'",
            },
            {
              name: 'type',
              type: 'uuid',
            },
            {
              name: 'brand',
              type: 'varchar',
              length: '50',
              isNullable: true,
            },
            {
              name: 'model',
              type: 'varchar',
              length: '50',
              isNullable: true,
            },
            {
              name: 'allow_pets',
              type: 'boolean',
              default: true,
            },
            {
              name: 'licence_plate',
              type: 'text',
              isNullable: true,
            },
            {
              name: 'bags_capacity',
              type: 'int',
            },
            {
              name: 'created_at',
              type: 'timestamp',
              default: 'CURRENT_TIMESTAMP',
            },
            {
              name: 'updated_at',
              type: 'timestamp',
              default: 'CURRENT_TIMESTAMP',
            },
            {
              name: 'updated_by',
              type: 'int',
              isNullable: true,
            },
          ],
        }),
        true,
    );

    await queryRunner.createForeignKey(
        'car',
        new TableForeignKey({
          columnNames: ['type'],
          referencedTableName: 'car_type',
          referencedColumnNames: ['id'],
          onDelete: 'CASCADE',
        }),
    );

    await queryRunner.createTable(
        new Table({
          name: 'customer',
          columns: [
            {
              name: 'id',
              type: 'uuid',
              isPrimary: true,
              isGenerated: true,
              generationStrategy: 'uuid',
            },
            {
              name: 'first_name',
              type: 'varchar',
              length: '50',
            },
            {
              name: 'last_name',
              type: 'varchar',
              length: '50',
            },
            {
              name: 'phone_number',
              type: 'varchar',
              length: '30',
            },
            {
              name: 'user_id',
              type: 'int',
            },
            {
              name: 'stripe_customer_id',
              type: 'text',
              isNullable: true,
            },
            {
              name: 'created_at',
              type: 'timestamp',
              default: 'CURRENT_TIMESTAMP',
            },
            {
              name: 'updated_at',
              type: 'timestamp',
              default: 'CURRENT_TIMESTAMP',
            },
            {
              name: 'updated_by',
              type: 'int',
              isNullable: true,
            },
          ],
        }),
        true,
    );

    await queryRunner.createForeignKey(
        'customer',
        new TableForeignKey({
          columnNames: ['user_id'],
          referencedTableName: 'user',
          referencedColumnNames: ['id'],
          // onDelete: 'CASCADE',
        }),
    );

    await queryRunner.createTable(
        new Table({
          name: 'bag',
          columns: [
            {
              name: 'id',
              type: 'uuid',
              isPrimary: true,
              isGenerated: true,
              generationStrategy: 'uuid',
            },
            {
              name: 'name',
              type: 'varchar',
              length: '50',
            },
            {
              name: 'description',
              type: 'text',
              isNullable: true,
            },
            {
              name: 'created_at',
              type: 'timestamp',
              default: 'CURRENT_TIMESTAMP',
            },
            {
              name: 'updated_at',
              type: 'timestamp',
              default: 'CURRENT_TIMESTAMP',
            },
          ],
        }),
        true,
    );

    await queryRunner.createTable(
        new Table({
          name: 'booking',
          columns: [
            {
              name: 'id',
              type: 'uuid',
              isPrimary: true,
              isGenerated: true,
              generationStrategy: 'uuid',
            },
            {
              name: 'trip_type',
              type: 'varchar',
              length: '20',
              default: "'transfer'",
            },
            {
              name: 'pickup_address',
              type: 'text',
            },
            {
              name: 'pickup_lat',
              type: 'text',
            },
            {
              name: 'pickup_long',
              type: 'text',
            },
            {
              name: 'destination_address',
              type: 'text',
            },
            {
              name: 'destination_lat',
              type: 'text',
            },
            {
              name: 'destination_long',
              type: 'text',
            },
            {
              name: 'one_way_trip',
              type: 'boolean',
              default: true,
            },
            {
              name: 'return_trip_time',
              type: 'timestamp',
              isNullable: true,
            },
            {
              name: 'adults',
              type: 'int',
            },
            {
              name: 'children',
              type: 'int',
              isNullable: true,
            },
            {
              name: 'children_chairs_under_five',
              type: 'int',
              isNullable: true,
            },
            {
              name: 'children_chairs_above_five',
              type: 'int',
              isNullable: true,
            },
            {
              name: 'pets',
              type: 'int',
              isNullable: true,
            },
            {
              name: 'flight_number',
              type: 'int',
              isNullable: true,
            },
            {
              name: 'status',
              type: 'varchar',
              length: '20',
              default: "'pending'",
            },
            {
              name: 'scheduled_at',
              type: 'timestamp',
            },
            {
              name: 'other_details',
              type: 'text',
              isNullable: true,
            },
            {
              name: 'recommended_amount_to_pay',
              type: 'int',
            },
            {
              name: 'final_amount_to_pay',
              type: 'int',
            },
            {
              name: 'booking_finished_at',
              type: 'timestamp',
              isNullable: true,
            },
            {
              name: 'driver_observation',
              type: 'text',
              isNullable: true,
            },
            {
              name: 'created_at',
              type: 'timestamp',
              default: 'CURRENT_TIMESTAMP',
            },
            {
              name: 'updated_at',
              type: 'timestamp',
              default: 'CURRENT_TIMESTAMP',
            },
            {
              name: 'customer_id',
              type: 'uuid',
            },
            {
              name: 'car_type_id',
              type: 'uuid',
              isNullable: true,
            },
            {
              name: 'payment_id',
              type: 'uuid',
              isNullable: true,
            },
            {
              name: 'updated_by',
              type: 'int',
              isNullable: true,
            },
          ],
        }),
        true,
    );

    // ForeignKey for Customer
    await queryRunner.createForeignKey(
        'booking',
        new TableForeignKey({
          columnNames: ['customer_id'],
          referencedTableName: 'customer',
          referencedColumnNames: ['id'],
          // onDelete: 'CASCADE',
        }),
    );

    // ForeignKey for Car (optional relationship)
    await queryRunner.createForeignKey(
        'booking',
        new TableForeignKey({
          columnNames: ['car_type_id'],
          referencedTableName: 'car_type',
          referencedColumnNames: ['id'],
          onDelete: 'SET NULL', // or 'CASCADE', depending on your deletion policy
        }),
    );

    // ForeignKey for Payment (optional relationship)
    await queryRunner.createForeignKey(
        'booking',
        new TableForeignKey({
          columnNames: ['payment_id'],
          referencedTableName: 'payment',
          referencedColumnNames: ['id'],
          onDelete: 'SET NULL', // or 'CASCADE', depending on your deletion policy
        }),
    );

  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('active_session');
    await queryRunner.dropTable('user');
    await queryRunner.dropTable('role');
    await queryRunner.dropTable('customer');
    await queryRunner.dropTable('bag');
    await queryRunner.dropTable('car_type');
    await queryRunner.dropTable('booking');
    await queryRunner.dropTable('car');
    await queryRunner.dropTable('payment_type');
    await queryRunner.dropTable('payment');
  }
}
