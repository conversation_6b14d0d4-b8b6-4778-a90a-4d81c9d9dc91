import {MigrationInterface, QueryRunner, TableColumn} from "typeorm";

export class removedHourlyRateFromCartypeprices1711164889641 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropColumn('car_type_price', 'hourly_rate');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.addColumn('car_type_price', new TableColumn({
            name: 'hourly_rate',
            type: 'integer',
            isNullable: true, // Adjust based on your previous migration
        }));
    }

}
