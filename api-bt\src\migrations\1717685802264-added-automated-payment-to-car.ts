import {MigrationInterface, QueryRunner, TableColumn} from "typeorm";

export class addedAutomatedPaymentToCar1717685802264 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.addColumn('car', new TableColumn({
            name: 'automatic_acceptance',
            type: 'boolean',
            default: false, // Set a default value if necessary
        }));
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropColumn('car', 'automatic_acceptance');
    }


}
