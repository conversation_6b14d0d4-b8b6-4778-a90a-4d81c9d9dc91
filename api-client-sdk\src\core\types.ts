// API Client Configuration
export interface ApiClientConfig {
  baseUrl: string;
  timeout?: number;
  headers?: Record<string, string>;
}

// Auth Token
export interface AuthToken {
  token: string;
  expiresAt?: Date;
}

// Error Response
export interface ApiErrorResponse {
  success: boolean;
  msg: string;
  statusCode?: number;
}

// API Response
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: ApiErrorResponse;
} 