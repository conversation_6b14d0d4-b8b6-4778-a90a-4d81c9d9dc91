import {<PERSON><PERSON>, Button, Input, Loader, Modal, Select, Title} from 'rizzu<PERSON>';
import {PiCheckCircleFill, PiFloppyDisk, PiXBold, PiXCircle} from 'react-icons/pi';
import {SearchableSelect} from "@/components/app/searchable-select";
import React, {useEffect, useState} from "react";
import {BookingSchemaType, DriverSchemaType} from "@/utils/validators/app/entities";
import {usePaginatedDrivers} from "@/services/query/driver/usePaginatedDrivers";
import Image from "next/image";
import {useUpdateBooking} from "@/services/mutations/booking/admin";
import toast from "react-hot-toast";
import calculatePrice from "@/app/shared/app/bookings/create-multi-step/compute-trip-price";

export default function ExpandedOrderRow({
                                             customer,
                                             car_type,
                                             payment,
                                             id,
                                             driver,
                                             status,
                                             scheduled_at,
                                             trip_distance
                                         }: any) {

    const initialModalData: {
        title: string
        message: string
        data: Partial<BookingSchemaType> | null,

    } = {
        title: 'Customer Phone Number not Validated',
        message: 'It appears that the Customer for this Booking does not have the Phone Number Validated. ' +
            'Before accepting a booking, the Customer that made it must have their phone number Validated. ' +
            'If you tap yes, the phone number will be considered valid and the Booking will be accepted.',
        data: null,
    };

    const [selectedDriver, setSelectedDriver] = useState<Partial<DriverSchemaType>>();
    const [loading, setLoading] = useState<boolean>(false);
    const [statusSelectValue, setStatusSelectValue] = useState<string>(status);
    const [modalData, setModalData] = useState(initialModalData);
    const [open, setOpen] = useState(false);
    const [updatedAmountToPay, setUpdatedAmountToPay] = useState<number>(0);
    const updateMutation = useUpdateBooking();

    const onUpdateBooking = (data: any) => {
        updateBooking(data);
    };

    function updateBooking(data: any, cb?: ({success, error}: { success: boolean, error: string | null }) => void) {
        setLoading(true);
        updateMutation.mutate({
                id, payload: data
            },
            {
                onSuccess: (result: any) => {
                    setLoading(false);
                    if(result?.success) {
                        cb && cb({
                            success: true,
                            error: null,
                        });
                    }
                    if(result?.error) {
                        cb && cb({
                            success: false,
                            error: result.error,
                        });
                        toast.error(result.error);
                    }
                },
                onError: (error: any) => {
                    setLoading(false);
                    cb && cb({
                        success: false,
                        error: error.message,
                    });
                    toast.error(error.response?.data.error || 'Error updating booking');
                }
            }
        )
    }

    const {
        data: drivers,
        isFetching: isFetchingDrivers,
    } = usePaginatedDrivers(1, 30, {}, true);

    const driversOptions = drivers?.data.map((val: DriverSchemaType) => {
        return {
            value: val.id,
            label: `${val.first_name} ${val.last_name}`
        }
    }) || [];

    const onDriverSelect = (d: any) => {
        const selectedDriver = drivers?.data.find((c: DriverSchemaType) => c.id === d);
        setSelectedDriver(selectedDriver);

        if (!customer.is_phone_validated) {
            setModalData({
                ...modalData,
                data: {
                    driver: d,
                }
            });
            setOpen(true);
            return;
        }

        onUpdateBooking({
            driver: d,
        })
    }

    useEffect(() => {
        if (driver) {
            // console.log('Driver with car details:', driver);
            // initDriver.car = find(drivers, (d) => d.id == driver.id).car
            setSelectedDriver(driver);
        }
    }, [drivers]);

    const bookingStatuses = [
        {
            label: 'Pending',
            value: 'pending',
        },
        {
            label: 'Accepted',
            value: 'accepted',
        },
        {
            label: 'Rejected',
            value: 'rejected',
        },
        {
            label: 'Completed',
            value: 'completed',
        },
        {
            label: 'Cancelled',
            value: 'cancelled',
        }
    ]

    function onStatusSelectChange(d: string) {
        setStatusSelectValue(d);

        if (!customer.is_phone_validated) {
            setModalData({
                ...modalData,
                data: {
                    status: d,
                    // @ts-ignore
                    customer: {
                        is_phone_validated: true,
                    }
                }
            });
            setOpen(true);
            return;
        }

        if (d === 'completed'  && (payment.amount_paid < payment.amount)) {
            setModalData({
                ...modalData,
                title: 'Auto update amount paid to fully paid ?',
                message: 'You cannot update the status to completed unless the payment has been made in full. Do you want to also modify the amount paid to fully paid automatically ?',
                data: {
                    status: d,
                    // @ts-ignore
                    payment: {
                        amount_paid: payment.amount
                    }
                }
            });
            setOpen(true);
            return;
        }

        onUpdateBooking({
            status: d,
        });
    }

    const computedPrice = calculatePrice({
        distance: trip_distance,
        defaultPricePerKm: car_type.default_price_per_km,
        ranges: car_type.use_default_ppk ? [] : ( car_type.transfer_prices && car_type.transfer_prices.length > 0 ? car_type.transfer_prices : [] ),
    });

    useEffect(() => {
        if(payment && payment.amount_paid) {
            setUpdatedAmountToPay(payment.amount_paid);
        }
    }, [payment]);

    const saveNewAmountPaid = (updated_amount_paid: number) => {
        // console.log('amount_paid', updated_amount_paid, payment.amount_paid);
        onUpdateBooking({
            payment: {
                amount_paid: updated_amount_paid,
            }
        });
        // if(updated_amount_paid > payment.amount_paid) {
        //     onUpdateBooking({
        //         payment: {
        //             amount_paid: updated_amount_paid,
        //         }
        //     });
        // } else {
        //     setUpdatedAmountToPay(payment.amount_paid);
        //     toast.error('Cannot update amount paid with a value smaller than or equal to the previously set amount paid.');
        // }
    }

    return (
        <div
            className="bg-gradient-to-br dark:from-black-800 dark:to-black-900 shadow-xl rounded-lg overflow-hidden animate-fade-in text-gray-900 dark:text-gray-300">

            <div className="p-6 border-b border-gray-300 dark:border-gray-700 dark:bg-black">
                <h4 className="mb-4 text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                    <span className="text-green-500">💰</span>
                    <span className="ml-2">Update Amount Paid</span>
                </h4>

                <div className="w-full md:w-1/2 lg:w-1/3 flex items-center space-x-3">
                    <Input
                        value={updatedAmountToPay}
                        // label="Set Amount Paid"
                        placeholder="Amount"
                        type={'number'}
                        labelClassName="font-medium text-creamyWhite"
                        onChange={(d) => {
                            setUpdatedAmountToPay(Number(d.target.value));
                        }}
                        // error={'' as string}
                    />
                    <Button
                        size="sm"
                        onClick={() => {
                            // Handle save logic
                            saveNewAmountPaid(updatedAmountToPay);
                        }}
                        className="h-8 bg-gray-200/70"
                        variant="flat"
                    >
                        <PiFloppyDisk className="me-1.5 h-[17px] w-[17px]"/> Save
                    </Button>
                </div>
            </div>

            <div className="p-6 border-b border-gray-300 dark:border-gray-700 dark:bg-black">
                <h4 className="mb-4 text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                    <span className="text-green-500">✅️</span>
                    <span className="ml-2">Change Status</span>
                </h4>

                <div className="w-full md:w-1/2 lg:w-1/3">
                    <Select
                        disabled={!(new Date(scheduled_at).getTime() > new Date().getTime())}
                        label="Status"
                        className=""
                        labelClassName="text-gray-900"
                        dropdownClassName="p-2 gap-1 grid !z-10"
                        inPortal={false}
                        value={statusSelectValue}
                        onChange={onStatusSelectChange}
                        options={bookingStatuses}
                        getOptionValue={(option) => option.value}
                        displayValue={(selected: string) =>
                            bookingStatuses.find((c) => c.value === selected)?.label ?? ''
                        }
                    />
                </div>
            </div>

            <div className="p-6 border-b border-gray-300 dark:border-gray-700 dark:bg-black">
                <h4 className="mb-4 text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                    <span className="text-green-500">👨‍✈️</span>
                    <span className="ml-2">Assign Driver</span>
                </h4>

                <div className="w-full md:w-1/2 lg:w-1/3">
                    <SearchableSelect
                        disabled={!(new Date(scheduled_at).getTime() > new Date().getTime()) || driversOptions.length === 1}
                        data={driversOptions}
                        isFetching={isFetchingDrivers}
                        value={selectedDriver?.id}
                        onChange={(d) => {
                            onDriverSelect(d)
                        }}
                    />
                </div>
            </div>

            {
                loading ? (
                    <div className="grid h-32 flex-grow place-content-center items-center">
                        <Loader size="lg"/>
                    </div>
                ) : null
            }

            {selectedDriver && (
                <div className="p-6 border-b border-gray-300 dark:border-gray-700 dark:bg-black">
                    <h4 className="mb-4 text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                        <span className="text-green-500">🚘</span>
                        <span className="ml-2">Driver Details</span>
                    </h4>
                    <div className="space-y-2 text-gray-800 dark:text-gray-300">
                        <p><span
                            className="font-semibold">Name:</span> {selectedDriver?.first_name} {selectedDriver?.last_name}
                        </p>
                        <p><span className="font-semibold">Driver ID:</span> {selectedDriver?.id}</p>
                        {selectedDriver?.car ? (
                            <div>
                                <p><span className="font-semibold">Car:</span> {selectedDriver.car.name}</p>
                                <p><span className="font-semibold">Brand:</span> {selectedDriver.car.brand}</p>
                                <p><span className="font-semibold">Model:</span> {selectedDriver.car.model}</p>
                                <p><span
                                    className="font-semibold">License Plate:</span> {selectedDriver.car.licence_plate}
                                </p>
                                <p><span
                                    className="font-semibold">Bags Capacity:</span> {selectedDriver.car.bags_capacity}
                                </p>
                                {selectedDriver.car.image ? (
                                    <div className="relative w-[200px] h-[200px] mt-4 rounded-lg overflow-hidden">
                                        <Image
                                            alt={"Car Image"}
                                            src={selectedDriver.car.image}
                                            className="absolute inset-0 object-cover"
                                            priority
                                            quality={90}
                                            width={200}
                                            height={200}
                                        />
                                    </div>
                                ) : null}
                            </div>
                        ) : null}
                    </div>
                </div>
            )}

            {customer ? (
                <div className="p-6 border-b border-gray-300 dark:border-gray-700 dark:bg-black">
                    <h4 className="mb-4 text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                        <span className="text-blue-500">👤</span>
                        <span className="ml-2">Customer Details</span>
                    </h4>
                    <div className="space-y-2 text-gray-800 dark:text-gray-300">
                        <p><span className="font-semibold">Name:</span> {customer?.first_name} {customer?.last_name}</p>
                        <p><span className="font-semibold">Email:</span> {customer?.email}</p>
                        <p><span className="font-semibold">Phone:</span> {customer?.phone_number}</p>
                        <p className={`flex items-center ${customer?.is_phone_validated ? 'text-green-600' : 'text-red-600'}`}>
                            {customer?.is_phone_validated ? <PiCheckCircleFill className="h-5 w-5 mr-1"/> :
                                <PiXCircle className="h-5 w-5 mr-1"/>}
                            <span>Phone Verified: {customer?.is_phone_validated ? 'Yes' : 'No'}</span>
                        </p>
                        <p><span className="font-semibold">Stripe ID:</span> {customer?.stripe_customer_id}</p>
                    </div>
                </div>
            ) : null}

            {car_type ? (
                <div className="p-6 border-b border-gray-300 dark:border-gray-700 dark:bg-black">
                    <h4 className="mb-4 text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                        <span className="text-purple-500">🚗</span>
                        <span className="ml-2">Car Details</span>
                    </h4>
                    <div className="space-y-2 text-gray-800 dark:text-gray-300">
                        <p><span className="font-semibold">Type:</span> {car_type?.name}</p>
                        <p><span className="font-semibold">Seats:</span> {car_type?.seats}</p>
                        <p><span className="font-semibold">Car Hourly Rate:</span> EUR {car_type?.hourly_rate?.toFixed(2)}</p>
                        <p><span
                            className="font-semibold">Default Price Per KM:</span> EUR {car_type?.default_price_per_km.toFixed(2)}
                        </p>
                    </div>
                </div>
            ) : null}

            {payment ? (
                <div className="p-6">
                    <h4 className="mb-4 text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                        <span className="text-yellow-500">💳</span>
                        <span className="ml-2">Payment Details</span>
                    </h4>
                    <div className="space-y-2 text-gray-800 dark:text-gray-300">
                        <p>
                            <span className="font-semibold">Method:</span>
                            <Badge color={'primary'} className="ml-2">
                                {payment?.type.name}
                            </Badge>
                        </p>
                        <p>
                            <span className="font-semibold">Price Range Picked:</span>
                            <span className="ml-2">
                            <Badge color={'primary'} className="mr-2">{`${computedPrice?.range?.name}`}</Badge>
                            <Badge color={'secondary'}
                                   className="mr-2">Fixed Charge - <span
                                className="text-chillGold">{`${computedPrice?.range?.fixed_charge_value}EUR`}</span></Badge>
                            <Badge color={'primary'}
                                   className="mr-2">{`Price per Km: ${computedPrice?.range?.price_per_km}EUR`}</Badge>
                            <Badge color={'primary'}
                                   className="mr-2">{`Max Distance: ${computedPrice?.range?.range_max_distance}km`}</Badge>
                            <Badge
                                color={'success'}>{`Total: ${((computedPrice?.range?.fixed_charge_value || 0) + (computedPrice?.range?.range_max_distance || 0) * +(computedPrice?.range?.price_per_km || 0)).toFixed(2)}EUR`}</Badge>
                        </span>
                        </p>

                        <p className="flex items-center">
                            <span className="font-semibold">Total Amount to Pay:</span>
                            <Badge color={'info'}
                                   className="ml-2">
                                {payment?.amount.toFixed(2)}EUR
                            </Badge>
                        </p>
                        <p className="flex items-center">
                            <span className="font-semibold">Amount paid:</span>
                            <Badge color={payment?.amount_paid >= payment?.amount ? 'success' : 'danger'}
                                   className="ml-2">
                                {payment?.amount_paid}EUR
                            </Badge>
                        </p>
                        <p className="flex items-center">
                            <span className="font-semibold">Total amount left to be paid:</span>
                            <Badge color={payment?.amount - payment?.amount_paid <= 0 ? 'success' : 'danger'}
                                   className="ml-2">
                                {payment?.amount - payment?.amount_paid}EUR
                            </Badge>
                        </p>
                        <p className="flex items-center">
                            <span className="font-semibold">Status:</span>
                            <Badge color={payment?.paid_status ? 'success' : 'danger'} className="ml-2">
                                {
                                    payment?.paid_status ? 'Honored' : (
                                        payment?.amount_paid > 0 && +payment?.amount_paid < +payment?.amount ? 'Not Fully Honored' : 'Not Honored'
                                    )
                                }
                            </Badge>
                        </p>
                    </div>
                </div>
            ) : null}

            <Modal
                isOpen={open}
                onClose={() => {
                    setOpen(false);
                }}
                overlayClassName="dark:bg-opacity-40 dark:backdrop-blur-lg"
                containerClassName="dark:text-gray-300 max-w-[460px] rounded-md p-5 lg:p-6"
            >
                <div className="flex items-center justify-between pb-2 lg:pb-3">
                    <Title
                        as="h3"
                        className="text-lg font-semibold text-gray-900 xl:text-xl"
                    >
                        {modalData.title}
                    </Title>
                </div>

                <div className="flex items-center justify-between pb-2 lg:pb-3">
                    <p className="max-w-xl mx-auto">
                        {modalData.message}
                    </p>
                </div>

                <div className="mt-4 flex justify-center space-x-4">
                    <Button
                        variant="solid"
                        className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md shadow-lg"
                        onClick={() => {
                            // Yes button action
                            if (modalData.data) {
                                updateBooking({
                                    ...modalData.data,
                                });
                                setModalData(initialModalData);
                                setOpen(false);
                            }
                        }}
                    >
                        Yes
                    </Button>
                    <Button
                        variant="flat"
                        className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md shadow-lg"
                        onClick={() => {
                            // No button action
                            setModalData(initialModalData);
                            setOpen(false);
                        }}
                    >
                        No
                    </Button>
                </div>
            </Modal>
        </div>
    );
}
