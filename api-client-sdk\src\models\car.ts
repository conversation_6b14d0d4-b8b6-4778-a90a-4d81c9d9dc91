import { z } from 'zod';
import { CarTypeSchema } from './car-type';

/**
 * Car schema with validation
 */
export const CarSchema = z.object({
  id: z.string().uuid(),
  name: z.string(),
  image: z.string().url().nullable().optional().default('https://via.placeholder.com/150'),
  brand: z.string().nullable().optional(),
  model: z.string().nullable().optional(),
  allow_pets: z.boolean().default(true),
  licence_plate: z.string().nullable().optional(),
  bags_capacity: z.number().int(),
  created_at: z.string().or(z.date()).transform(val => new Date(val)),
  updated_at: z.string().or(z.date()).transform(val => new Date(val)),
  updated_by: z.number().nullable().optional(),
  type: z.lazy(() => CarTypeSchema.optional()),
  drivers: z.array(z.lazy(() => z.any())).optional(), // Circular reference to Driver
});

/**
 * Car creation schema
 */
export const CarCreateSchema = CarSchema.omit({ 
  id: true, 
  created_at: true, 
  updated_at: true,
  drivers: true 
});

/**
 * Car update schema
 */
export const CarUpdateSchema = CarSchema
  .partial()
  .omit({ 
    id: true, 
    created_at: true, 
    updated_at: true,
    drivers: true 
  });

// Type definitions derived from Zod schemas
export type Car = z.infer<typeof CarSchema>;
export type CarCreate = z.infer<typeof CarCreateSchema>;
export type CarUpdate = z.infer<typeof CarUpdateSchema>; 