import { getRepository } from 'typeorm';
import { CarTypeBag } from '../../models/CarTypeBag';
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const deleteCarType_Bag = async (id: string) => {
    const carType_BagRepository = getRepository(CarTypeBag);
    try {
        const deleteResult = await carType_BagRepository.delete(id);
        if (deleteResult.affected === 0) {
            return { success: false, data: null, error: 'CarType_Bag not found' };
        }
        return { success: true, data: { id }, error: null };
    } catch (error) {
        const interpretedError = interpretDatabaseError(error);
        return { success: false, data: null, error: interpretedError };
    }
};
