import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany, OneToOne, JoinColumn,
} from 'typeorm';
import { CarType } from './CarType';
import { Customer } from './Customer';
// eslint-disable-next-line import/no-cycle
import { BookingBag } from './BookingBag';
import {Payment} from "./Payment";
import {Driver} from "./Driver";
import {ColumnNumericTransformer} from "../utils/ColumnNumericTransformer";
import {Car} from "./Car";

@Entity()
export class Booking {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ length: 20, default: 'transfer' })
  trip_type: string;

  @Column('text', )
  pickup_address: string;

  @Column('text', { nullable: true })
  booking_code: string;

  @Column('text', )
  pickup_lat: string;

  @Column('text', )
  pickup_long: string;

  @Column('text', { nullable: true })
  destination_address: string;

  @Column('text', { nullable: true })
  destination_lat: string;

  @Column('text', { nullable: true })
  destination_long: string;

  @Column({ default: true })
  one_way_trip: boolean;

  @Column({ nullable: true })
  return_trip_time: Date;

  @Column()
  adults: number;

  @Column({ nullable: true })
  children: number;

  @Column({ nullable: true })
  children_chairs_under_five: number;

  @Column({ nullable: true })
  children_chairs_above_five: number;

  @Column({ nullable: true })
  pets: number;

  @Column()
  flight_number: string;

  // ##############################################################

  // # pending, accepted, rejected, cancelled, completed
  @Column({ length: 20, default: 'pending' })
  status: string;

  @Column()
  scheduled_at: Date;

  @Column('text', { nullable: true })
  other_details: string;

  @Column('numeric', {
    precision: 7,
    scale: 2,
    transformer: new ColumnNumericTransformer(),
  })
  recommended_amount_to_pay: number;

  @Column('integer', { nullable: true, })
  estimated_service_hours: number;

  @Column('numeric', {
    precision: 7,
    scale: 2,
    transformer: new ColumnNumericTransformer(),
  })
  final_amount_to_pay: number;

  @Column('numeric', {
    precision: 7,
    scale: 2,
    transformer: new ColumnNumericTransformer(),
    nullable: true,
  })
  trip_distance: number;

  @Column('numeric', {
    precision: 7,
    scale: 2,
    transformer: new ColumnNumericTransformer(),
    nullable: true,
  })
  return_trip_distance: number;

  @Column('integer', { nullable: true })
  trip_duration: number;

  @Column('integer', { nullable: true })
  return_trip_duration: number;

  @Column({ nullable: true })
  booking_finished_at: Date;

  @Column('text', { nullable: true })
  driver_observation: string;

  @ManyToOne(() => CarType)
  car_type: CarType;

  @OneToMany(() => BookingBag, bookingBag => bookingBag.booking)
  bookingBags: BookingBag[];

  @ManyToOne(() => Customer)
  customer: Customer;

  @ManyToOne(() => Payment, { cascade: true, onDelete: 'CASCADE' })
  @JoinColumn({ name: "car_id" })
  car: Car;

  @ManyToOne(() => Driver, { cascade: true, onDelete: 'CASCADE' })
  @JoinColumn({ name: "driver_id" })
  driver: Driver;

  @OneToOne(() => Payment, { cascade: true, onDelete: 'CASCADE' })
  @JoinColumn({ name: "payment_id" })
  payment: Payment;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ nullable: true })
  updated_by: number;
}
