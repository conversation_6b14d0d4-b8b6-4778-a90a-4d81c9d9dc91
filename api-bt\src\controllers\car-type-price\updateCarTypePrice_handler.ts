import { Request, Response } from 'express';
import {
    updateCarTypePrice,
} from '../../services/car-type-price'; // Adjust the path as necessary

export const updateCarTypePriceHandler = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        const updateData = req.body;
        const result = await updateCarTypePrice(id, updateData);
        if (!result.success) {
            return res.status(404).json(result);
        }
        return res.json(result);
    } catch (error: any) {
        return res.status(500).json({ error: error.message });
    }
};
