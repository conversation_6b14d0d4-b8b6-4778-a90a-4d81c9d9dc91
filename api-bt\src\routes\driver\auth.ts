import express from 'express';

import { checkToken } from '../../config/safeRoutes';
import { logout } from '../../controllers/auth';
import { loginUser } from '../loginUser';
export const driverAuthRouter = express.Router();

driverAuthRouter.post('/login', (req, res) => {
  loginUser(req, res, 'driver');
});

driverAuthRouter.post('/logout', checkToken, logout);

driverAuthRouter.post('/checkSession', checkToken, (req, res) => {
  const { sessionData } = req as any;
  res.json({
    success: true,
    session: sessionData,
  });
});

// Used for tests (nothing functional)
driverAuthRouter.get('/testme', (_req, res) => {
  res.status(200).json({ success: true, msg: 'all good' });
});
