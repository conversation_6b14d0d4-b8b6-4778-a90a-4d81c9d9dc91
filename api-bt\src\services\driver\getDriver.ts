import { getRepository } from 'typeorm';
import { Driver } from '../../models/Driver';
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const getDriver = async (id: string) => {
  const driverRepository = getRepository(Driver);
  try {
    const driver = await driverRepository.findOne(id, {
      relations: ["car"], // Uncomment if you need to load relations
    });
    if (!driver) {
      return { success: false, data: null, error: 'Driver not found' };
    }
    return { success: true, data: driver, error: null };
  } catch (error) {
    const interpretedError = interpretDatabaseError(error);
    return { success: false, data: null, error: interpretedError };
  }
};
