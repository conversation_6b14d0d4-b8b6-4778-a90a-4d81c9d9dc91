import { Request, Response } from 'express';
import {
  deleteRole,
} from '../../services/role';

export const deleteRole_handler = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const result = await deleteRole(id);
    if (!result.success) {
      return res.status(404).json(result);
    }
    return res.json(result);
  } catch (error: any) {
    return res.status(500).json({ error: error.message });
  }
};
