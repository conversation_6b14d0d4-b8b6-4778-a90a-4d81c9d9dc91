import { Request, Response } from 'express';
import {
    createCarType_Bag,
} from '../../services/car-type-bag';

export const createCarType_Bag_bulk_handler = async (req: Request, res: Response) => {
    try {
        const data = req.body;
        if (!Array.isArray(data) || data.length === 0 || data.length > 20) {
            return res.status(400).json({
                success: false,
                message: 'Invalid bulk data. Must be an array with maximum 20 entries.',
                errorType: 'BadRequest',
                data: undefined,
            });
        }

        const results = await Promise.all(data.map(entry => createCarType_Bag(entry)));
        const errors = results.filter(result => !result.success);

        if (errors.length > 0) {
            return res.status(400).json({
                success: false,
                message: 'Some entries failed to create. Check error details for each entry.',
                errorType: 'BadRequest',
                errors: errors.map(error => error.error),
                data: undefined,
            });
        }

        return res.json({
            success: true,
            message: 'Bulk entries created successfully.',
            data: results.map(result => result.data),
        });
    } catch (error: any) {
        return res.status(500).json({
            success: false,
            message: 'Failed to create bulk CarType_Bags',
            errorType: 'InternalServerError',
            data: undefined,
        });
    }
};
