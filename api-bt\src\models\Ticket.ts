import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
// eslint-disable-next-line import/no-cycle
import { Customer } from './Customer';

@Entity()
export class Ticket {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column('text')
  message: string;
  
  @Column('text', { nullable: true })
  booking_code: string;

  @Column({ type: 'boolean', default: 'false' })
  resolved: boolean;

  @Column({ type: 'boolean', default: 'false' })
  canceled: boolean;

  @ManyToOne(() => Customer, customer => customer.tickets)
  @JoinColumn({ name: 'customer_id' })
  customer: Customer;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
