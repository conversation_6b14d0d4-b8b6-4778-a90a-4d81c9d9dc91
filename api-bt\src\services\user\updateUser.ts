import { getRepository, Not } from 'typeorm';
import { User } from '../../models/User';
import { Driver } from '../../models/Driver';
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';
import bcrypt from 'bcryptjs';
import { userUpdate } from '../../routes/loginUser';

export const updateUser = async (id: string, updateData: Partial<User & { confirmed_password?: string }>) => {

  const result = userUpdate.validate(updateData);

  if (result.error) {
    return { success: false, data: null, error: `Validation err: ${result.error.details[0].message}` };
  }
  
  const userRepository = getRepository(User);
  const driverRepository = getRepository(Driver);

  try {
    // If updateData contains car ID, handle the association
    if (updateData.driver && updateData.driver.id) {
      const driver = await driverRepository.findOne(updateData.driver.id);
      if (!driver) {
        return { success: false, data: null, error: 'Driver not found' };
      }
      updateData.driver = driver;
    }

    // Check for unique email
    if (updateData.email) {
      const existingUser = await userRepository.findOne({ where: { email: updateData.email, id: Not(id) } });
      if (existingUser) {
        return { success: false, data: null, error: 'Email already exists' };
      }
    }

    // Handle password update
    if (updateData.password && updateData.confirmed_password) {
      if (updateData.password !== updateData.confirmed_password) {
        return { success: false, data: null, error: 'Password and Confirm Password do not match' };
      }
      const salt = await bcrypt.genSalt(10);
      updateData.password = await bcrypt.hash(updateData.password, salt);
    } else {
      delete updateData.password; // Remove password if empty
      delete updateData.confirmed_password; // Remove confirmed_password if empty
    }

    // create new updatedData without confirmed_password. i need it removed from the object before updating the user
    const { confirmed_password, ...updatedDataWithoutConfirmedPassword } = updateData;

    const updateResult = await userRepository.update(id, updatedDataWithoutConfirmedPassword);
    if (updateResult.affected === 0) {
      return { success: false, data: null, error: 'User not found' };
    }

    const updatedUser = await userRepository.findOne(id, { relations: ["driver"] });

    console.log('updatedUser', updatedUser);

    if(updatedUser && updatedUser.driver) {
      await driverRepository.update(updatedUser.driver.id, {
        first_name: updatedUser.first_name,
        last_name: updatedUser.last_name,
      });
    }

    return { success: true, data: updatedUser, error: null };
  } catch (error) {
    const interpretedError = interpretDatabaseError(error);
    return { success: false, data: null, error: interpretedError };
  }
};
