import {getRepository, Not, IsNull} from 'typeorm';
import {User} from '../../models/User';
import {paginate, PaginatedResult} from '../../utils/pagination';

const buildDriverFilter = (filterString: Record<string, any>, filterWithCar: boolean) => {
    const filter: any = {};

    if (filterString.first_name) {
        filter.first_name = filterString.first_name;
    }
    if (filterString.last_name) {
        filter.last_name = filterString.last_name;
    }

    if (filterWithCar) {
        filter.car = {id: Not(IsNull())};
    }

    return filter;
};

export const getAllUsers = async (
    page = 1,
    limit = 10,
    filterString?: Record<string, any>,
    filterWithCar: boolean = false
): Promise<PaginatedResult<User>> => {
    const userRepository = getRepository(User);

    const filters = buildDriverFilter(filterString || {}, filterWithCar);

    // modify such that password is not returned
    const options: any = {
        skip: (page - 1) * limit,
        take: limit,
        where: filters,
        order: {
            created_at: 'DESC',
        },
        relations: ['role', 'driver'],
        select: ['id', 'first_name', 'last_name', 'email', 'created_at', 'role', 'driver', 'country', 'avatar_url', 'bio', 'username'] // exclude 'password'
    };

    const [data, totalRecords] = await userRepository.findAndCount(options);

    return paginate<User>(data, page, limit, totalRecords);
};
