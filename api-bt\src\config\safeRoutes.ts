import { NextFunction, Request, Response } from 'express';
import { ActiveSession } from '../models/ActiveSession';
import { connection } from '../server/database';

// Generic middleware factory for role-based access
export const checkRole = (requiredRole?: string) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    const token = String(req.headers.authorization || req.body.token);
    const activeSessionRepository = connection!.getRepository(ActiveSession);

    try {
      const session = await activeSessionRepository.findOne(
        { token },
        { relations: ['user', 'user.role'] }
      );

      (req as any).sessionData = session;

      if (!session?.token || !session?.user) {
        res.status(403).json({ success: false, msg: 'User is not authenticated' });
        return;
      }

      // Allow if user is admin regardless of requiredRole
      if (session.user.role.name === 'admin') {
        next();
        return;
      }

      if (requiredRole && session.user.role.name !== requiredRole) {
        res.status(403).json({ success: false, msg: `User is not a ${requiredRole}` });
        return;
      }

      next();
    } catch (error) {
      res.status(500).json({ success: false, msg: 'Server error' });
    }
  };
};

// Specific middlewares using the generic factory
export const checkToken = checkRole();
export const checkAdmin = checkRole('admin');
export const checkDriver = checkRole('driver');

