import { getRepository } from 'typeorm';
import { Car } from '../../models/Car';
import { PaginatedResult, paginate } from '../../utils/pagination';

export const getAllCars = async (
  page = 1,
  limit = 10,
): Promise<PaginatedResult<Car>> => {
  const carRepository = getRepository(Car);
  const options: any = {
    skip: (page - 1) * limit,
    take: limit,
    relations: ['type', 'drivers', 'type.bags'],
    order: {
      created_at: 'DESC',  // Order by created_at in descending order
    },
  };

  const [data, totalRecords] = await carRepository.findAndCount(options);

  return paginate<Car>(data, page, limit, totalRecords);
};
