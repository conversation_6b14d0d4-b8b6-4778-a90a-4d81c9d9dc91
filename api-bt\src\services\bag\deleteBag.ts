import { getRepository } from 'typeorm';
import { Bag } from '../../models/Bag'; // Adjust the path as necessary
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const deleteBag = async (id: string) => {
  const bagRepository = getRepository(Bag);
  try {
    const deleteResult = await bagRepository.delete(id);
    if (deleteResult.affected === 0) {
      return { success: false, data: null, error: 'Bag not found' };
    }
    return { success: true, data: { id }, error: null };
  } catch (error) {
    const interpretedError = interpretDatabaseError(error);
    return { success: false, data: null, error: interpretedError };
  }
};
