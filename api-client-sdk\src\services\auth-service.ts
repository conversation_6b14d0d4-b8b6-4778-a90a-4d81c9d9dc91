import { apiClient } from '../core/api-client';
import { 
  LoginCredentials, 
  LoginResponse, 
  LogoutResponse,
  AuthSession,
  LoginCredentialsSchema, 
  LoginResponseSchema 
} from '../models/auth';
import { User } from '../models/user';

/**
 * Authentication service for handling user authentication
 */
export class AuthService {
  private static instance: AuthService;
  private currentSession: AuthSession | null = null;

  private constructor() {}

  /**
   * Get the singleton instance of the AuthService
   */
  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  /**
   * Login a user with email and password
   * @param credentials User login credentials
   * @returns Login response with token and user data
   */
  public async login(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      // Validate credentials
      LoginCredentialsSchema.parse(credentials);
      
      const response = await apiClient.post<LoginResponse>('/auth/login', credentials);
      
      if (response.success && response.data && response.data.success && response.data.token && response.data.user) {
        // Validate response
        const validatedResponse = LoginResponseSchema.parse(response.data);
        
        // Set the auth token
        apiClient.setAuthToken(validatedResponse.token!);
        
        // Store the session
        this.currentSession = {
          token: validatedResponse.token!,
          user: validatedResponse.user!,
        };
        
        return validatedResponse;
      }
      
      return response.data || { success: false, msg: 'Login failed' };
    } catch (error) {
      return { 
        success: false, 
        msg: error instanceof Error ? error.message : 'Login failed' 
      };
    }
  }

  /**
   * Login as an admin
   * @param credentials Admin login credentials
   * @returns Login response with token and user data
   */
  public async loginAdmin(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      // Validate credentials
      LoginCredentialsSchema.parse(credentials);
      
      const response = await apiClient.post<LoginResponse>('/admin/login', credentials);
      
      if (response.success && response.data && response.data.success && response.data.token && response.data.user) {
        // Validate response
        const validatedResponse = LoginResponseSchema.parse(response.data);
        
        // Set the auth token
        apiClient.setAuthToken(validatedResponse.token!);
        
        // Store the session
        this.currentSession = {
          token: validatedResponse.token!,
          user: validatedResponse.user!,
        };
        
        return validatedResponse;
      }
      
      return response.data || { success: false, msg: 'Admin login failed' };
    } catch (error) {
      return { 
        success: false, 
        msg: error instanceof Error ? error.message : 'Admin login failed' 
      };
    }
  }

  /**
   * Login as a driver
   * @param credentials Driver login credentials
   * @returns Login response with token and user data
   */
  public async loginDriver(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      // Validate credentials
      LoginCredentialsSchema.parse(credentials);
      
      const response = await apiClient.post<LoginResponse>('/driver/login', credentials);
      
      if (response.success && response.data && response.data.success && response.data.token && response.data.user) {
        // Validate response
        const validatedResponse = LoginResponseSchema.parse(response.data);
        
        // Set the auth token
        apiClient.setAuthToken(validatedResponse.token!);
        
        // Store the session
        this.currentSession = {
          token: validatedResponse.token!,
          user: validatedResponse.user!,
        };
        
        return validatedResponse;
      }
      
      return response.data || { success: false, msg: 'Driver login failed' };
    } catch (error) {
      return { 
        success: false, 
        msg: error instanceof Error ? error.message : 'Driver login failed' 
      };
    }
  }

  /**
   * Logout the current user
   */
  public async logout(): Promise<LogoutResponse> {
    try {
      if (!this.currentSession?.token) {
        return { success: false, msg: 'No active session' };
      }
      
      const response = await apiClient.post<LogoutResponse>('/auth/logout', {
        token: this.currentSession.token,
      });
      
      // Clear client state regardless of server response
      apiClient.clearAuthToken();
      this.currentSession = null;
      
      return response.data || { success: true, msg: 'Logged out successfully' };
    } catch (error) {
      // Clear client state even if server request fails
      apiClient.clearAuthToken();
      this.currentSession = null;
      
      return { 
        success: false, 
        msg: error instanceof Error ? error.message : 'Logout failed' 
      };
    }
  }

  /**
   * Get the current authenticated user
   */
  public getCurrentUser(): User | null {
    return this.currentSession?.user || null;
  }

  /**
   * Get the current authentication session
   */
  public getSession(): AuthSession | null {
    return this.currentSession;
  }

  /**
   * Check if a user is authenticated
   */
  public isAuthenticated(): boolean {
    return !!this.currentSession;
  }

  /**
   * Restore a previous session
   * @param session Authentication session to restore
   */
  public restoreSession(session: AuthSession): void {
    this.currentSession = session;
    apiClient.setAuthToken(session.token);
  }

  /**
   * Clear the current session
   */
  public clearSession(): void {
    this.currentSession = null;
    apiClient.clearAuthToken();
  }
}

// Export a singleton instance of the auth service
export const authService = AuthService.getInstance(); 