import { getRepository } from 'typeorm';
import { CarTypeBag } from '../../models/CarTypeBag';
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const updateCarType_Bag = async (id: string, data: Partial<CarTypeBag>) => {
    const carType_BagRepository = getRepository(CarTypeBag);
    try {
        const carType_Bag = await carType_BagRepository.findOne(id);
        if (!carType_Bag) {
            return { success: false, data: null, error: 'CarType_Bag not found' };
        }

        carType_BagRepository.merge(carType_Bag, data);
        const updatedCarType_Bag = await carType_BagRepository.save(carType_Bag);
        return { success: true, data: updatedCarType_Bag, error: null };
    } catch (error) {
        const interpretedError = interpretDatabaseError(error);
        return { success: false, data: null, error: interpretedError };
    }
};
