import { apiClient } from '../core/api-client';
import { Booking, BookingCreate, BookingUpdate, BookingSchema, BookingStatus } from '../models/booking';
import { PaginatedResponse, PaginationParams, createPaginationParams } from '../utils/pagination';
import { ApiResponse } from '../core/types';

/**
 * Booking service for handling booking operations
 */
export class BookingService {
  private static instance: BookingService;

  private constructor() {}

  /**
   * Get the singleton instance of the BookingService
   */
  public static getInstance(): BookingService {
    if (!BookingService.instance) {
      BookingService.instance = new BookingService();
    }
    return BookingService.instance;
  }

  /**
   * Get all bookings with pagination
   * @param params Pagination parameters
   * @returns Paginated list of bookings
   */
  public async getAllBookings(params: PaginationParams = {}): Promise<ApiResponse<PaginatedResponse<Booking>>> {
    try {
      const paginationParams = createPaginationParams(params);
      const response = await apiClient.get<PaginatedResponse<Booking>>('/bookings', paginationParams);
      
      if (response.success && response.data) {
        // Validate each booking in the response
        const validatedBookings = response.data.data.map(booking => BookingSchema.parse(booking));
        return {
          success: true,
          data: {
            ...response.data,
            data: validatedBookings,
          },
        };
      }
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: {
          success: false,
          msg: error instanceof Error ? error.message : 'Failed to fetch bookings',
        },
      };
    }
  }

  /**
   * Get a booking by ID
   * @param id Booking ID
   * @returns Booking data
   */
  public async getBookingById(id: string): Promise<ApiResponse<Booking>> {
    try {
      const response = await apiClient.get<Booking>(`/bookings/${id}`);
      
      if (response.success && response.data) {
        // Validate booking data
        const validatedBooking = BookingSchema.parse(response.data);
        return {
          success: true,
          data: validatedBooking,
        };
      }
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: {
          success: false,
          msg: error instanceof Error ? error.message : `Failed to fetch booking with ID: ${id}`,
        },
      };
    }
  }

  /**
   * Create a new booking
   * @param bookingData Booking creation data
   * @returns Created booking data
   */
  public async createBooking(bookingData: BookingCreate): Promise<ApiResponse<Booking>> {
    try {
      const response = await apiClient.post<Booking>('/bookings', bookingData);
      
      if (response.success && response.data) {
        // Validate response
        const validatedBooking = BookingSchema.parse(response.data);
        return {
          success: true,
          data: validatedBooking,
        };
      }
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: {
          success: false,
          msg: error instanceof Error ? error.message : 'Failed to create booking',
        },
      };
    }
  }

  /**
   * Update a booking
   * @param id Booking ID
   * @param bookingData Booking update data
   * @returns Updated booking data
   */
  public async updateBooking(id: string, bookingData: BookingUpdate): Promise<ApiResponse<Booking>> {
    try {
      const response = await apiClient.put<Booking>(`/bookings/${id}`, bookingData);
      
      if (response.success && response.data) {
        // Validate response
        const validatedBooking = BookingSchema.parse(response.data);
        return {
          success: true,
          data: validatedBooking,
        };
      }
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: {
          success: false,
          msg: error instanceof Error ? error.message : `Failed to update booking with ID: ${id}`,
        },
      };
    }
  }

  /**
   * Update booking status
   * @param id Booking ID
   * @param status New booking status
   * @returns Updated booking data
   */
  public async updateBookingStatus(id: string, status: BookingStatus): Promise<ApiResponse<Booking>> {
    try {
      const response = await apiClient.patch<Booking>(`/bookings/${id}/status`, { status });
      
      if (response.success && response.data) {
        // Validate response
        const validatedBooking = BookingSchema.parse(response.data);
        return {
          success: true,
          data: validatedBooking,
        };
      }
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: {
          success: false,
          msg: error instanceof Error ? error.message : `Failed to update status for booking with ID: ${id}`,
        },
      };
    }
  }

  /**
   * Assign driver to booking
   * @param bookingId Booking ID
   * @param driverId Driver ID
   * @returns Updated booking data
   */
  public async assignDriver(bookingId: string, driverId: string): Promise<ApiResponse<Booking>> {
    try {
      const response = await apiClient.post<Booking>(`/bookings/${bookingId}/assign-driver`, { driver_id: driverId });
      
      if (response.success && response.data) {
        // Validate response
        const validatedBooking = BookingSchema.parse(response.data);
        return {
          success: true,
          data: validatedBooking,
        };
      }
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: {
          success: false,
          msg: error instanceof Error ? error.message : `Failed to assign driver to booking with ID: ${bookingId}`,
        },
      };
    }
  }

  /**
   * Delete a booking
   * @param id Booking ID
   * @returns API response
   */
  public async deleteBooking(id: string): Promise<ApiResponse<{ success: boolean }>> {
    try {
      const response = await apiClient.delete<{ success: boolean }>(`/bookings/${id}`);
      return response;
    } catch (error) {
      return {
        success: false,
        error: {
          success: false,
          msg: error instanceof Error ? error.message : `Failed to delete booking with ID: ${id}`,
        },
      };
    }
  }

  /**
   * Get bookings by customer ID
   * @param customerId Customer ID
   * @param params Pagination parameters
   * @returns Paginated list of bookings for the specified customer
   */
  public async getBookingsByCustomer(customerId: string, params: PaginationParams = {}): Promise<ApiResponse<PaginatedResponse<Booking>>> {
    try {
      const paginationParams = createPaginationParams(params);
      const response = await apiClient.get<PaginatedResponse<Booking>>(`/customers/${customerId}/bookings`, paginationParams);
      
      if (response.success && response.data) {
        // Validate each booking in the response
        const validatedBookings = response.data.data.map(booking => BookingSchema.parse(booking));
        return {
          success: true,
          data: {
            ...response.data,
            data: validatedBookings,
          },
        };
      }
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: {
          success: false,
          msg: error instanceof Error ? error.message : `Failed to fetch bookings for customer with ID: ${customerId}`,
        },
      };
    }
  }

  /**
   * Get bookings by driver ID
   * @param driverId Driver ID
   * @param params Pagination parameters
   * @returns Paginated list of bookings for the specified driver
   */
  public async getBookingsByDriver(driverId: string, params: PaginationParams = {}): Promise<ApiResponse<PaginatedResponse<Booking>>> {
    try {
      const paginationParams = createPaginationParams(params);
      const response = await apiClient.get<PaginatedResponse<Booking>>(`/drivers/${driverId}/bookings`, paginationParams);
      
      if (response.success && response.data) {
        // Validate each booking in the response
        const validatedBookings = response.data.data.map(booking => BookingSchema.parse(booking));
        return {
          success: true,
          data: {
            ...response.data,
            data: validatedBookings,
          },
        };
      }
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: {
          success: false,
          msg: error instanceof Error ? error.message : `Failed to fetch bookings for driver with ID: ${driverId}`,
        },
      };
    }
  }
}

// Export a singleton instance of the booking service
export const bookingService = BookingService.getInstance(); 