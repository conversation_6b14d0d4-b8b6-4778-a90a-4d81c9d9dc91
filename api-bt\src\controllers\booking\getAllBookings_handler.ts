import { Request, Response } from 'express';
import {
  getAllBookings,
} from '../../services/booking';

export const getAllBookingsHandler = async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string, 10) || 1;
    const limit = parseInt(req.query.limit as string, 10) || 10;
    const paginatedResult = await getAllBookings(page, limit);
    return res.json(paginatedResult);
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
};
