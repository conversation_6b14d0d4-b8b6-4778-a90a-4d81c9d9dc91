"use client";

import { FC, ReactElement, useEffect, useState } from 'react';
import Image from 'next/image'; // Assuming you are using Next.js
import { FaBars, FaTimes, FaMoon, FaSun } from 'react-icons/fa';
import { useTheme } from 'next-themes';
import { useColorPresetName } from '@/hooks/use-theme-color';
import { updateThemeColor } from '@/utils/update-theme-color';
import { presetDark, presetLight } from '@/config/color-presets';
import {Language, useI18n} from "@/hooks/use-translation";

interface NavItem {
    name: string;
    link: string;
    icon: ReactElement;
}

interface FloatingNavbarProps {
    items: NavItem[];
}

const DesktopNavbar: FC<FloatingNavbarProps> = ({ items }) => {
    const { theme, setTheme } = useTheme();
    const { currentLanguage, changeLanguage, getAvailableLanguages } = useI18n();
    const languages = getAvailableLanguages();

    return (
        <nav className="hidden md:flex fixed top-4 left-1/2 transform -translate-x-1/2 shadow-lg rounded-full px-10 py-3 justify-center items-center space-x-6 z-[1000] bg-gray-100 shadow-xl">
            <div className="flex-shrink-0">
                <Image src="/brand/click4transfer-logo.png" alt="Click4Transfer Logo" width={50} height={50} />
            </div>
            {items.map((item, index) => (
                <div key={index} className="flex items-center">
                    <a
                        href={item.link}
                        className="flex flex-col items-center text-gray-800 hover:text-chillGold transition-colors duration-200 mx-4"
                    >
                        <div className="p-2 rounded-full shadow-md">
                            {item.icon}
                        </div>
                        <span className="text-xs mt-1">{item.name}</span>
                    </a>
                    {index < items.length - 1 && (
                        <div className="w-px h-6 bg-gray-300 mx-2"></div>
                    )}
                </div>
            ))}
            <div className="ml-4 flex items-center">
                <button
                    onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
                    className="p-2 rounded-full shadow-md transition-colors duration-200"
                >
                    {theme === 'light' ? (
                        <FaMoon className="h-6 w-6 text-gray-800" />
                    ) : (
                        <FaSun className="h-6 w-6 text-gray-800" />
                    )}
                </button>
                <select
                    value={currentLanguage}
                    onChange={(e) => changeLanguage(e.target.value as Language)}
                    className="custom-locale-selector p-2 rounded-md shadow-md bg-transparent  focus:outline-none focus:ring-2 focus:ring-chillGold"
                >
                    {languages.map((lang) => (
                        <option key={lang} value={lang}>
                            {lang}
                        </option>
                    ))}
                </select>
            </div>
        </nav>
    );
};

const MobileNavbar: FC<FloatingNavbarProps> = ({ items }) => {
    const { theme, setTheme } = useTheme();
    const { currentLanguage, changeLanguage, getAvailableLanguages } = useI18n();
    const languages = getAvailableLanguages();
    const [isOpen, setIsOpen] = useState(false);

    const handleClose = () => setIsOpen(false);

    return (
        <>
            <nav className="md:hidden fixed top-4 left-1/2 transform -translate-x-1/2 shadow-lg p-3 flex justify-between rounded-full items-center space-x-4 z-[1000] w-11/12 bg-gray-100 shadow-xl">
                <div className="flex-shrink-0">
                    <Image src="/brand/click4transfer-logo.png" alt="Click4Transfer Logo" width={40} height={40} />
                </div>
                <div className="flex items-center space-x-4">
                    <button
                        onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
                        className="p-2 rounded-full shadow-md transition-colors duration-200"
                    >
                        {theme === 'light' ? (
                            <FaMoon className="h-6 w-6 text-gray-800"/>
                        ) : (
                            <FaSun className="h-6 w-6 text-gray-800"/>
                        )}
                    </button>
                    <select
                        value={currentLanguage}
                        onChange={(e) => changeLanguage(e.target.value as Language)}
                        className="custom-locale-selector p-2 rounded-md shadow-md bg-transparent focus:outline-none focus:ring-2 focus:ring-chillGold"
                    >
                        {languages.map((lang) => (
                            <option
                                key={lang}
                                value={lang}
                                className="bg-transparent"
                            >
                                {lang}
                            </option>
                        ))}
                    </select>
                    <button
                        onClick={() => setIsOpen(!isOpen)}
                        className="text-gray-800 focus:outline-none"
                    >
                        {isOpen ? <FaTimes className="h-8 w-8 text-chillGold"/> :
                            <FaBars className="h-8 w-8 text-chillGold animate-pulse"/>}
                    </button>
                </div>
            </nav>
            {isOpen && (
                <div
                    className="fixed inset-0 bg-black bg-opacity-50 z-[900]"
                    onClick={handleClose}
                />
            )}
            {isOpen && (
                <div
                    className="fixed top-16 left-1/2 transform -translate-x-1/2 bg-gray-100 shadow-lg rounded-lg p-4 flex flex-col space-y-4 z-[1000] w-11/12 shadow-xl">
                    {items.map((item, index) => (
                        <a
                            key={index}
                            href={item.link}
                            className="flex items-center text-gray-800 hover:text-chillGold transition-colors duration-200"
                            onClick={handleClose}
                        >
                            <div className="p-2 rounded-full shadow-md mr-2">
                                {item.icon}
                            </div>
                            <span className="text-base">{item.name}</span>
                        </a>
                    ))}
                </div>
            )}
        </>
    );
};

export function Navbar(props: FloatingNavbarProps) {
    const { theme, setTheme } = useTheme();
    const { colorPresetName } = useColorPresetName();
    const [isThemeAutoSet, setIsThemeAutoSet] = useState(false);

    useEffect(() => {
        const currentHour = new Date().getHours();

        // Automatically switch to dark mode after 7 PM
        if (currentHour >= 19 && !isThemeAutoSet) {
            setTheme('dark');
            setIsThemeAutoSet(true); // Prevents overriding user selection on subsequent renders
        }

        if (theme === 'light' && colorPresetName === 'black') {
            updateThemeColor(
                // presetLight.lighter,
                // presetLight.light,
                // presetLight.default,
                // presetLight.dark,
                // presetLight.foreground
                presetDark.lighter,
                presetDark.light,
                presetDark.default,
                presetDark.dark,
                presetDark.foreground
            );
        }
        if (theme === 'dark' && colorPresetName === 'black') {
            updateThemeColor(
                presetDark.lighter,
                presetDark.light,
                presetDark.default,
                presetDark.dark,
                presetDark.foreground
            );
        }
    }, [theme, colorPresetName, setTheme, isThemeAutoSet]);

    return (
        <div className="relative w-full">
            <DesktopNavbar items={props.items} />
            <MobileNavbar items={props.items} />
        </div>
    );
}

export default Navbar;