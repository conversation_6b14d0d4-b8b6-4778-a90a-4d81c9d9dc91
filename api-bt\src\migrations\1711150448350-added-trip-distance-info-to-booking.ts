import {MigrationInterface, QueryRunner, TableColumn} from "typeorm";

export class addedTripDistanceInfoToBooking1711150448350 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        const bookingTable = await queryRunner.getTable('booking');
        if (bookingTable) {
            if (!bookingTable.columns.find(column => column.name === 'trip_distance')) {
                await queryRunner.addColumn('booking', new TableColumn({
                    name: 'trip_distance',
                    type: 'integer',
                }));
            }
            if (!bookingTable.columns.find(column => column.name === 'return_trip_distance')) {
                await queryRunner.addColumn('booking', new TableColumn({
                    name: 'return_trip_distance',
                    type: 'integer',
                }));
            }
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropColumn('booking', 'trip_distance');
        await queryRunner.dropColumn('booking', 'return_trip_distance');
    }

}
