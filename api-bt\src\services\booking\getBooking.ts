import { getRepository } from 'typeorm';
import { Booking } from '../../models/Booking';
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const getBooking = async (id: string) => {
  const bookingRepository = getRepository(Booking);
  try {
    const booking = await bookingRepository.findOne(id, {
      relations: ['car_type', 'customer', 'payment', 'payment.type', 'driver', 'driver.car', 'car_type.transfer_prices'],
    });
    if (!booking) {
      return { success: false, data: null, error: 'Booking not found' };
    }
    return { success: true, data: booking, error: null };
  } catch (error) {
    const interpretedError = interpretDatabaseError(error);
    return { success: false, data: null, error: interpretedError };
  }
};
