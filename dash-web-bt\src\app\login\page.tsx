import LoginForm from '@/app/login/login-form';
import AuthWrapperOne from '@/app/shared/lib/auth-layout/auth-wrapper';
import Image from 'next/image';
import UnderlineShape from '@/components/lib/shape/underline';
import { metaObject } from '@/config/site.config';
import btSplash from '@public/banners/b_04.webp';
import { getServerSession } from 'next-auth/next';
import { redirect } from 'next/navigation'
export const metadata = {
  ...metaObject('Log In'),
};

export default async function SignIn() {
    const session = await getServerSession();

    // If the user is not authenticated, redirect them to the login page
    if (session && session.user) {
        redirect('/admin')
    }
  return (
    <AuthWrapperOne
      title={
        <>
          Welcome back! Please{' '}
          <span className="relative inline-block">
            Sign in to
            <UnderlineShape className="absolute -bottom-2 start-0 h-2.5 w-24 text-blue md:w-28 xl:-bottom-1.5 xl:w-36" />
          </span>{' '}
          continue.
        </>
      }
      description="By signing up, you will gain access to exclusive content, special
      offers, and be the first to hear about exciting news and updates."
      bannerTitle="The simplest way to manage your workspace."
      bannerDescription="Amet minim mollit non deserunt ullamco est sit aliqua dolor do
      amet sint velit officia consequat duis."
      isSocialLoginActive={true}
      pageImage={
        <div className="relative mx-auto aspect-[4/3.37] w-[500px] xl:w-[620px] 2xl:w-[820px]">
          <Image
            src={
                btSplash
            }
            alt="Sign Up Thumbnail"
            fill
            priority
            sizes="(max-width: 768px) 100vw"
            className="object-cover"
          />
        </div>
      }
    >
      <LoginForm />
    </AuthWrapperOne>
  );
}
