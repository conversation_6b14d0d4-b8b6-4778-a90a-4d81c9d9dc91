import { getRepository } from 'typeorm';
import { Bag } from '../../models/Bag'; // Adjust the path as necessary
import { paginate, PaginatedResult } from '../../utils/pagination';

export const getAllBags = async (
  page = 1,
  limit = 10,
): Promise<PaginatedResult<Bag>> => {
  const bagRepository = getRepository(Bag);
  const options: any = {
    skip: (page - 1) * limit,
    take: limit,
  };

  const [data, totalRecords] = await bagRepository.findAndCount(options);

  return paginate<Bag>(data, page, limit, totalRecords);
};
