import Image from 'next/image';
import UnderlineShape from '@/components/lib/shape/underline';
import RegisterForm from './register-form';
import AuthWrapperOne from '@/app/shared/lib/auth-layout/auth-wrapper';
import { metaObject } from '@/config/site.config';
import btSplash from '@public/banners/b_04.webp';

export const metadata = {
  ...metaObject('Register'),
};

export default function Register() {
  return (
    <AuthWrapperOne
      title={
        <>
          Join us and never miss a thing -{' '}
          <span className="relative inline-block">
            SIGN UP!
            <UnderlineShape className="absolute -bottom-2 start-0 h-2.5 w-28 text-blue xl:-bottom-1.5 xl:w-36" />
          </span>
        </>
      }
      description="By signing up, you will gain access to exclusive content, special offers, and be the first to hear about exciting news and updates."
      bannerTitle="The simplest way to manage your workspace."
      bannerDescription="Amet minim mollit non deserunt ullamco est sit aliqua dolor do
      amet sint velit officia consequat duis."
      isSocialLoginActive={true}
      pageImage={
        <div className="relative mx-auto aspect-[4/3.37] w-[500px] xl:w-[620px] 2xl:w-[820px]">
          <Image
            src={
                btSplash
            }
            alt="Sign Up Thumbnail"
            fill
            priority
            sizes="(max-width: 768px) 100vw"
            className="object-cover"
          />
        </div>
      }
    >
      <RegisterForm />
    </AuthWrapperOne>
  );
}
