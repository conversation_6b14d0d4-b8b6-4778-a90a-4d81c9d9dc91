import { getRepository } from 'typeorm';
import { Booking } from '../../models/Booking';
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const deleteBooking = async (id: string) => {
  const bookingRepository = getRepository(Booking);
  try {
    const deleteResult = await bookingRepository.delete(id);
    if (deleteResult.affected === 0) {
      return { success: false, data: null, error: 'Booking not found' };
    }
    return { success: true, data: { id }, error: null };
  } catch (error) {
    const interpretedError = interpretDatabaseError(error);
    return { success: false, data: null, error: interpretedError };
  }
};
