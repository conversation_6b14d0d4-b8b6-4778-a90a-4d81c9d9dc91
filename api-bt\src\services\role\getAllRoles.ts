import {getRepository, Not, IsNull} from 'typeorm';
import {Role} from '../../models/Role';
import {paginate, PaginatedResult} from '../../utils/pagination';

const buildRoleFilter = (filterString: Record<string, any>, filterWithCar: boolean) => {
    const filter: any = {};

    if (filterString.name) {
        filter.name = filterString.name;
    }
    if (filterString.description) {
        filter.description = filterString.description;
    }

    if (filterWithCar) {
        filter.users = {id: Not(IsNull())};
    }

    return filter;
};

export const getAllRoles = async (
    page = 1,
    limit = 10,
    filterString?: Record<string, any>,
    filterWithCar: boolean = false
): Promise<PaginatedResult<Role>> => {
    const roleRepository = getRepository(Role);

    const filters = buildRoleFilter(filterString || {}, filterWithCar);

    const options: any = {
        skip: (page - 1) * limit,
        take: limit,
        relations: ["users"],
        where: filters,
        order: {
            created_at: 'DESC',
        },
    };

    const [data, totalRecords] = await roleRepository.findAndCount(options);

    return paginate<Role>(data, page, limit, totalRecords);
};
