import { Request, Response } from "express";
import <PERSON><PERSON> from "joi";
import { connection } from '../server/database';
import { User } from "../models/User";
import { ActiveSession } from "../models/ActiveSession";
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

export const userSchema = Joi.object().keys({
    id: Joi.number().optional(),
    email: Joi.string().email().required(),
    username: Joi.string().min(4).max(15)
      .optional(),
    password: Joi.string().required(),
    confirmed_password: Joi.string().optional(),
    avatar_url: Joi.string().uri().optional(),
    first_name: Joi.string(),
    last_name: Joi.string(),
    country: Joi.string().optional(),
    bio: Joi.string().optional(),
    driver: [Joi.string().optional(), Joi.allow(null)],
    role: [Joi.number().optional(), Joi.allow(null)],
  });
  
  export const userUpdate = Joi.object().keys({
    email: Joi.string().email().optional(),
    username: Joi.string().min(4).max(15)
      .optional(),
    // password can be empty string
    password: Joi.string().allow('').optional(),
    confirmed_password: Joi.string().allow('').optional(),
    avatar_url: Joi.string().allow('').uri().optional(),
    first_name: Joi.string().optional(),
    last_name: Joi.string().optional(),
    country: Joi.string().allow('').optional().optional(),
    driver: [Joi.string().optional(), Joi.allow(null)],
    bio: Joi.string().allow('').optional().optional(),
    role: [Joi.number().optional(), Joi.allow(null)],
  });

export function loginUser(req: Request, res: Response, type: 'admin' | 'driver' = 'admin') {
    const result = userSchema.validate(req.body);
    if (result.error) {
      res.status(422).json({
        success: false,
        msg: `Validation err: ${result.error.details[0].message}`,
      });
      return;
    }
  
    const { email } = req.body;
    const { password } = req.body;
  
    const userRepository = connection!.getRepository(User);
    const activeSessionRepository = connection!.getRepository(ActiveSession);
    userRepository.findOne({ email }, {
      relations: ['role', 'driver'],
    }).then((user) => {
      if (!user) {
        return res.json({ success: false, msg: 'Wrong credentials' });
      }
  
      if (!user.password) {
        return res.json({ success: false, msg: 'No password' });
      }
  
      if(type === 'admin') {
        if(!user.role || user.role.id !== 1) {
          return res.json({ success: false, msg: 'User is not an admin' });
        }
      }

      if(type === 'driver') {
        if(!((user.role && (user.role.id === 1 || user.role.id === 2)) && user.driver && user.driver.id)) {
          return res.json({ success: false, msg: 'User is not a driver or has no driver attached.' });
        }
      }
  
      bcrypt.compare(password, user.password, (_err2, isMatch) => {
        if (isMatch) {
          if (!process.env.SECRET) {
            throw new Error('SECRET not provided');
          }
  
          const token = jwt.sign({
            id: user.id,
            username: user.username,
            email: user.email,
          }, process.env.SECRET, {
            expiresIn: 86400, // 1 week
          });
          const query = { user, token };
  
          activeSessionRepository.save(query);
          // Delete the password (hash)
          (user as { password: string | undefined }).password = undefined;
          return res.json({
            success: true,
            token,
            user,
          });
        }
        return res.json({ success: false, msg: 'Wrong credentials' });
      });
    });
  }