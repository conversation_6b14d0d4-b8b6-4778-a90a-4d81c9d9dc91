import { getRepository } from 'typeorm';
import { CarTypePrice } from '../../models/CarTypePrice';
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const createCarTypePrice = async (data: Partial<CarTypePrice>) => {
    const carTypePriceRepository = getRepository(CarTypePrice);
    try {
        const newCarTypePrice = carTypePriceRepository.create(data);
        await carTypePriceRepository.save(newCarTypePrice);
        return { success: true, data: newCarTypePrice, error: null };
    } catch (error) {
        const interpretedError = interpretDatabaseError(error);
        return { success: false, data: null, error: interpretedError };
    }
};
