import express from 'express';
import {
    deletePayment<PERSON>and<PERSON>,
    getAllPaymentsHandler,
    getPaymentHandler,
    updatePaymentHandler,
    createPaymentHandler,
    getAllStripeTransactionsHandler,
} from '../../controllers/payment';

export const paymentRouter = express.Router();

paymentRouter.post('/payments', createPaymentHandler);
paymentRouter.get('/payments', getAllPaymentsHandler);
paymentRouter.get('/payments/stripe', getAllStripeTransactionsHandler);
paymentRouter.get('/payments/:id', getPaymentHandler);
paymentRouter.put('/payments/:id', updatePaymentHandler);
paymentRouter.delete('/payments/:id', deletePaymentHandler);
