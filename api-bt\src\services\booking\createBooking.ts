import { getRepository, getConnection } from 'typeorm';
import { Booking } from '../../models/Booking';
import { Customer } from '../../models/Customer';
import { Payment } from '../../models/Payment';
import { BookingBag } from '../../models/BookingBag';
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';
import { createBookCode } from '../../utils/createBookingCode';

export const createBooking = async (data: any) => {
    const connection = getConnection();
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();

    try {
        await queryRunner.startTransaction();

        const bookingRepository = getRepository(Booking);
        const customerRepository = getRepository(Customer);
        const paymentRepository = getRepository(Payment);
        const bookingBagRepository = getRepository(BookingBag);

        // Handling customer creation or fetch
        let customer;
        if (data.customer) {
            customer = await customerRepository.findOne({ where: { id: data.customer } });
        } else {
            customer = customerRepository.create({
                first_name: data.customer_first_name,
                last_name: data.customer_last_name,
                phone_number: data.customer_phone_number,
            });
            await customerRepository.save(customer);
        }

        // Handling payment creation
        let paymentAmount = data.final_amount_to_pay;
        if (data.partial_payment !== 'full') {
            paymentAmount = data.final_amount_to_pay * 0.25;
        }

        const payment = paymentRepository.create({
            amount: data.final_amount_to_pay,
            amount_paid: paymentAmount,
            type: { id: data.payment_type }, // Assuming type is related by ID
            paid_status: data.partial_payment === 'full' && data.final_amount_to_pay === paymentAmount,
            tip: data.tip,
        });
        await paymentRepository.save(payment);

        // # generate the booking code. 
        const bookingCode = createBookCode(data.trip_type === 'hourly' ? 'H' : 'T');

        // Handling booking creation
        const newBooking = bookingRepository.create({
            ...data,
            customer: customer,
            payment: payment,
            booking_code: bookingCode,
        });
        await bookingRepository.save(newBooking);

        // Handling BookingBag entities
        for (const bag of data.bags) {
            const bookingBag = new BookingBag();
            bookingBag.booking = newBooking;
            bookingBag.bag = bag.bag;
            bookingBag.quantity = bag.quantity;
            await bookingBagRepository.save(bookingBag);
        }

        await queryRunner.commitTransaction();
        return { success: true, data: newBooking, error: null };
    } catch (error) {
        console.log('error', error)
        await queryRunner.rollbackTransaction();
        const interpretedError = interpretDatabaseError(error);
        return { success: false, data: null, error: interpretedError };
    } finally {
        await queryRunner.release();
    }
};
