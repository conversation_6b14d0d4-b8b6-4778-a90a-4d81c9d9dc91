'use client';

import { routes } from '@/config/routes';
import PageHeader from '@/app/shared/lib/page-header';
import CreateEdit from '@/app/shared/app/zones/create-edit';
import {useGetZone} from "@/services/query/zone/useGetZone";

const pageHeader = {
  title: 'Edit Zone',
  breadcrumb: [
    {
      href: routes.dashboard,
      name: 'Dashboard',
    },
    {
      href: routes.zones,
      name: 'Zones',
    },
    {
      name: 'Edit Zone',
    },
  ],
};

export default function EditShipmentsPage({
  params,
}: {
  params: { id: string };
}) {

  const id = params.id;

  const {
    isPending,
    isError,
    error,
    data,
    isFetching,
    isPlaceholderData,
    refetch,
    status,
  } = useGetZone(id);

  return (
    <>
      <PageHeader title={pageHeader.title} breadcrumb={pageHeader.breadcrumb}>
        {/*<ImportButton title={'Import File'} />*/}
      </PageHeader>

      {
        data?.data ? <CreateEdit zone={data.data} id={params.id} /> : null
      }
    </>
  );
}
