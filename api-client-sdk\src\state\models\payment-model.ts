import { types, Instance, SnapshotIn } from 'mobx-state-tree';
import { CustomerModel } from './customer-model';
import { PaymentTypeModel } from './payment-type-model';

/**
 * Payment status enum
 */
export const PaymentStatusEnum = types.enumeration('PaymentStatusEnum', [
  'pending',
  'processing',
  'completed',
  'failed',
  'refunded'
]);

/**
 * Payment model in MST
 */
export const PaymentModel = types.model('PaymentModel', {
  id: types.identifier,
  amount: types.number,
  currency: types.optional(types.string, 'USD'),
  status: types.optional(PaymentStatusEnum, 'pending'),
  payment_date: types.maybeNull(types.string),
  transaction_id: types.maybeNull(types.string),
  payment_method: types.maybeNull(types.string),
  notes: types.maybeNull(types.string),
  created_at: types.string, 
  updated_at: types.string, 
  updated_by: types.maybeNull(types.number),
  customer: types.maybeNull(types.late(() => CustomerModel)),
  payment_type: types.maybeNull(types.late(() => PaymentTypeModel)),
})
.views(self => ({
  /**
   * Get created date as Date object
   */
  get createdDate(): Date {
    return new Date(self.created_at);
  },
  
  /**
   * Get updated date as Date object
   */
  get updatedDate(): Date {
    return new Date(self.updated_at);
  },
  
  /**
   * Get payment date as Date object
   */
  get paymentDate(): Date | null {
    return self.payment_date ? new Date(self.payment_date) : null;
  },
  
  /**
   * Format the amount with currency
   */
  get formattedAmount(): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: self.currency
    }).format(self.amount);
  },
  
  /**
   * Check if payment is complete
   */
  get isComplete(): boolean {
    return self.status === 'completed';
  },
  
  /**
   * Check if payment is pending
   */
  get isPending(): boolean {
    return self.status === 'pending' || self.status === 'processing';
  },
  
  /**
   * Check if payment failed
   */
  get isFailed(): boolean {
    return self.status === 'failed';
  }
}));

// Type definitions for TypeScript
export interface Payment extends Instance<typeof PaymentModel> {}
export interface PaymentSnapshotIn extends SnapshotIn<typeof PaymentModel> {} 