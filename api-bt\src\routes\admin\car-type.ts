import express from 'express';

import {
  create<PERSON>ar<PERSON><PERSON><PERSON><PERSON><PERSON>,
  deleteCarType<PERSON><PERSON><PERSON>,
  updateCarType<PERSON><PERSON><PERSON>,
  getCarTypeHand<PERSON>,
  getAllCarTypes_handler,
} from '../../controllers/car-type'; // Adjust the path as necessary

export const carTypeRouter = express.Router();

carTypeRouter.post('/car-types', createCarTypeHandler);
carTypeRouter.delete('/car-types/:id', deleteCarTypeHandler);
carTypeRouter.get('/car-types', getAllCarTypes_handler);
carTypeRouter.get('/car-types/:id', getCarTypeHandler);
carTypeRouter.put('/car-types/:id', updateCarTypeHandler);
