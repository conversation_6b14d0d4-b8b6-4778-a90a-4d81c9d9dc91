import {getRepository} from 'typeorm';
import {CarType} from '../../models/CarType'; // Adjust the path as necessary
import {interpretDatabaseError} from '../../utils/interpretDatabaseError';

export const getCarType = async (id: string) => {
    const carTypeRepository = getRepository(CarType);
    try {
        const carType = await carTypeRepository.findOne(id, {
            relations: ['cars', 'bags', 'bags.bag', 'transfer_prices'],
        });
        if (!carType) {
            return {success: false, data: null, error: 'CarType not found'};
        }
        return {success: true, data: carType, error: null};
    } catch (error) {
        const interpretedError = interpretDatabaseError(error);
        return {success: false, data: null, error: interpretedError};
    }
};
