import {MigrationInterface, QueryRunner, TableColumn} from "typeorm";

export class addPaymentIntentFieldToPayment1714521368800 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        const paymentTable = await queryRunner.getTable('payment');
        if (paymentTable) {
            if (!paymentTable.columns.find(column => column.name === 'disabled')) {
                await queryRunner.addColumn('payment', new TableColumn({
                    name: 'payment_intent',
                    type: 'text',
                    isNullable: true,
                }));
            }
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropColumn('payment', 'payment_intent');
    }

}
