'use client';

import { useEffect, useState } from 'react';
import { Text, Input } from 'rizzui';

type AmountFieldTypes = {
  label?: string;
  value: string[];
  onChange: ([]: string[]) => void;
};

export default function AmountField({
  label = 'Amount',
  value,
  onChange,
}: AmountFieldTypes) {
  const [minAmount, setMinAmount] = useState(value[0] ?? '');
  const [maxAmount, setMaxAmount] = useState(value[1] ?? '');

  function handleMinAmount(value: string) {
    setMinAmount(() => value);
    onChange([value, maxAmount]);
  }

  function handleMaxAmount(value: string) {
    setMaxAmount(() => value);
    onChange([minAmount, value]);
  }

  useEffect(() => {
    setMinAmount(value[0]);
    setMaxAmount(value[1]);
  }, [value]);

  return (
    <div className="price-field flex items-center">
      <Text
        as="span"
        className="mr-2 whitespace-nowrap font-medium text-gray-500"
      >
        {label}
      </Text>
      <div className="flex items-center">
        <Input
          prefix={'$'}
          inputClassName="w-24 h-9"
          type="number"
          placeholder="0.00"
          min={0}
          value={minAmount}
          onChange={(event) => handleMinAmount(event.target.value)}
        />
        <Text as="span" className="mx-1.5 h-0.5 w-3 bg-gray-200" />
        <Input
          prefix={'$'}
          min={Number(minAmount)}
          inputClassName="w-24 h-9"
          type="number"
          placeholder="100.00"
          value={maxAmount}
          onChange={(event) => handleMaxAmount(event.target.value)}
          disabled={minAmount.length > 0 ? false : true}
        />
      </div>
    </div>
  );
}
