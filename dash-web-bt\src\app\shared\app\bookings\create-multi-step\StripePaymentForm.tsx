import { PaymentElement, useElements, useStripe } from "@stripe/react-stripe-js";
import React, { useState } from "react";
import { Loader } from "rizzui";

interface StripePaymentFormPropsType {
    step: number,
    onHandleSubmit: () => void,
    clientSecret: string,
}

export default function StripePaymentForm({ step, onHandleSubmit, clientSecret }: StripePaymentFormPropsType) {
    const [errorMessage, setErrorMessage] = useState<string | null>(null);
    const stripe = useStripe();
    const elements = useElements();
    const [loading, setLoading] = useState(false);

    const handleSubmit = async (event: React.FormEvent<HTMLFormElement>): Promise<void> => {
        event.preventDefault();
        if (!elements || !stripe) return;

        setLoading(true);

        const { error: submitError } = await elements.submit();
        if (submitError) {
            setErrorMessage(submitError.message || null);
            return;
        }

        if (!stripe) return;

        const { error } = await stripe.confirmPayment({
            elements,
            confirmParams: {
                return_url: `https://click4transfer.com/booking/payment-complete`,
            },
            redirect: 'if_required',
            clientSecret: clientSecret,
        });

        if (error) {
            setErrorMessage(error.message || null);
        } else {
            onHandleSubmit();
        }

        setLoading(false);
    };

    return (
        <>
            {
                loading ? (
                    <div className="flex justify-center items-center mb-4">
                        <Loader variant="pulse" size="lg" />
                        <Loader variant="threeDot" size="lg" />
                        <Loader variant="pulse" size="lg" />
                        <Loader variant="threeDot" size="lg" />
                        <Loader variant="pulse" size="lg" />
                        <Loader variant="threeDot" size="lg" />
                        <Loader variant="pulse" size="lg" />
                        <Loader variant="threeDot" size="lg" />
                        <Loader variant="pulse" size="lg" />
                    </div>
                ) : null
            }
            <form
                id={`rhf-${step.toString()}`}
                onSubmit={handleSubmit}
                className="flex-grow rounded-lg mb-16 p-8 shadow-xl text-chillGold">
                <PaymentElement className="StripeElement" />
                {errorMessage && <div className="text-red-500 mt-2">{errorMessage}</div>}
            </form>
        </>
    );
}
