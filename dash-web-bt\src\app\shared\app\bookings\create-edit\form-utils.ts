import { BookingSchemaType } from '@/utils/validators/app/entities';

export function defaultValues(booking?: Partial<BookingSchemaType>) {
  return {
    trip_type: booking?.trip_type ?? 'transfer',
    pickup_address: booking?.pickup_address ?? null,
    pickup_lat: booking?.pickup_lat ?? null,
    pickup_long: booking?.pickup_long ?? null,
    destination_address: booking?.destination_address ?? null,
    destination_lat: booking?.destination_lat ?? null,
    destination_long: booking?.destination_long ?? null,
    scheduled_at: booking?.scheduled_at ?? null,
    one_way_trip: booking?.one_way_trip ?? true,
    return_trip_time: booking?.return_trip_time ?? null,
    car_type: booking?.car_type ?? null,
    trip_distance: booking?.trip_distance ?? null,
    return_trip_distance: booking?.return_trip_distance ?? null,
    trip_duration: booking?.trip_duration ?? null,
    return_trip_duration: booking?.return_trip_duration ?? null,
    estimated_service_hours: booking?.estimated_service_hours ?? null,
    bags: booking?.bags ?? [],

    adults: booking?.adults ?? 1,
    children: booking?.children ?? 0,
    children_chairs_under_five: booking?.children_chairs_under_five ?? 0,
    children_chairs_above_five: booking?.children_chairs_above_five ?? 0,
    pets: booking?.pets ?? 0,
    flight_number: booking?.flight_number ?? null,
    other_details: booking?.other_details ?? null,

    partial_payment: booking?.partial_payment ?? 'full',
    recommended_amount_to_pay: booking?.recommended_amount_to_pay ?? null,
    final_amount_to_pay: booking?.final_amount_to_pay ?? null,
    tip: booking?.tip ?? 0,

    booking_finished_at: booking?.booking_finished_at ?? null,
    status: booking?.status ?? 'accepted',
    customer: booking?.customer ?? null,
    driver_observation: booking?.driver_observation ?? null,

    customer_first_name: booking?.customer_first_name ?? null,
    customer_last_name: booking?.customer_last_name ?? null,
    customer_phone_number: booking?.customer_phone_number ?? null,
  };
}
