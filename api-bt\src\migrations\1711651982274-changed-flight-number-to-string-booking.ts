import {MigrationInterface, QueryRunner, TableColumn} from "typeorm";

export class changedFlightNumberToStringBooking1711651982274 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        const bookingTable = await queryRunner.getTable('booking');
        if (bookingTable) {
            const flightNumberColumn = bookingTable.columns.find(column => column.name === 'flight_number');
            if (flightNumberColumn) {
                // Change flight_number column type to varchar
                await queryRunner.changeColumn('booking', 'flight_number', new TableColumn({
                    name: 'flight_number',
                    type: 'varchar',
                    isNullable: flightNumberColumn.isNullable,
                }));
            }
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        const bookingTable = await queryRunner.getTable('booking');
        if (bookingTable) {
            const flightNumberColumn = bookingTable.columns.find(column => column.name === 'flight_number');
            if (flightNumberColumn) {
                // Revert flight_number column type to int
                await queryRunner.changeColumn('booking', 'flight_number', new TableColumn({
                    name: 'flight_number',
                    type: 'int',
                    isNullable: flightNumberColumn.isNullable,
                }));
            }
        }
    }

}
