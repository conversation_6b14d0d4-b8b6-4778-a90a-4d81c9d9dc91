import {MigrationInterface, QueryRunner, TableColumn} from "typeorm";

export class addedTippedToPayment1711570698301 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        const bookingTable = await queryRunner.getTable('payment');
        if (bookingTable) {
            const columnExists = bookingTable.columns.some(column => column.name === 'tip');
            if (!columnExists) {
                // Step 1: Add the min_distance column as nullable
                await queryRunner.addColumn('payment', new TableColumn({
                    name: 'tip',
                    type: 'decimal',
                    isNullable: true,
                }));
            }
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropColumn('payment', 'tip');
    }

}
