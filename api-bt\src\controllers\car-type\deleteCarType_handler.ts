// controllers/carType.ts

import { Request, Response } from 'express';
import {
  deleteCarType,
} from '../../services/car-type'; // Adjust the path as necessary

export const deleteCarTypeHandler = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const result = await deleteCarType(id);
    if (!result.success) {
      return res.status(404).json(result);
    }
    return res.json(result);
  } catch (error: any) {
    return res.status(500).json({ error: error.message });
  }
};
