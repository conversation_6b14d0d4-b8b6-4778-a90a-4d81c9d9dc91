import { getRepository } from 'typeorm';
import { Zone } from '../../models/Zone'; // Adjust the path as necessary
import { paginate, PaginatedResult } from '../../utils/pagination';

export const getAllZones = async (
  page = 1,
  limit = 10,
): Promise<PaginatedResult<Zone>> => {
  const zoneRepository = getRepository(Zone);
  const [data, totalRecords] = await zoneRepository.findAndCount({
    skip: (page - 1) * limit,
    take: limit,
  });

  return paginate<Zone>(data, page, limit, totalRecords);
};
