// GuideSection.tsx
import React from 'react';
import Container from '@/app/(public)/(sections)/(old)/container';
import { IoMdPeople } from 'react-icons/io'; // Icon for small groups

const GuideSection: React.FC = () => {
    return (
        <section className="bg-white text-gray-800">
            <Container>
                <div className="text-center mb-4">
                    <h1 className="text-3xl font-semibold mb-4">Lo que dicen nuestros clientes</h1>
                    <p className="max-w-xl mx-auto">Tu satisfacción es nuestra prioridad. Lee lo que nuestros
                        clientes dicen de nosotros.</p>
                </div>
                <section className="text-gray-600 body-font">
                    <div className="container px-5 py-8 mx-auto flex flex-wrap">
                        <div className="flex relative pt-10 pb-20 sm:items-center md:w-2/3 mx-auto">
                            <div className="h-full w-6 absolute inset-0 flex items-center justify-center">
                                <div className="h-full w-1 bg-gray-200 pointer-events-none"></div>
                            </div>
                            <div
                                className="flex-shrink-0 w-6 h-6 rounded-full mt-10 sm:mt-0 inline-flex items-center justify-center bg-red-500 text-white relative z-10 title-font font-medium text-sm">1
                            </div>
                            <div
                                className="flex-grow md:pl-8 pl-6 flex sm:items-center items-start flex-col sm:flex-row">
                                <div
                                    className="flex-shrink-0 w-24 h-24 bg-red-100 text-red-500 rounded-full inline-flex items-center justify-center">
                                    <svg fill="none" stroke="currentColor" strokeLinecap="round"
                                         strokeLinejoin="round" strokeWidth="2" className="w-12 h-12"
                                         viewBox="0 0 24 24">
                                        <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                                    </svg>
                                </div>
                                <div className="flex-grow sm:pl-6 mt-6 sm:mt-0">
                                    <h2 className="font-medium title-font text-gray-900 mb-1 text-xl">Shooting
                                        Stars</h2>
                                    <p className="leading-relaxed">VHS cornhole pop-up, try-hard 8-bit iceland
                                        helvetica. Kinfolk bespoke try-hard cliche palo santo offal.</p>
                                </div>
                            </div>
                        </div>
                        <div className="flex relative pb-20 sm:items-center md:w-2/3 mx-auto">
                            <div className="h-full w-6 absolute inset-0 flex items-center justify-center">
                                <div className="h-full w-1 bg-gray-200 pointer-events-none"></div>
                            </div>
                            <div
                                className="flex-shrink-0 w-6 h-6 rounded-full mt-10 sm:mt-0 inline-flex items-center justify-center bg-red-500 text-white relative z-10 title-font font-medium text-sm">2
                            </div>
                            <div
                                className="flex-grow md:pl-8 pl-6 flex sm:items-center items-start flex-col sm:flex-row">
                                <div
                                    className="flex-shrink-0 w-24 h-24 bg-red-100 text-red-500 rounded-full inline-flex items-center justify-center">
                                    <svg fill="none" stroke="currentColor" strokeLinecap="round"
                                         strokeLinejoin="round" strokeWidth="2" className="w-12 h-12"
                                         viewBox="0 0 24 24">
                                        <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
                                    </svg>
                                </div>
                                <div className="flex-grow sm:pl-6 mt-6 sm:mt-0">
                                    <h2 className="font-medium title-font text-gray-900 mb-1 text-xl">The Catalyzer</h2>
                                    <p className="leading-relaxed">VHS cornhole pop-up, try-hard 8-bit iceland
                                        helvetica. Kinfolk bespoke try-hard cliche palo santo offal.</p>
                                </div>
                            </div>
                        </div>
                        <div className="flex relative pb-20 sm:items-center md:w-2/3 mx-auto">
                            <div className="h-full w-6 absolute inset-0 flex items-center justify-center">
                                <div className="h-full w-1 bg-gray-200 pointer-events-none"></div>
                            </div>
                            <div
                                className="flex-shrink-0 w-6 h-6 rounded-full mt-10 sm:mt-0 inline-flex items-center justify-center bg-red-500 text-white relative z-10 title-font font-medium text-sm">3
                            </div>
                            <div
                                className="flex-grow md:pl-8 pl-6 flex sm:items-center items-start flex-col sm:flex-row">
                                <div
                                    className="flex-shrink-0 w-24 h-24 bg-red-100 text-red-500 rounded-full inline-flex items-center justify-center">
                                    <svg fill="none" stroke="currentColor" strokeLinecap="round"
                                         strokeLinejoin="round" strokeWidth="2" className="w-12 h-12"
                                         viewBox="0 0 24 24">
                                        <circle cx="12" cy="5" r="3"></circle>
                                        <path d="M12 22V8M5 12H2a10 10 0 0020 0h-3"></path>
                                    </svg>
                                </div>
                                <div className="flex-grow sm:pl-6 mt-6 sm:mt-0">
                                    <h2 className="font-medium title-font text-gray-900 mb-1 text-xl">The 400 Blows</h2>
                                    <p className="leading-relaxed">VHS cornhole pop-up, try-hard 8-bit iceland
                                        helvetica. Kinfolk bespoke try-hard cliche palo santo offal.</p>
                                </div>
                            </div>
                        </div>
                        <div className="flex relative pb-10 sm:items-center md:w-2/3 mx-auto">
                            <div className="h-full w-6 absolute inset-0 flex items-center justify-center">
                                <div className="h-full w-1 bg-gray-200 pointer-events-none"></div>
                            </div>
                            <div
                                className="flex-shrink-0 w-6 h-6 rounded-full mt-10 sm:mt-0 inline-flex items-center justify-center bg-red-500 text-white relative z-10 title-font font-medium text-sm">4
                            </div>
                            <div
                                className="flex-grow md:pl-8 pl-6 flex sm:items-center items-start flex-col sm:flex-row">
                                <div
                                    className="flex-shrink-0 w-24 h-24 bg-red-100 text-red-500 rounded-full inline-flex items-center justify-center">
                                    <svg fill="none" stroke="currentColor" strokeLinecap="round"
                                         strokeLinejoin="round" strokeWidth="2" className="w-12 h-12"
                                         viewBox="0 0 24 24">
                                        <path d="M20 21v-2a4 4 0 00-4-4H8a4 4 0 00-4 4v2"></path>
                                        <circle cx="12" cy="7" r="4"></circle>
                                    </svg>
                                </div>
                                <div className="flex-grow sm:pl-6 mt-6 sm:mt-0">
                                    <h2 className="font-medium title-font text-gray-900 mb-1 text-xl">Neptune</h2>
                                    <p className="leading-relaxed">VHS cornhole pop-up, try-hard 8-bit iceland
                                        helvetica. Kinfolk bespoke try-hard cliche palo santo offal.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </Container>
        </section>
    );
};

export default GuideSection;
