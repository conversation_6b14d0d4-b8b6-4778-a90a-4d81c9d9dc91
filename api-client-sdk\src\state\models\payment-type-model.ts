import { types, Instance, SnapshotIn } from 'mobx-state-tree';

/**
 * Payment Type model in MST
 */
export const PaymentTypeModel = types.model('PaymentTypeModel', {
  id: types.identifierNumber,
  name: types.string,
  description: types.maybeNull(types.string),
  created_at: types.string, 
  updated_at: types.string, 
  updated_by: types.maybeNull(types.number),
})
.views(self => ({
  /**
   * Get created date as Date object
   */
  get createdDate(): Date {
    return new Date(self.created_at);
  },
  
  /**
   * Get updated date as Date object
   */
  get updatedDate(): Date {
    return new Date(self.updated_at);
  }
}));

// Type definitions for TypeScript
export interface PaymentType extends Instance<typeof PaymentTypeModel> {}
export interface PaymentTypeSnapshotIn extends SnapshotIn<typeof PaymentTypeModel> {} 