import { jest, describe, it, expect } from '@jest/globals';
import { apiClient } from '../../core/api-client';
import { normalizeResponse, isValidResponse } from '../../core/response-utils';

// Mock the API client
jest.mock('../../core/api-client', () => ({
  apiClient: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn()
  }
}));

const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;

describe('API Response Format', () => {
  describe('isValidResponse', () => {
    it('should validate a valid response', () => {
      const validResponse = {
        success: true,
        data: { id: 1, name: 'Test' },
        message: 'Success'
      };
      
      expect(isValidResponse(validResponse)).toBe(true);
    });
    
    it('should invalidate a response without success field', () => {
      const invalidResponse = {
        data: { id: 1, name: 'Test' },
        message: 'Success'
      };
      
      expect(isValidResponse(invalidResponse)).toBe(false);
    });
    
    it('should invalidate a response without data field', () => {
      const invalidResponse = {
        success: true,
        message: 'Success'
      };
      
      expect(isValidResponse(invalidResponse)).toBe(false);
    });
    
    it('should validate an error response', () => {
      const errorResponse = {
        success: false,
        error: 'Not found',
        message: 'Resource not found'
      };
      
      expect(isValidResponse(errorResponse, true)).toBe(true);
    });
  });
  
  describe('normalizeResponse', () => {
    it('should normalize a standard response', () => {
      const rawResponse = {
        data: {
          success: true,
          data: { id: 1, name: 'Test' },
          message: 'Success'
        }
      };
      
      const normalized = normalizeResponse(rawResponse);
      
      expect(normalized).toEqual({
        success: true,
        data: { id: 1, name: 'Test' },
        message: 'Success'
      });
    });
    
    it('should handle nested data structure', () => {
      const rawResponse = {
        data: {
          success: true,
          data: {
            users: [
              { id: 1, name: 'User 1' },
              { id: 2, name: 'User 2' }
            ],
            pagination: {
              page: 1,
              limit: 10,
              total: 2
            }
          },
          message: 'Success'
        }
      };
      
      const normalized = normalizeResponse(rawResponse);
      
      expect(normalized).toEqual({
        success: true,
        data: {
          users: [
            { id: 1, name: 'User 1' },
            { id: 2, name: 'User 2' }
          ],
          pagination: {
            page: 1,
            limit: 10,
            total: 2
          }
        },
        message: 'Success'
      });
    });
    
    it('should normalize an error response', () => {
      const rawResponse = {
        data: {
          success: false,
          error: 'Invalid input',
          message: 'Validation failed'
        }
      };
      
      const normalized = normalizeResponse(rawResponse);
      
      expect(normalized).toEqual({
        success: false,
        error: 'Invalid input',
        message: 'Validation failed'
      });
    });
    
    it('should handle axios error response', () => {
      const axiosError = {
        isAxiosError: true,
        response: {
          data: {
            success: false,
            error: 'Server error',
            message: 'Internal server error'
          },
          status: 500
        }
      };
      
      const normalized = normalizeResponse(axiosError);
      
      expect(normalized).toEqual({
        success: false,
        error: 'Server error',
        message: 'Internal server error',
        status: 500
      });
    });
    
    it('should handle network errors', () => {
      const networkError = {
        isAxiosError: true,
        message: 'Network Error'
      };
      
      const normalized = normalizeResponse(networkError);
      
      expect(normalized).toEqual({
        success: false,
        error: 'Network Error',
        message: 'Unable to connect to the server'
      });
    });
    
    it('should handle unexpected response formats', () => {
      const unexpectedResponse = {
        data: 'Not a proper response object'
      };
      
      const normalized = normalizeResponse(unexpectedResponse);
      
      expect(normalized).toEqual({
        success: false,
        error: 'Invalid response format',
        message: 'Received an unexpected response format from the server'
      });
    });
  });
  
  describe('API client response integration', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });
    
    it('should correctly process successful GET response', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: { id: 1, name: 'Test' },
          message: 'Resource retrieved successfully'
        }
      };
      
      mockApiClient.get.mockResolvedValue(mockResponse);
      
      const result = await apiClient.get('/test');
      const normalized = normalizeResponse(result);
      
      expect(mockApiClient.get).toHaveBeenCalledWith('/test');
      expect(normalized.success).toBe(true);
      expect(normalized.data).toEqual({ id: 1, name: 'Test' });
    });
    
    it('should correctly process error response', async () => {
      const mockErrorResponse = {
        isAxiosError: true,
        response: {
          data: {
            success: false,
            error: 'Not found',
            message: 'Resource not found'
          },
          status: 404
        }
      };
      
      mockApiClient.get.mockRejectedValue(mockErrorResponse);
      
      try {
        await apiClient.get('/test/999');
        // Should not reach here
        expect(true).toBe(false);
      } catch (error) {
        const normalized = normalizeResponse(error);
        expect(normalized.success).toBe(false);
        expect(normalized.error).toBe('Not found');
        expect(normalized.status).toBe(404);
      }
    });
    
    it('should correctly process POST response', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: { id: 1, name: 'New Resource' },
          message: 'Resource created successfully'
        }
      };
      
      mockApiClient.post.mockResolvedValue(mockResponse);
      
      const result = await apiClient.post('/test', { name: 'New Resource' });
      const normalized = normalizeResponse(result);
      
      expect(mockApiClient.post).toHaveBeenCalledWith('/test', { name: 'New Resource' });
      expect(normalized.success).toBe(true);
      expect(normalized.data).toEqual({ id: 1, name: 'New Resource' });
      expect(normalized.message).toBe('Resource created successfully');
    });
  });
}); 