// controllers/driver/updateTicket_handler.ts
import { Request, Response } from 'express';
import { updateDriver } from '../../services/driver';

export const updateDriver_handler = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    const result = await updateDriver(id, updateData);
    if (!result.success) {
      return res.status(404).json(result);
    }
    return res.json(result);
  } catch (error: any) {
    return res.status(500).json({ error: error.message });
  }
};
