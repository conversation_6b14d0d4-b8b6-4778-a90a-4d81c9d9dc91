import { ReactNode } from 'react';
import classNames from 'classnames';
import { Title, Text, Badge, Button } from 'rizzu<PERSON>';
import { FaCalendar, FaPlane, FaMarker, Fa<PERSON>ser, FaEnvelope, FaPhone } from 'react-icons/fa6';
import { useRouter } from 'next/navigation';
import { BookingSchemaType, TicketSchemaType } from '@/utils/validators/app/entities';
import { routes } from "@/config/routes";

interface CardProps {
    children: ReactNode;
    className?: string;
    color?: string;
}

interface HasCreatedAt {
    created_at: string | Date | number;
}

function Card({ children, className, color }: CardProps) {
    return (
        <div className={classNames('shadow-md rounded-lg p-4', color || 'bg-gray-50', className)}>
            {children}
        </div>
    );
}

interface Props {
    first_name: string;
    email: string;
    phone_number: string;
    bookings: BookingSchemaType[];
    tickets: TicketSchemaType[];
}

export default function ExpandedOrderRow({ first_name, email, phone_number, bookings, tickets }: Props) {
    const router = useRouter();

    const navigateToBookingEdit = (id: string) => {
        router.push(routes.bookingEdit(id));
    };

    const navigateToTicketEdit = (id: string) => {
        router.push(routes.ticketEdit(id));
    };

    function sortByCreatedAt(array: BookingSchemaType[], order: 'asc' | 'desc' = 'asc') {
        return array.sort((a, b) => {
            const dateA = a.created_at ? new Date(a.created_at).getTime() : 0;
            const dateB = b.created_at ? new Date(b.created_at).getTime() : 0;

            if (order === 'asc') {
                return dateA - dateB;
            } else {
                return dateB - dateA;
            }
        });
    }

    return (
        <div className="space-y-4">
            <div className="space-y-2">
                <Card className="space-y-3" color="bg-blue-50">
                    <div className="flex items-center space-x-2 text-blue-800">
                        <FaUser />
                        <Text className="font-medium">Name:</Text>
                        <Text className="font-semibold">{first_name || 'N/A'}</Text>
                    </div>
                    <div className="flex items-center space-x-2 text-blue-800">
                        <FaEnvelope />
                        <Text className="font-medium">Email:</Text>
                        <Text className="font-semibold">{email || 'N/A'}</Text>
                    </div>
                    <div className="flex items-center space-x-2 text-blue-800">
                        <FaPhone />
                        <Text className="font-medium">Phone:</Text>
                        <Text className="font-semibold">{phone_number || 'N/A'}</Text>
                    </div>
                </Card>
            </div>

            {bookings && bookings.length > 0 && (
                <div className="space-y-2">
                    <Title as="h3" className="text-lg font-semibold text-blue-600">Bookings ({bookings.length})</Title>
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                        {sortByCreatedAt(bookings, 'desc').map((booking) => (
                            <Card key={booking.id} className="space-y-3" color="bg-white border border-blue-200">
                                <div className="flex items-center space-x-2 text-gray-600">
                                    <span className="font-semibold text-blue-600">Booking Status</span>
                                    <Badge color={booking.status === 'completed' ? 'success' : 'primary'}>{booking.status}</Badge>
                                </div>
                                <div className="flex items-center space-x-2 text-gray-600">
                                    <Badge color="primary">Booking Code</Badge>
                                    <span className="font-semibold text-blue-600">{booking.booking_code}</span>
                                </div>
                                <div className="flex items-center space-x-2 text-gray-600">
                                    <FaCalendar className="text-blue-600"/>
                                    <Text className="font-medium">Scheduled at:</Text>
                                    <Text className="font-semibold">
                                        {booking.scheduled_at ? new Date(booking.scheduled_at).toLocaleDateString() : 'N/A'}
                                    </Text>
                                </div>
                                <div className="flex items-center space-x-2 text-gray-600">
                                    <FaPlane className="text-blue-600"/>
                                    <Text className="font-medium">Flight number:</Text>
                                    <Text className="font-semibold">{booking.flight_number || 'N/A'}</Text>
                                </div>
                                <div className="flex items-center space-x-2 text-gray-600">
                                    <FaMarker className="text-blue-600"/>
                                    <Text className="font-medium">Pickup:</Text>
                                    <Text className="font-semibold">{booking.pickup_address || 'N/A'}</Text>
                                </div>
                                <div className="flex items-center space-x-2 text-gray-600">
                                    <FaMarker className="text-blue-600"/>
                                    <Text className="font-medium">Destination:</Text>
                                    <Text className="font-semibold">{booking.destination_address || 'N/A'}</Text>
                                </div>
                                <Button
                                    onClick={() => navigateToBookingEdit(booking.id as string)}
                                    className="text-sm hover:underline"
                                >
                                    Edit Booking
                                </Button>
                            </Card>
                        ))}
                    </div>
                </div>
            )}

            {tickets && tickets.length > 0 && (
                <div className="space-y-2">
                    <Title as="h3" className="text-lg font-semibold text-green-600">Tickets ({tickets.length})</Title>
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                        {tickets.map((ticket) => (
                            <Card key={ticket.id} className="space-y-3" color="bg-white border border-green-200">
                                <div className="flex items-center space-x-2 text-gray-600">
                                    <Badge color="success">Booking Code</Badge>
                                    <span className="font-semibold text-green-600">{ticket.booking_code}</span>
                                </div>
                                <div className="flex items-center space-x-2 text-gray-600">
                                    <Text className="font-medium">Message:</Text>
                                    <Text className="font-semibold">{ticket.message || 'No message available'}</Text>
                                </div>
                                {ticket.created_at && (
                                    <div className="flex items-center space-x-2 text-gray-600">
                                        <FaCalendar className="text-green-600" />
                                        <Text className="font-medium">Created at:</Text>
                                        <Text className="font-semibold">
                                            {ticket.created_at ? new Date(ticket.created_at).toLocaleDateString() : 'N/A'}
                                        </Text>
                                    </div>
                                )}
                                <Button
                                    onClick={() => navigateToTicketEdit(ticket.id as string)}
                                    className="text-sm hover:underline"
                                >
                                    Edit Ticket
                                </Button>
                            </Card>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
}
