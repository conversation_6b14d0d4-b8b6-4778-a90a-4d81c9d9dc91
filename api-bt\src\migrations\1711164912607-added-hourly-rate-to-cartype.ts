import {MigrationInterface, QueryRunner, TableColumn} from "typeorm";

export class addedHourlyRateToCartype1711164912607 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        const carTypeTable = await queryRunner.getTable('car_type');
        if (carTypeTable) {
            const columnExists = carTypeTable.columns.some(column => column.name === 'hourly_rate');
            if (!columnExists) {
                // Step 1: Add the hourly_rate column as nullable
                await queryRunner.addColumn('car_type', new TableColumn({
                    name: 'hourly_rate',
                    type: 'float',
                    isNullable: true,
                }));

                // Step 2: Update existing records to ensure no null values for the new column
                await queryRunner.query(`UPDATE "car_type" SET "hourly_rate" = 0 WHERE "hourly_rate" IS NULL;`);

                // Step 3: Alter the column to be non-nullable now that all rows have a value
                await queryRunner.changeColumn('car_type', 'hourly_rate', new TableColumn({
                    name: 'hourly_rate',
                    type: 'float',
                    isNullable: false, // or you might omit isNullable for default non-nullable behavior
                }));
            }
        }
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropColumn('car_type', 'hourly_rate');
    }
}
