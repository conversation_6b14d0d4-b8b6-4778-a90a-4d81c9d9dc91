import { getRepository } from 'typeorm';
import { PaymentType } from '../../models/PaymentType'; // Adjust the path as necessary
export const deletePaymentType = async (id: string) => {
  const paymentTypeRepository = getRepository(PaymentType);
  try {
    const deleteResult = await paymentTypeRepository.delete(id);
    if (deleteResult.affected === 0) {
      return { success: false, data: null, error: 'PaymentType not found' };
    }
    return { success: true, data: { id }, error: null };
  } catch (error: any) {
    return { success: false, data: null, error: error.message };
  }
};
