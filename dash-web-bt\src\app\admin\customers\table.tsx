'use client';

import React, {useCallback, useEffect, useMemo, useState} from 'react';
import dynamic from 'next/dynamic';
import {useTable} from '@/hooks/use-table';
import {useColumn} from '@/hooks/use-column';
import {PiCaretDownBold, PiCaretUpBold} from 'react-icons/pi';
import ControlledTable from 'src/components/lib/controlled-table';
import {getColumns} from './columns';
import {ActionIcon, Button} from 'rizzui';
import cn from '@/utils/class-names';
import ExpandedBookingRow from './expanded-row';
import {useDeleteCustomer} from "@/services/mutations/customer";
import axios from "axios";
import toast from "react-hot-toast";

// dynamic import
const FilterElement = dynamic(
    () => import('./filter-element'),
    {ssr: false}
);

function CustomExpandIcon(props: any) {
    return (
        <ActionIcon
            size="sm"
            variant="outline"
            rounded="full"
            className="expand-row-icon ms-2"
            onClick={(e) => {
                props.onExpand(props.record, e);
            }}
        >
            {props.expanded ? (
                <PiCaretUpBold className="h-3.5 w-3.5"/>
            ) : (
                <PiCaretDownBold className="h-3.5 w-3.5"/>
            )}
        </ActionIcon>
    );
}

const filterState = {
    amount_to_pay: ['', ''],
    scheduled_at: [null, null],
    paid_status: false,
};

const TableFooter = dynamic(() => import('@/app/shared/lib/table-footer'), {
    ssr: false,
});

export default function ScheduleTable({
                                          initialData,
                                          onSelectedPage,
                                          onPageLimitChange,
                                          variant = 'modern',
                                          className,
                                      }: {
    initialData: any;
    variant?: 'modern' | 'minimal' | 'classic' | 'elegant' | 'retro';
    className?: string;
    onSelectedPage: (page: number) => void;
    onPageLimitChange: (limit: number) => void;
}) {
    const [pageSize, setPageSize] = useState(initialData.limit);
    const deleteCustomer = useDeleteCustomer();

    const onHeaderCellClick = (value: string) => ({
        onClick: () => {
            handleSort(value);
        },
    });

    const onDeleteItem = useCallback((id: string) => {
        deleteCustomer.mutate(id, {
            onError: (error: any) => {
                // Check if the error is an AxiosError and has a response
                if (axios.isAxiosError(error) && error.response) {
                    // Now TypeScript knows error.response exists and its structure
                    toast.error(error.response.data.msg);
                } else {
                    // Handle other errors
                    toast.error("An error occurred");
                }
            },
            onSuccess: () => {
                handleDelete(id);
            },
        })
    }, []);

    const {
        isLoading,
        isFiltered,
        tableData,
        currentPage,
        totalItems,
        handlePaginate,
        filters,
        updateFilter,
        searchTerm,
        handleSearch,
        sortConfig,
        handleSort,
        selectedRowKeys,
        setSelectedRowKeys,
        handleRowSelect,
        handleSelectAll,
        handleDelete,
        handleReset,
    } = useTable(initialData.data, pageSize, filterState);

    const columns = React.useMemo(
        () =>
            getColumns({
                data: initialData,
                sortConfig,
                checkedItems: selectedRowKeys,
                onHeaderCellClick,
                onDeleteItem,
                onChecked: handleRowSelect,
                handleSelectAll,
            }),
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [
            selectedRowKeys,
            onHeaderCellClick,
            sortConfig.key,
            sortConfig.direction,
            onDeleteItem,
            handleRowSelect,
            handleSelectAll,
        ]
    );

    const {visibleColumns, checkedColumns, setCheckedColumns} =
        useColumn(columns);

    useEffect(() => {
        onPageLimitChange(pageSize)
    }, [pageSize])

    return (
        <div className={cn('table-wrapper flex-grow')}>
            <ControlledTable
                variant="modern"
                data={tableData}
                isLoading={isLoading}
                showLoadingText={true}
                // @ts-ignore
                columns={visibleColumns}
                expandable={{
                    expandIcon: CustomExpandIcon,
                    expandedRowRender: (record) => {
                        return <ExpandedBookingRow bookings={record.orders} tickets={record.tickets} first_name={record.first_name} email={record.email} phone_number={record.phone_number} />
                    },
                }}
                paginatorOptions={{
                    pageSize: initialData.limit,
                    setPageSize,
                    total: initialData.count,
                    current: currentPage,
                    onChange: (page: number) => {
                        onSelectedPage(page);
                        handlePaginate(page)
                    },
                }}
                filterOptions={{
                    searchTerm,
                    onSearchClear: () => {
                        handleSearch('');
                    },
                    onSearchChange: (event) => {
                        handleSearch(event.target.value);
                    },
                    hasSearched: isFiltered,
                    columns,
                    checkedColumns,
                    setCheckedColumns,
                }}
                // filterElement={
                //     <FilterElement
                //         isFiltered={isFiltered}
                //         filters={filters}
                //         updateFilter={(columnId, filterValue) => {
                //             updateFilter(columnId, filterValue);

                //         }}
                //         handleReset={handleReset}
                //     />
                // }
                tableFooter={
                    <TableFooter
                        checkedItems={selectedRowKeys}
                        handleDelete={(ids: string[]) => {
                            setSelectedRowKeys([]);
                            handleDelete(ids);
                        }}
                    >
                        <Button size="sm" className="dark:bg-gray-300 dark:text-gray-800">
                            Re-send {selectedRowKeys.length}{' '}
                            {selectedRowKeys.length > 1 ? 'Bags' : 'Bag'}{' '}
                        </Button>
                    </TableFooter>
                }
                className="rounded-md border border-muted text-sm shadow-sm [&_.rc-table-placeholder_.rc-table-expanded-row-fixed>div]:h-60 [&_.rc-table-placeholder_.rc-table-expanded-row-fixed>div]:justify-center [&_.rc-table-row:last-child_td.rc-table-cell]:border-b-0 [&_thead.rc-table-thead]:border-t-0"
            />
        </div>
    );
}
