import { jest, describe, it, expect, beforeEach } from '@jest/globals';
import { authService } from '../../services/auth-service';
import { apiClient } from '../../core/api-client';

// Manually mock the API client instead of using jest.mock
const mockApiClient = {
  post: jest.fn(),
  setAuthToken: jest.fn(),
  clearAuthToken: jest.fn(),
};

// Replace the real apiClient with our mock
Object.assign(apiClient, mockApiClient);

describe('AuthService', () => {
  const mockUser = {
    id: 1,
    username: 'testuser',
    email: '<EMAIL>',
    first_name: 'Test',
    last_name: 'User',
    created_at: new Date(), // Use a Date object instead of string
  };

  const mockAuthResponse = {
    success: true,
    token: 'test-token',
    user: mockUser,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Make sure we start with a clean state
    authService['currentSession'] = null;
  });

  describe('login', () => {
    it('should login successfully', async () => {
      // Mock successful login response
      mockApiClient.post.mockResolvedValue({
        success: true,
        data: mockAuthResponse,
      });

      const credentials = { email: '<EMAIL>', password: 'password' };
      const result = await authService.login(credentials);

      expect(mockApiClient.post).toHaveBeenCalledWith('/auth/login', credentials);
      expect(mockApiClient.setAuthToken).toHaveBeenCalledWith('test-token');
      
      // Use toMatchObject instead of toEqual for partial matching
      expect(result).toMatchObject({
        success: true,
        token: 'test-token',
        user: {
          id: mockUser.id,
          username: mockUser.username,
          email: mockUser.email,
        }
      });
      
      expect(authService.isAuthenticated()).toBe(true);
      
      // Use toMatchObject for user comparison
      expect(authService.getCurrentUser()).toMatchObject({
        id: mockUser.id,
        username: mockUser.username,
        email: mockUser.email,
      });
    });

    it('should handle login failure', async () => {
      const errorMsg = 'Invalid credentials';
      
      // Mock failed login response
      mockApiClient.post.mockResolvedValue({
        success: true,
        data: { success: false, msg: errorMsg },
      });

      const credentials = { email: '<EMAIL>', password: 'wrong' };
      const result = await authService.login(credentials);

      expect(mockApiClient.post).toHaveBeenCalledWith('/auth/login', credentials);
      expect(mockApiClient.setAuthToken).not.toHaveBeenCalled();
      expect(result).toEqual({ success: false, msg: errorMsg });
      expect(authService.isAuthenticated()).toBe(false);
      expect(authService.getCurrentUser()).toBeNull();
    });

    it('should handle unexpected errors', async () => {
      // Mock error
      mockApiClient.post.mockRejectedValue(new Error('Network error'));

      const credentials = { email: '<EMAIL>', password: 'password' };
      const result = await authService.login(credentials);

      expect(mockApiClient.post).toHaveBeenCalledWith('/auth/login', credentials);
      expect(result.success).toBe(false);
      expect(result.msg).toBeDefined();
    });
  });

  describe('logout', () => {
    it('should logout successfully', async () => {
      // Setup authenticated state
      authService['currentSession'] = {
        token: 'test-token',
        user: mockUser,
      };

      // Mock successful logout response
      mockApiClient.post.mockResolvedValue({
        success: true,
        data: { success: true },
      });

      const result = await authService.logout();

      expect(mockApiClient.post).toHaveBeenCalledWith('/auth/logout', { token: 'test-token' });
      expect(mockApiClient.clearAuthToken).toHaveBeenCalled();
      expect(result.success).toBe(true);
      expect(authService.isAuthenticated()).toBe(false);
      expect(authService.getCurrentUser()).toBeNull();
    });

    it('should handle no active session', async () => {
      // Ensure no active session
      authService['currentSession'] = null;

      const result = await authService.logout();

      expect(mockApiClient.post).not.toHaveBeenCalled();
      expect(result.success).toBe(false);
      expect(result.msg).toBe('No active session');
    });
  });

  describe('session management', () => {
    it('should restore session', () => {
      const session = { token: 'test-token', user: mockUser };
      authService.restoreSession(session);

      expect(mockApiClient.setAuthToken).toHaveBeenCalledWith('test-token');
      
      // Use toMatchObject for the session comparison
      expect(authService.getSession()).toMatchObject({
        token: 'test-token',
        user: {
          id: mockUser.id,
          username: mockUser.username,
          email: mockUser.email,
        }
      });
      
      expect(authService.isAuthenticated()).toBe(true);
      
      // Use toMatchObject for user comparison
      expect(authService.getCurrentUser()).toMatchObject({
        id: mockUser.id,
        username: mockUser.username,
        email: mockUser.email,
      });
    });

    it('should clear session', () => {
      // Setup authenticated state
      authService['currentSession'] = {
        token: 'test-token',
        user: mockUser,
      };

      authService.clearSession();

      expect(mockApiClient.clearAuthToken).toHaveBeenCalled();
      expect(authService.getSession()).toBeNull();
      expect(authService.isAuthenticated()).toBe(false);
      expect(authService.getCurrentUser()).toBeNull();
    });
  });
}); 