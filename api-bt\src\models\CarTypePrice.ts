import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne } from 'typeorm';
import { CarType } from "./CarType";

@Entity()
export class CarTypePrice {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ length: 50 })
  name: string;

  @Column('decimal')
  price_per_km: number;

  @Column('int')
  range_max_distance: number;

  @Column('int4')
  fixed_charge_value: number;

  @ManyToOne(() => CarType, carType => carType.transfer_prices, { onDelete: 'CASCADE' })
  car_type: CarType;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ nullable: true })
  updated_by: number;
}
