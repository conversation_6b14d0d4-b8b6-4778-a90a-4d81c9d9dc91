import { getRepository } from 'typeorm';
import { Role } from '../../models/Role';
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const updateRole = async (id: string, updateData: Partial<Role>) => {
  const roleRepository = getRepository(Role);

  try {
    const updateResult = await roleRepository.update(id, updateData);
    if (updateResult.affected === 0) {
      return { success: false, data: null, error: 'Role not found' };
    }

    const updatedRole = await roleRepository.findOne(id, { relations: ["users"] });
    return { success: true, data: updatedRole, error: null };
  } catch (error) {
    const interpretedError = interpretDatabaseError(error);
    return { success: false, data: null, error: interpretedError };
  }
};