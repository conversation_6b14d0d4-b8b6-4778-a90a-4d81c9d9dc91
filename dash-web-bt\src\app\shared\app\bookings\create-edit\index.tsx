'use client';

import { useState } from 'react';
import toast from 'react-hot-toast';
import { Element } from 'react-scroll';
import { zodResolver } from '@hookform/resolvers/zod';
import { SubmitHandler, useForm, FormProvider } from 'react-hook-form';
import FormFooter from '@/components/lib/form-footer';

import JourneyDetails from '@/app/shared/app/bookings/create-edit/journey-details';
import PassengerDetails from '@/app/shared/app/bookings/create-edit/passenger-details';
import PaymentDetails from '@/app/shared/app/bookings/create-edit/payment-details';
import BookingDetails from '@/app/shared/app/bookings/create-edit/booking-details';

import FormNav, {
  FormParts,
} from '@/app/shared/app/bookings/create-edit/form-nav';
import { defaultValues } from '@/app/shared/app/bookings/create-edit/form-utils';
import cn from '@/utils/class-names';
import {
  BookingSchema,
  BookingSchemaType,
} from '@/utils/validators/app/entities/booking';
import { useLayout } from '@/hooks/use-layout';
import { LAYOUT_OPTIONS } from '@/config/enums';
import {useUpdateBooking, useCreateBooking} from "@/services/mutations/booking/admin";
import {routes} from "@/config/routes";
import {useRouter} from "next/navigation";

const MAP_STEP_TO_COMPONENT = {
  [FormParts.BookingDetails]: BookingDetails,
  [FormParts.JourneyDetails]: JourneyDetails,
  [FormParts.PassengerDetails]: PassengerDetails,
  [FormParts.PaymentDetails]: PaymentDetails,
};

interface IndexProps {
  id?: string;
  className?: string;
  booking?: Partial<BookingSchemaType>;
}

export default function CreateEdit({
  id,
  booking,
  className,
}: IndexProps) {
  const { layout } = useLayout();
  const [isLoading, setLoading] = useState(false);

  const updateMutation = useUpdateBooking();
  const createMutation = useCreateBooking();

  const router = useRouter();

  const methods = useForm({
    defaultValues: defaultValues(booking),
    resolver: zodResolver(BookingSchema),
  });

  const onSubmit: SubmitHandler<BookingSchemaType> = (data) => {
    // set timeout ony required to display loading state of the create product button
    setLoading(true);

    if(id) {
      updateMutation.mutate({
            id, payload: data
          },
          {
            onSuccess: () => {
              toast.success('Booking Updated Successfully');
              methods.reset();
              setLoading(false);
              router.push(routes.bookings);
            },
            onError: (error) => {
              console.log('error', error);
              setLoading(false);
              toast.error('Error updating booking');
            }
          }
      )
    } else {
      createMutation.mutate({
        payload: data
      }, {
        onSuccess: () => {
          toast.success('Booking Created Successfully');
          methods.reset();
          // redirect to the bookings list page
          setLoading(false);
          router.push(routes.bookings);
        },
        onError: (error: any) => {
          console.log('error', error);
          setLoading(false);
          toast.error(error?.response?.data.error);
        }
      })
    }
  };

  console.log('errors', methods.formState.errors);

  return (
    <div className="@container">
      <FormNav
        className={cn(layout === LAYOUT_OPTIONS.BERYLLIUM && '2xl:top-[72px]')}
      />
      <FormProvider {...methods}>
        <form
          className={cn('mt-6', className)}
          onSubmit={methods.handleSubmit((data, event) => onSubmit(data, event))}
        >
          <div className="mb-10 grid gap-7 divide-y divide-dashed divide-gray-200 @2xl:gap-9 @3xl:gap-11">
            {Object.entries(MAP_STEP_TO_COMPONENT).map(([key, Component]) => (
              <Element
                key={key}
                name={FormParts[key as keyof typeof FormParts]}
              >
                <Component className="pt-7 @2xl:pt-9 @3xl:pt-11" />
              </Element>
            ))}
          </div>
          <FormFooter
            isLoading={isLoading}
            submitBtnText={id ? 'Update Booking' : 'Create Booking'}
          />
        </form>
      </FormProvider>
    </div>
  );
}
