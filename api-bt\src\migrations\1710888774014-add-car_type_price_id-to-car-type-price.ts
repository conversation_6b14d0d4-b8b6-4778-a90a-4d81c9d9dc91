import {MigrationInterface, QueryRunner, TableC<PERSON>umn, TableForeign<PERSON><PERSON>} from "typeorm";

export class addCarTypePriceIdToCarTypePrice1710888774014 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add the car_type_price_id column
        await queryRunner.addColumn('car_type_price', new TableColumn({
            name: 'car_type_price_id',
            type: 'uuid',
            isNullable: true, // Set to false if it should be a required field
        }));

        // Add the foreign key
        await queryRunner.createForeignKey('car_type_price', new TableForeignKey({
            columnNames: ['car_type_price_id'],
            referencedTableName: 'car_type_price',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE' // or 'SET NULL' if you want the reference to be nullified upon deletion
        }));
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop the foreign key
        await queryRunner.dropForeignKey('related_table_name', 'car_type_price_id');
        // Drop the column
        await queryRunner.dropColumn('related_table_name', 'car_type_price_id');
    }

}
