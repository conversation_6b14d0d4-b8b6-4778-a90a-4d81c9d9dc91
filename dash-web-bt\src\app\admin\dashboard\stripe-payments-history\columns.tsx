'use client';

import Link from 'next/link';
import {Text, Badge, Tooltip, Checkbox, ActionIcon} from 'rizzui';
import {HeaderCell} from '@/components/lib/ui/table';
import EyeIcon from '@/components/lib/icons/eye';
import PencilIcon from '@/components/lib/icons/pencil';
import AvatarCard from '@/components/lib/ui/avatar-card';
import DateCell from '@/components/lib/ui/date-cell';
import DeletePopover from '@/app/shared/lib/delete-popover';
import {routes} from "@/config/routes";
import moment from "moment";

type Columns = {
    data: any[];
    sortConfig?: any;
    handleSelectAll: any;
    checkedItems: string[];
    onDeleteItem: (id: string) => void;
    onHeaderCellClick: (value: string) => void;
    onChecked?: (id: string) => void;
};

function getStatusBadge(status: string) {
    switch (status) {
        case "succeeded":
            return (
                <div className="flex items-center">
                    <Badge color="success" renderAsDot/>
                    <Text className="ms-2 font-medium text-green-dark">Succeeded - Paid</Text>
                </div>
            );
        case "requires_payment_method":
            return (
                <div className="flex items-center">
                    <Badge color="warning" renderAsDot/>
                    <Text className="ms-2 font-medium text-yellow-dark">Requires Payment Method</Text>
                </div>
            );
        case "requires_confirmation":
            return (
                <div className="flex items-center">
                    <Badge color="info" renderAsDot/>
                    <Text className="ms-2 font-medium text-blue-dark">Requires Confirmation</Text>
                </div>
            );
        case "requires_action":
            return (
                <div className="flex items-center">
                    <Badge color="info" renderAsDot/>
                    <Text className="ms-2 font-medium text-blue-dark">Requires Action</Text>
                </div>
            );
        case "processing":
            return (
                <div className="flex items-center">
                    <Badge color="info" renderAsDot/>
                    <Text className="ms-2 font-medium text-blue-dark">Processing</Text>
                </div>
            );
        case "requires_capture":
            return (
                <div className="flex items-center">
                    <Badge color="info" renderAsDot/>
                    <Text className="ms-2 font-medium text-blue-dark">Requires Capture</Text>
                </div>
            );
        case "failed":
            return (
                <div className="flex items-center">
                    <Badge color="danger" renderAsDot/>
                    <Text className="ms-2 font-medium text-red-dark">Failed - Unpaid</Text>
                </div>
            );
        case "canceled":
            return (
                <div className="flex items-center">
                    <Badge color="danger" renderAsDot/>
                    <Text className="ms-2 font-medium text-red-dark">Canceled</Text>
                </div>
            );
        default:
            return (
                <div className="flex items-center">
                    <Badge renderAsDot className="bg-gray-400"/>
                    <Text className="ms-2 font-medium text-gray-600">{status}</Text>
                </div>
            );
    }
}

export const getColumns = ({
                               data,
                               sortConfig,
                               checkedItems,
                               onDeleteItem,
                               onHeaderCellClick,
                               handleSelectAll,
                               onChecked,
                           }: Columns) => [
    // {
    //     title: (
    //         <div className="ps-2">
    //             <Checkbox
    //                 title={'Select All'}
    //                 onChange={handleSelectAll}
    //                 checked={checkedItems.length === data.length}
    //                 className="cursor-pointer"
    //             />
    //         </div>
    //     ),
    //     dataIndex: 'checked',
    //     key: 'checked',
    //     width: 30,
    //     render: (_: any, row: any) => (
    //         <div className="inline-flex ps-2">
    //             <Checkbox
    //                 className="cursor-pointer"
    //                 checked={checkedItems.includes(row.id)}
    //                 {...(onChecked && { onChange: () => onChecked(row.id) })}
    //             />
    //         </div>
    //     ),
    // },
    {
        title: (
            <HeaderCell
                title="Amount"
                sortable
                ascending={
                    sortConfig?.direction === 'asc' && sortConfig?.key === 'amount_captured'
                }
            />
        ),
        onHeaderCell: () => onHeaderCellClick('amount_captured'),
        dataIndex: 'amount_captured',
        key: 'amount_captured',
        width: 200,
        render: (value: string) => {
            return <Text className="font-medium text-gray-700 dark:text-gray-600">
                {+value/100}
            </Text>
        },
    },
    {
        title: (
            <HeaderCell
                title="Order"
                sortable
                ascending={
                    sortConfig?.direction === 'asc' && sortConfig?.key === 'order'
                }
            />
        ),
        onHeaderCell: () => onHeaderCellClick('order'),
        dataIndex: 'metadata',
        key: 'order',
        width: 200,
        render: (row: any) => {
            return <a href={routes.bookingEdit(row.booking_id || '')} className="font-medium text-gray-700 dark:text-gray-600">
                View
            </a>
        },
    },
    {
        title: <HeaderCell title="Currency"/>,
        dataIndex: 'currency',
        key: 'currency',
        width: 120,
        render: (value: any) => `${value}`,
    },
    {
        title: <HeaderCell title="Status"/>,
        dataIndex: 'status',
        key: 'status',
        width: 120,
        render: (_value: boolean, row: any) => getStatusBadge(row.status),
    },
    {
        title: (
            <HeaderCell
                title="Created"
                sortable
                ascending={
                    sortConfig?.direction === 'asc' && sortConfig?.key === 'created'
                }
            />
        ),
        onHeaderCell: () => onHeaderCellClick('created'),
        dataIndex: 'created',
        key: 'created',
        width: 200,
        render: (value: string) => {
            return <Text className="font-medium text-gray-700 dark:text-gray-600">
                {moment(+value * 1000).format('DD/MM/YYYY')}
            </Text>
        },
    },
    // {
    //     title: (
    //         <HeaderCell
    //             title="Payment Method"
    //             sortable
    //             ascending={
    //                 sortConfig?.direction === 'asc' && sortConfig?.key === 'payment_method_details'
    //             }
    //         />
    //     ),
    //     onHeaderCell: () => onHeaderCellClick('payment_method_details'),
    //     dataIndex: 'payment_method_details',
    //     key: 'payment_method_details',
    //     width: 200,
    //     render: (value: any, row: any) => <>
    //         <Text className="font-medium text-gray-700 dark:text-gray-600">
    //             { row.payment_method_details.card.type }
    //         </Text>,
    //     </>
    // },
];
