import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { apiConfig } from './config';
import { ApiResponse, AuthToken } from './types';
import { handleApiError } from '../utils/error-handler';

/**
 * Core API client that handles API requests
 */
export class ApiClient {
  private axiosInstance: AxiosInstance;
  private authToken?: string;

  constructor() {
    this.axiosInstance = axios.create({
      baseURL: apiConfig.baseUrl,
      timeout: apiConfig.timeout,
      headers: apiConfig.headers,
    });

    // Add response interceptor for error handling
    this.axiosInstance.interceptors.response.use(
      (response) => response,
      (error) => {
        return Promise.reject(handleApiError(error));
      }
    );
  }

  /**
   * Set authentication token for API requests
   * @param token The auth token or token object
   */
  public setAuthToken(token: string | AuthToken): void {
    this.authToken = typeof token === 'string' ? token : token.token;
    this.axiosInstance.defaults.headers.common['Authorization'] = `Bearer ${this.authToken}`;
  }

  /**
   * Clear authentication token
   */
  public clearAuthToken(): void {
    this.authToken = undefined;
    delete this.axiosInstance.defaults.headers.common['Authorization'];
  }

  /**
   * Make a GET request
   * @param url The endpoint URL
   * @param params Query parameters
   * @param config Optional Axios request config
   */
  public async get<T>(
    url: string,
    params?: Record<string, any>,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse = await this.axiosInstance.get(url, {
        ...config,
        params,
      });
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: handleApiError(error) };
    }
  }

  /**
   * Make a POST request
   * @param url The endpoint URL
   * @param data Request body data
   * @param config Optional Axios request config
   */
  public async post<T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse = await this.axiosInstance.post(url, data, config);
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: handleApiError(error) };
    }
  }

  /**
   * Make a PUT request
   * @param url The endpoint URL
   * @param data Request body data
   * @param config Optional Axios request config
   */
  public async put<T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse = await this.axiosInstance.put(url, data, config);
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: handleApiError(error) };
    }
  }

  /**
   * Make a DELETE request
   * @param url The endpoint URL
   * @param config Optional Axios request config
   */
  public async delete<T>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse = await this.axiosInstance.delete(url, config);
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: handleApiError(error) };
    }
  }

  /**
   * Make a PATCH request
   * @param url The endpoint URL
   * @param data Request body data
   * @param config Optional Axios request config
   */
  public async patch<T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse = await this.axiosInstance.patch(url, data, config);
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: handleApiError(error) };
    }
  }
}

// Create and export a singleton instance of the API client
export const apiClient = new ApiClient(); 