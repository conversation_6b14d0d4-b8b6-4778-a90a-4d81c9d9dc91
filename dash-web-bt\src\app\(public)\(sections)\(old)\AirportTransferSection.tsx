import React from 'react';
import Container from '@/app/(public)/(sections)/(old)/container';
import { FaPlaneArrival, FaRegHandshake, FaLuggageCart } from 'react-icons/fa'; // Icons for airport, reliable service, and luggage

const AirportTransferSection: React.FC = () => {
    return (
        <section className="py-16 text-chillGold">
            <Container>
                <div className="text-center">
                    <h2 className="text-4xl font-semibold mb-6">
                        Seamless Airport Transfers
                    </h2>
                    <p className="text-lg mb-8">
                        Your comfort and peace of mind are our top priorities. Enjoy hassle-free transfers from and to the airport, with services tailored to your needs.
                    </p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 items-center text-center">
                    <div>
                        <FaPlaneArrival className="mx-auto text-6xl mb-4" style={{ color: '#FFD700' }} />
                        <h3 className="text-xl font-semibold mb-2">Flight Monitoring</h3>
                        <p>We monitor your flight to ensure timely pickups and drop-offs.</p>
                    </div>
                    <div>
                        <FaRegHandshake className="mx-auto text-6xl mb-4" style={{ color: '#FF5733' }} />
                        <h3 className="text-xl font-semibold mb-2">Meet & Greet</h3>
                        <p>Our drivers meet you personally, assisting with your luggage for a smooth experience.</p>
                    </div>
                    <div>
                        <FaLuggageCart className="mx-auto text-6xl mb-4" style={{ color: '#FF6347' }} />
                        <h3 className="text-xl font-semibold mb-2">Luggage Assistance</h3>
                        <p>No extra charges for luggage, ensuring a hassle-free transfer to your destination.</p>
                    </div>
                </div>
            </Container>
        </section>
    );
};

export default AirportTransferSection;
