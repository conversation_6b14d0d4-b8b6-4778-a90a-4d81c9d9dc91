{"compilerOptions": {"target": "es2018", "module": "esnext", "lib": ["dom", "esnext"], "importHelpers": true, "declaration": true, "sourceMap": true, "rootDir": "./src", "outDir": "./dist", "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictPropertyInitialization": true, "noImplicitThis": true, "alwaysStrict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "moduleResolution": "node", "baseUrl": "./", "paths": {"*": ["src/*", "node_modules/*"]}, "jsx": "react", "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "experimentalDecorators": true, "emitDecoratorMetadata": true}, "include": ["src/**/*", "src/**/__tests__/**/*"], "exclude": ["node_modules", "dist", "src/react.bak/**/*", "src/tanstack.bak/**/*"]}