import { 
  useMutation, 
  UseMutationResult, 
  UseMutationOptions,
  useQueryClient
} from 'react-query';
import { authService } from '../services/auth-service';
import { LoginCredentials, LoginResponse, LogoutResponse } from '../models/auth';

/**
 * React Query hook for user login
 * @param options Optional mutation options
 * @returns Mutation result for login
 */
export const useLogin = (
  options?: UseMutationOptions<LoginResponse, Error, LoginCredentials>
): UseMutationResult<LoginResponse, Error, LoginCredentials> => {
  const queryClient = useQueryClient();
  
  return useMutation<LoginResponse, Error, LoginCredentials>(
    (credentials) => authService.login(credentials),
    {
      ...options,
      onSuccess: (data, variables, context) => {
        // Invalidate queries that depend on auth state
        if (data.success) {
          queryClient.invalidateQueries('currentUser');
        }
        
        // Call the provided onSuccess if it exists
        if (options?.onSuccess) {
          options.onSuccess(data, variables, context);
        }
      },
    }
  );
};

/**
 * React Query hook for admin login
 * @param options Optional mutation options
 * @returns Mutation result for admin login
 */
export const useAdminLogin = (
  options?: UseMutationOptions<LoginResponse, Error, LoginCredentials>
): UseMutationResult<LoginResponse, Error, LoginCredentials> => {
  const queryClient = useQueryClient();
  
  return useMutation<LoginResponse, Error, LoginCredentials>(
    (credentials) => authService.loginAdmin(credentials),
    {
      ...options,
      onSuccess: (data, variables, context) => {
        // Invalidate queries that depend on auth state
        if (data.success) {
          queryClient.invalidateQueries('currentUser');
        }
        
        // Call the provided onSuccess if it exists
        if (options?.onSuccess) {
          options.onSuccess(data, variables, context);
        }
      },
    }
  );
};

/**
 * React Query hook for driver login
 * @param options Optional mutation options
 * @returns Mutation result for driver login
 */
export const useDriverLogin = (
  options?: UseMutationOptions<LoginResponse, Error, LoginCredentials>
): UseMutationResult<LoginResponse, Error, LoginCredentials> => {
  const queryClient = useQueryClient();
  
  return useMutation<LoginResponse, Error, LoginCredentials>(
    (credentials) => authService.loginDriver(credentials),
    {
      ...options,
      onSuccess: (data, variables, context) => {
        // Invalidate queries that depend on auth state
        if (data.success) {
          queryClient.invalidateQueries('currentUser');
        }
        
        // Call the provided onSuccess if it exists
        if (options?.onSuccess) {
          options.onSuccess(data, variables, context);
        }
      },
    }
  );
};

/**
 * React Query hook for user logout
 * @param options Optional mutation options
 * @returns Mutation result for logout
 */
export const useLogout = (
  options?: UseMutationOptions<LogoutResponse, Error, void>
): UseMutationResult<LogoutResponse, Error, void> => {
  const queryClient = useQueryClient();
  
  return useMutation<LogoutResponse, Error, void>(
    () => authService.logout(),
    {
      ...options,
      onSuccess: (data, variables, context) => {
        // Invalidate queries that depend on auth state
        queryClient.invalidateQueries('currentUser');
        
        // Call the provided onSuccess if it exists
        if (options?.onSuccess) {
          options.onSuccess(data, variables, context);
        }
      },
    }
  );
}; 