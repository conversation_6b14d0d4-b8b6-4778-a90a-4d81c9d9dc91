{"name": "api-client-sdk", "version": "0.1.0", "description": "Client SDK for the API", "type": "module", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsc src/index.ts --declaration --outDir dist", "build:rollup": "rollup -c", "build:types": "tsc src/index.ts --declaration --outDir dist --emitDeclarationOnly", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "tsc src/index.ts --declaration --outDir dist --watch", "test": "jest", "lint": "eslint src --ext .ts,.tsx", "prepare": "npm run clean && npm run build"}, "dependencies": {"axios": "^1.6.2", "mobx": "^6.12.0", "mobx-state-tree": "^5.3.0", "react-query": "^3.39.3", "zod": "^3.22.4"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-typescript": "^11.1.5", "@types/jest": "^29.5.11", "@types/node": "^20.10.4", "@types/react": "^18.2.45", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "prettier": "^3.1.1", "rimraf": "^5.0.5", "rollup": "^4.6.1", "rollup-plugin-dts": "^6.1.0", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.36.0", "ts-jest": "^29.1.1", "typescript": "^5.3.3"}, "keywords": ["api", "client", "sdk"], "author": "", "license": "MIT"}