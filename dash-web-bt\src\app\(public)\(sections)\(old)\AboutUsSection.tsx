import React from 'react';
import Container from '@/app/(public)/(sections)/(old)/container';
import { FaRegSmileBeam, FaSuitcaseRolling, FaClock } from 'react-icons/fa'; // Import desired icons

const AboutUsSection: React.FC = () => {
    return (
        <section className="py-16 bg-pureWhite text-charcoalBlack">
            <Container>
                <div className="flex flex-col md:flex-row items-center md:space-x-12">
                    <div className="md:w-1/2 lg:w-3/5 text-center md:text-left">
                        <h2 className="text-3xl md:text-4xl font-semibold mb-6">
                            Excellence in Motion
                        </h2>
                        <p className="text-lg mb-4">
                            Experience unmatched comfort and style with Click4Transfer, where every journey is tailored to your needs. Our commitment to quality ensures that you receive the best in luxury transportation.
                        </p>
                        <p className="text-lg mb-6">
                            From the heart of the Costa del Sol to your chosen destination, our reliable drivers and premium vehicles provide a seamless travel experience.
                        </p>
                        <div className="space-y-4">
                            <div className="flex items-center">
                                <FaRegSmileBeam className="text-2xl mr-2" style={{ color: '#FF5733' }} />
                                <span>Arrive in style and pay upon arrival</span>
                            </div>
                            <div className="flex items-center">
                                <FaSuitcaseRolling className="text-2xl mr-2" style={{ color: '#FFC300' }} />
                                <span>No extra charges for luggage</span>
                            </div>
                            <div className="flex items-center">
                                <FaClock className="text-2xl mr-2" style={{ color: '#900C3F' }} />
                                <span>24/7 service for your convenience</span>
                            </div>
                        </div>
                    </div>
                    <div className="md:w-1/2 lg:w-2/5 mt-8 md:mt-0 flex justify-center">
                        <img src="/banners/b_01.webp" alt="Luxury Vehicle" className="w-full h-auto rounded-xl shadow-xl" />
                    </div>
                </div>
            </Container>
        </section>
    );
};

export default AboutUsSection;
