import {MigrationInterface, QueryRunner, TableColumn} from "typeorm";

export class addEmailToCustomer1713916320151 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        const customerTable = await queryRunner.getTable('customer');
        if (customerTable) {
            if (!customerTable.columns.find(column => column.name === 'email')) {
                await queryRunner.addColumn('customer', new TableColumn({
                    name: 'email',
                    type: 'varchar(50)',
                    isNullable: true,
                }));
            }
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropColumn('customer', 'email');
    }

}
