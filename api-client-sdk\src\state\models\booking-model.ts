import { types, Instance, SnapshotIn } from 'mobx-state-tree';
import { CarTypeModel } from './car-type-model';
import { CustomerModel } from './customer-model';
import { CarModel } from './car-model';
import { DriverModel } from './driver-model';
import { PaymentModel } from './payment-model';

/**
 * Booking status enum
 */
export const BookingStatusEnum = types.enumeration('BookingStatusEnum', [
  'pending',
  'accepted',
  'rejected',
  'cancelled',
  'completed'
]);

/**
 * Booking model in MST
 */
export const BookingModel = types.model('BookingModel', {
  id: types.identifier,
  trip_type: types.optional(types.string, 'transfer'),
  pickup_address: types.string,
  booking_code: types.maybeNull(types.string),
  pickup_lat: types.string,
  pickup_long: types.string,
  destination_address: types.maybeNull(types.string),
  destination_lat: types.maybeNull(types.string),
  destination_long: types.maybeNull(types.string),
  one_way_trip: types.optional(types.boolean, true),
  return_trip_time: types.maybeNull(types.string),
  adults: types.number,
  children: types.maybeNull(types.number),
  children_chairs_under_five: types.maybeNull(types.number),
  children_chairs_above_five: types.maybeNull(types.number),
  pets: types.maybeNull(types.number),
  flight_number: types.string,
  status: types.optional(BookingStatusEnum, 'pending'),
  scheduled_at: types.string,
  other_details: types.maybeNull(types.string),
  recommended_amount_to_pay: types.number,
  estimated_service_hours: types.maybeNull(types.number),
  final_amount_to_pay: types.number,
  trip_distance: types.maybeNull(types.number),
  return_trip_distance: types.maybeNull(types.number),
  trip_duration: types.maybeNull(types.number),
  return_trip_duration: types.maybeNull(types.number),
  booking_finished_at: types.maybeNull(types.string),
  driver_observation: types.maybeNull(types.string),
  created_at: types.string, 
  updated_at: types.string, 
  updated_by: types.maybeNull(types.number),
  car_type: types.maybeNull(types.late(() => CarTypeModel)),
  customer: types.maybeNull(types.late(() => CustomerModel)),
  car: types.maybeNull(types.late(() => CarModel)),
  driver: types.maybeNull(types.late(() => DriverModel)),
  payment: types.maybeNull(types.late(() => PaymentModel)),
})
.views(self => ({
  /**
   * Get created date as Date object
   */
  get createdDate(): Date {
    return new Date(self.created_at);
  },
  
  /**
   * Get updated date as Date object
   */
  get updatedDate(): Date {
    return new Date(self.updated_at);
  },
  
  /**
   * Get scheduled date as Date object
   */
  get scheduledDate(): Date {
    return new Date(self.scheduled_at);
  },
  
  /**
   * Get return trip time as Date object
   */
  get returnTripDate(): Date | null {
    return self.return_trip_time ? new Date(self.return_trip_time) : null;
  },
  
  /**
   * Get booking finished date as Date object
   */
  get finishedDate(): Date | null {
    return self.booking_finished_at ? new Date(self.booking_finished_at) : null;
  },
  
  /**
   * Calculate total passengers
   */
  get totalPassengers(): number {
    const adults = self.adults || 0;
    const children = self.children || 0;
    return adults + children;
  },
  
  /**
   * Check if booking has a driver assigned
   */
  get hasDriver(): boolean {
    return !!self.driver;
  },
  
  /**
   * Check if booking has been paid
   */
  get isPaid(): boolean {
    return !!self.payment && self.payment.status === 'completed';
  },
  
  /**
   * Check if booking can be cancelled
   */
  get canBeCancelled(): boolean {
    return ['pending', 'accepted'].includes(self.status);
  }
}));

// Type definitions for TypeScript
export interface Booking extends Instance<typeof BookingModel> {}
export interface BookingSnapshotIn extends SnapshotIn<typeof BookingModel> {} 