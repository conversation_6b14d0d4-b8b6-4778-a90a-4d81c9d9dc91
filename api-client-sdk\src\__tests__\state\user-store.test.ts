import { describe, it, expect, beforeEach } from '@jest/globals';
// Remove jest from the import
import { getSnapshot, onPatch } from 'mobx-state-tree';
import { UserStore, User } from '../../state/user-store';
import { apiClient } from '../../core/api-client';

// The mock should work with the global jest object
jest.mock('../../core/api-client', () => ({
  apiClient: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn()
  }
}));

const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;

describe('UserStore', () => {
  let store: typeof UserStore.Type;
  
  const mockUsers = [
    {
      id: 1,
      username: 'user1',
      email: '<EMAIL>',
      first_name: 'User',
      last_name: 'One',
      created_at: new Date().toISOString(),
    },
    {
      id: 2,
      username: 'user2',
      email: '<EMAIL>',
      first_name: 'User',
      last_name: 'Two',
      created_at: new Date().toISOString(),
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    store = UserStore.create({
      users: [],
      isLoading: false,
      error: null,
      currentPage: 1,
      itemsPerPage: 10,
      totalItems: 0,
      totalPages: 0
    });
  });

  describe('actions', () => {
    it('should set loading state', () => {
      store.setLoading(true);
      expect(store.isLoading).toBe(true);
      
      store.setLoading(false);
      expect(store.isLoading).toBe(false);
    });

    it('should set error state', () => {
      const error = 'Something went wrong';
      store.setError(error);
      expect(store.error).toBe(error);
      
      store.setError(null);
      expect(store.error).toBeNull();
    });

    it('should set users', () => {
      store.setUsers(mockUsers);
      
      expect(store.users.length).toBe(2);
      expect(getSnapshot(store.users[0])).toMatchObject({
        id: 1,
        username: 'user1',
        email: '<EMAIL>'
      });
    });

    it('should select a user by id', () => {
      store.setUsers(mockUsers);
      store.selectUserById(1);
      
      expect(store.selectedUser).toBeDefined();
      expect(store.selectedUser?.id).toBe(1);
      expect(store.selectedUser?.username).toBe('user1');
    });

    it('should update a user', () => {
      // Setup
      store.setUsers(mockUsers);
      
      // Track changes to state
      const patches: any[] = [];
      onPatch(store, patch => {
        patches.push(patch);
      });

      // Update a user
      const updatedData = { 
        id: 1, 
        username: 'user1_updated', 
        first_name: 'Updated',
        last_name: 'User'
      };
      
      store.updateUser(updatedData);
      
      // Check the store state was updated
      expect(store.users[0].username).toBe('user1_updated');
      expect(store.users[0].first_name).toBe('Updated');
      
      // Check that patches were applied
      expect(patches.length).toBeGreaterThan(0);
      expect(patches.some(p => p.path.includes('/username') && p.value === 'user1_updated')).toBe(true);
    });
  });

  describe('async actions', () => {
    it('should fetch users', async () => {
      mockApiClient.get.mockResolvedValue({
        data: {
          success: true,
          data: {
            users: mockUsers,
            pagination: {
              page: 1,
              limit: 10,
              total: 2,
              pages: 1
            }
          }
        }
      });

      await store.fetchUsers();
      
      expect(mockApiClient.get).toHaveBeenCalledWith('/users', { params: { page: 1, limit: 10 } });
      expect(store.users.length).toBe(2);
      expect(store.currentPage).toBe(1);
      expect(store.totalItems).toBe(2);
      expect(store.isLoading).toBe(false);
    });

    it('should handle fetch users error', async () => {
      mockApiClient.get.mockRejectedValue(new Error('Network error'));
      
      await store.fetchUsers();
      
      expect(mockApiClient.get).toHaveBeenCalled();
      expect(store.isLoading).toBe(false);
      expect(store.error).toBeDefined();
      expect(store.users.length).toBe(0);
    });

    it('should fetch a user by id', async () => {
      mockApiClient.get.mockResolvedValue({
        data: {
          success: true,
          data: mockUsers[0]
        }
      });

      await store.fetchUserById(1);
      
      expect(mockApiClient.get).toHaveBeenCalledWith('/users/1');
      expect(store.selectedUser).toBeDefined();
      expect(store.selectedUser?.id).toBe(1);
      expect(store.isLoading).toBe(false);
    });

    it('should create a user', async () => {
      const newUser = {
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password',
        confirmed_password: 'password',
        first_name: 'New',
        last_name: 'User'
      };
      
      const createdUser = {
        ...newUser,
        id: 3,
        created_at: new Date().toISOString()
      };
      
      mockApiClient.post.mockResolvedValue({
        data: {
          success: true,
          data: createdUser
        }
      });

      const result = await store.createUser(newUser);
      
      expect(mockApiClient.post).toHaveBeenCalledWith('/users', newUser);
      expect(result.success).toBe(true);
      expect(result.data).toMatchObject(createdUser);
    });

    it('should update a user via API', async () => {
      const userId = 1;
      const updates = {
        first_name: 'Updated',
        last_name: 'Name'
      };
      
      const updatedUser = {
        ...mockUsers[0],
        ...updates
      };
      
      mockApiClient.put.mockResolvedValue({
        data: {
          success: true,
          data: updatedUser
        }
      });

      store.setUsers(mockUsers);
      const result = await store.updateUserViaApi(userId, updates);
      
      expect(mockApiClient.put).toHaveBeenCalledWith(`/users/${userId}`, updates);
      expect(result.success).toBe(true);
      expect(store.users[0].first_name).toBe('Updated');
    });

    it('should delete a user', async () => {
      const userId = 1;
      
      mockApiClient.delete.mockResolvedValue({
        data: {
          success: true
        }
      });

      store.setUsers(mockUsers);
      const result = await store.deleteUser(userId);
      
      expect(mockApiClient.delete).toHaveBeenCalledWith(`/users/${userId}`);
      expect(result.success).toBe(true);
      expect(store.users.length).toBe(1);
      expect(store.users[0].id).toBe(2);
    });
  });

  describe('views', () => {
    it('should filter users by role', () => {
      const usersWithRoles = [
        {
          ...mockUsers[0],
          role: { id: 1, name: 'admin', description: 'Administrator' }
        },
        {
          ...mockUsers[1],
          role: { id: 2, name: 'user', description: 'Regular user' }
        }
      ];
      
      store.setUsers(usersWithRoles);
      
      const admins = store.getUsersByRole('admin');
      expect(admins.length).toBe(1);
      expect(admins[0].id).toBe(1);
      
      const regularUsers = store.getUsersByRole('user');
      expect(regularUsers.length).toBe(1);
      expect(regularUsers[0].id).toBe(2);
    });

    it('should search users by name or email', () => {
      store.setUsers(mockUsers);
      
      const searchResult1 = store.searchUsers('user1');
      expect(searchResult1.length).toBe(1);
      expect(searchResult1[0].id).toBe(1);
      
      const searchResult2 = store.searchUsers('user2@example');
      expect(searchResult2.length).toBe(1);
      expect(searchResult2[0].id).toBe(2);
      
      const searchResultAll = store.searchUsers('user');
      expect(searchResultAll.length).toBe(2);
    });
  });
}); 
