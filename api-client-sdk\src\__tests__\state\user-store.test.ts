import { describe, it, expect, beforeEach } from '@jest/globals';
import { UserStore, IUserStore } from '../../state/user-store';
import { apiClient } from '../../core/api-client';

// Mock the API client
jest.mock('../../core/api-client', () => ({
  apiClient: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn()
  }
}));

const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;

describe('UserStore', () => {
  let store: IUserStore;

  const mockUsers = [
    {
      id: 1,
      username: 'user1',
      email: '<EMAIL>',
      first_name: 'User',
      last_name: 'One',
      created_at: new Date().toISOString(),
    },
    {
      id: 2,
      username: 'user2',
      email: '<EMAIL>',
      first_name: 'User',
      last_name: 'Two',
      created_at: new Date().toISOString(),
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    store = UserStore.create({
      users: [],
      isLoading: false,
      currentPage: 1,
      itemsPerPage: 10,
      totalItems: 0,
      totalPages: 0
    });
  });

  describe('initial state', () => {
    it('should have correct initial state', () => {
      expect(store.users.length).toBe(0);
      expect(store.isLoading).toBe(false);
      expect(store.error).toBeUndefined();
      expect(store.currentPage).toBe(1);
      expect(store.itemsPerPage).toBe(10);
      expect(store.totalItems).toBe(0);
      expect(store.totalPages).toBe(0);
      expect(store.selectedUser).toBeUndefined();
    });
  });

  describe('views', () => {
    it('should get user by id when no users exist', () => {
      expect(store.getUserById(1)).toBeUndefined();
    });
  });

  describe('async actions', () => {
    it('should fetch users', async () => {
      mockApiClient.get.mockResolvedValue({
        success: true,
        data: {
          success: true,
          data: {
            users: mockUsers,
            pagination: {
              page: 1,
              limit: 10,
              total: 2,
              pages: 1
            }
          }
        }
      } as any);

      await store.fetchUsers();
      
      expect(mockApiClient.get).toHaveBeenCalledWith('/users', { params: { page: 1, limit: 10 } });
      expect(store.users.length).toBe(2);
      expect(store.currentPage).toBe(1);
      expect(store.totalItems).toBe(2);
      expect(store.isLoading).toBe(false);
    });

    it('should handle fetch users error', async () => {
      mockApiClient.get.mockRejectedValue(new Error('Network error'));
      
      await store.fetchUsers();
      
      expect(mockApiClient.get).toHaveBeenCalled();
      expect(store.isLoading).toBe(false);
      expect(store.error).toBeDefined();
      expect(store.users.length).toBe(0);
    });

    it('should fetch a user by id', async () => {
      mockApiClient.get.mockResolvedValue({
        success: true,
        data: {
          success: true,
          data: mockUsers[0]
        }
      } as any);

      await store.fetchUserById(1);

      expect(mockApiClient.get).toHaveBeenCalledWith('/users/1');
      expect(store.isLoading).toBe(false);
    });
  });
}); 
