import { z } from 'zod';
import { CustomerSchema } from './customer';
import { PaymentTypeSchema } from './payment-type';

/**
 * Payment status enum
 */
export const PaymentStatusEnum = z.enum([
  'pending',
  'processing',
  'completed',
  'failed',
  'refunded'
]);

/**
 * Payment schema with validation
 */
export const PaymentSchema = z.object({
  id: z.string().uuid(),
  amount: z.number(),
  currency: z.string().default('USD'),
  status: PaymentStatusEnum.default('pending'),
  payment_date: z.string().or(z.date()).transform(val => val ? new Date(val) : null).nullable().optional(),
  transaction_id: z.string().nullable().optional(),
  payment_method: z.string().nullable().optional(),
  notes: z.string().nullable().optional(),
  created_at: z.string().or(z.date()).transform(val => new Date(val)),
  updated_at: z.string().or(z.date()).transform(val => new Date(val)),
  updated_by: z.number().nullable().optional(),
  customer: z.lazy(() => CustomerSchema.optional()),
  payment_type: z.lazy(() => PaymentTypeSchema.optional()),
});

/**
 * Payment creation schema
 */
export const PaymentCreateSchema = PaymentSchema.omit({ 
  id: true, 
  created_at: true, 
  updated_at: true,
  status: true
});

/**
 * Payment update schema
 */
export const PaymentUpdateSchema = PaymentSchema
  .partial()
  .omit({ 
    id: true, 
    created_at: true, 
    updated_at: true 
  });

// Type definitions derived from Zod schemas
export type PaymentStatus = z.infer<typeof PaymentStatusEnum>;
export type Payment = z.infer<typeof PaymentSchema>;
export type PaymentCreate = z.infer<typeof PaymentCreateSchema>;
export type PaymentUpdate = z.infer<typeof PaymentUpdateSchema>; 