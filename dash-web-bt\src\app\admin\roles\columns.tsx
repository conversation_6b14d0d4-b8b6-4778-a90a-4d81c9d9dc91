'use client';

import Link from 'next/link';
import {Text, Tooltip, ActionIcon} from 'rizzui';
import {HeaderCell} from '@/components/lib/ui/table';
import PencilIcon from '@/components/lib/icons/pencil'; 
import DateCell from '@/components/lib/ui/date-cell';
import DeletePopover from '@/app/shared/lib/delete-popover';
import {routes} from "@/config/routes";

type Columns = {
    sortConfig?: any;
    onDeleteItem: (id: string) => void;
    onHeaderCellClick: (value: string) => void;
};
export const getColumns = ({
    sortConfig,
    onDeleteItem,
    onHeaderCellClick,
}: Columns) => [
    {
        title: (
            <HeaderCell
                title="Name"
                sortable
                ascending={
                    sortConfig?.direction === 'asc' && sortConfig?.key === 'name'
                }
            />
        ),
        onHeaderCell: () => onHeaderCellClick('name'),
        dataIndex: 'name',
        key: 'name',
        width: 200,
        render: (value: any, _row: any) => {
            return <Text className="font-medium text-gray-700 dark:text-gray-600">
                {value}
            </Text>
        },
    },
    {
        title: (
            <HeaderCell
                title="Created At"
                sortable
                ascending={
                    sortConfig?.direction === 'asc' && sortConfig?.key === 'created_at'
                }
            />
        ),
        onHeaderCell: () => onHeaderCellClick('created_at'),
        dataIndex: 'created_at',
        key: 'created_at',
        width: 200,
        render: (value: Date) => <DateCell date={value}/>,
    },
    {
        title: <></>,
        dataIndex: 'action',
        key: 'action',
        width: 140,
        render: (_: string, row: any) => (
            <div className="flex items-center justify-end gap-3 pe-3">
                <Tooltip
                    size="sm"
                    content={'Edit Role'}
                    placement="top"
                >
                    <Link href={routes.roleEdit(row.id)}>
                        <ActionIcon
                            as="span"
                            size="sm"
                            variant="outline"
                            className="hover:!border-gray-900 hover:text-gray-700"
                        >
                            <PencilIcon className="h-4 w-4"/>
                        </ActionIcon>
                    </Link>
                </Tooltip>
                <DeletePopover
                    title={`Delete the role`}
                    description={`Are you sure you want to delete this #${row.id} role?`}
                    onDelete={() => onDeleteItem(row.id)}
                />
            </div>
        ),
    },
];
