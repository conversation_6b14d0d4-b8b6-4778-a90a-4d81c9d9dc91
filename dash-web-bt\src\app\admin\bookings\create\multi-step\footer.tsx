'use client';

import { useEffect } from 'react';
import { useSetAtom } from 'jotai';
import { useResetAtom } from 'jotai/utils';
import { PiArrowUpLight, PiCheck } from 'react-icons/pi';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { Button } from 'rizzui';
import cn from '@/utils/class-names';
import {
  formDataAtom,
  stepperAtomOne,
  useStepperOne,
} from '@/app/shared/app/bookings/create-multi-step';
import INITIAL_FORM_DATA from "@/app/shared/app/bookings/create-multi-step/initialFormData";

interface FooterProps {
  formId?: number;
  className?: string;
  isLoading?: boolean;
}

function buttonLabel(formId?: number) {
  if (formId === 6) {
    return (
      <>
        Submit <PiCheck />
      </>
    );
  }
  if (formId === 7) {
    return 'Next';
  }
  return (
    <>
      Next <PiArrowUpLight className="rotate-90" />
    </>
  );
}

export default function Footer({ isLoading, className }: FooterProps) {
  const { push } = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const setFormData = useSetAtom(formDataAtom);
  const { step, gotoPrevStep } = useStepperOne();
  const resetLocation = useResetAtom(stepperAtomOne);

  useEffect(() => {
    resetLocation();
    setFormData(INITIAL_FORM_DATA);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname, searchParams]);

  function buttonAttr() {
    // if (step === 7) {
    //   return {
    //     onClick: () => push('/'),
    //   };
    // }
    return { form: `rhf-${step?.toString()}` };
  }

  return (
    <footer
      className={cn(
        'fixed bottom-0 left-0 right-0 flex items-center justify-between gap-3 px-4 py-5 bg-gradient-to-t from-gray-900 to-transparent',
        'lg:px-8 4xl:px-10',
        'transition-all duration-300 ease-in-out',
        className
      )}
    >
      {step > 0 && step < 7 && (
        <Button
          rounded="pill"
          variant="outline"
          onClick={gotoPrevStep}
          className="gap-1 border-white text-white backdrop-blur-md hover:border-white hover:bg-white hover:text-black transition-colors duration-300 hover:shadow-md"
        >
          <PiArrowUpLight className="-rotate-90" />
          Back
        </Button>
      )}
      <Button
        isLoading={isLoading}
        disabled={isLoading}
        rounded="pill"
        {...buttonAttr()}
        type={'submit'}
        className="ml-auto gap-1 bg-blue-600 text-white hover:bg-blue-700 transition-colors duration-300 hover:shadow-md"
      >
        {buttonLabel(step)}
      </Button>
    </footer>
  );
}
