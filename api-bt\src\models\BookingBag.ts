import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
// eslint-disable-next-line import/no-cycle
import { Booking } from './Booking';
// eslint-disable-next-line import/no-cycle
import { Bag } from './Bag';

@Entity()
export class BookingBag {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ManyToOne(() => Bag)
  @JoinColumn({ name: "bag_id" })
  bag: Bag;

  @Column()
  quantity: number;

  @ManyToOne(() => Booking, booking => booking.bookingBags)
  @JoinColumn({ name: "booking_id" })
  booking: Booking[];

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
