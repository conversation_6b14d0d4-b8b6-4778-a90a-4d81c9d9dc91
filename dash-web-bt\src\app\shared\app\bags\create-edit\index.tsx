'use client';

import { useState } from 'react';
import toast from 'react-hot-toast';
import { Element } from 'react-scroll';
import { zodResolver } from '@hookform/resolvers/zod';
import { SubmitHandler, useForm, FormProvider } from 'react-hook-form';
import FormFooter from '@/app/shared/app/bags/create-edit/form-footer';
import FormSenderInfo from '@/app/shared/app/bags/create-edit/form-details';
import FormNav, {
  FormParts,
} from '@/app/shared/app/bags/create-edit/form-nav';
import { defaultValues } from '@/app/shared/app/bags/create-edit/form-utils';
import cn from '@/utils/class-names';
import { useLayout } from '@/hooks/use-layout';
import { LAYOUT_OPTIONS } from '@/config/enums';

import {useCreateBag, useUpdateBag} from '@/services/mutations/bag';
import {useRouter} from "next/navigation";
import {BagSchema, BagSchemaType} from "@/utils/validators/app/entities/bag";
import {routes} from "@/config/routes";

const MAP_STEP_TO_COMPONENT = {
  [FormParts.SenderInfo]: FormSenderInfo,
};

interface IndexProps {
  id?: string;
  className?: string;
  bag?: BagSchemaType;
}

export default function CreateEdit({
                                     id,
                                     bag,
                                     className,
                                   }: IndexProps) {
  const router = useRouter();

  const { layout } = useLayout();
  const [isLoading, setLoading] = useState(false);

  const methods = useForm({
    defaultValues: defaultValues(bag),
    resolver: zodResolver(BagSchema),
  });


  const updateMutation = useUpdateBag();
  const createMutation = useCreateBag();

  const onSubmit: SubmitHandler<BagSchemaType> = (data) => {
    // set timeout ony required to display loading state of the create product button
    setLoading(true);

    if(id) {
      updateMutation.mutate({
            id, payload: data
          },
          {
            onSuccess: () => {
              toast.success('Bag Updated Successfully');
              methods.reset();
              setLoading(false);
              router.push(routes.bags);
            },
            onError: (error) => {
              console.log('error', error);
              setLoading(false);
              toast.error('Error updating bag');
            }
          }
      )
    } else {
      createMutation.mutate({
        payload: data
      }, {
        onSuccess: () => {
          toast.success('Bag Created Successfully');
          methods.reset();
          // redirect to the bags list page
          setLoading(false);
          router.push(routes.bags);
        },
        onError: (error) => {
          console.log('error', error);
          setLoading(false);
          toast.error('Error creating bag');
        }
      })
    }
  };

  // console.log('errors', methods.formState.errors);

  return (
      <div className="@container">
        <FormNav
            className={cn(layout === LAYOUT_OPTIONS.BERYLLIUM && '2xl:top-[72px]')}
        />
        <FormProvider {...methods}>
          <form
              className={cn('mt-6', className)}
              onSubmit={methods.handleSubmit(onSubmit)}
          >
            <div className="mb-10 grid gap-7 divide-y divide-dashed divide-gray-200 @2xl:gap-9 @3xl:gap-11">
              {Object.entries(MAP_STEP_TO_COMPONENT).map(([key, Component]) => (
                  <Element
                      key={key}
                      name={FormParts[key as keyof typeof FormParts]}
                  >
                    <Component className="pt-7 @2xl:pt-9 @3xl:pt-11" />
                  </Element>
              ))}
            </div>
            <FormFooter
                isLoading={isLoading}
                submitBtnText={id ? 'Update Bag' : 'Create Bag'}
            />
          </form>
        </FormProvider>
      </div>
  );
}
