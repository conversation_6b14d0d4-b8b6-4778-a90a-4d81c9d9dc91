import { Request, Response } from 'express';
import {
    deletePayment,
} from '../../services/payment'; // Adjust the import path as necessary

export const deletePaymentHandler = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        const result = await deletePayment(id);
        if (!result.success) {
            return res.status(404).json(result);
        }
        return res.json(result);
    } catch (error: any) {
        return res.status(500).json({
            success: false,
            message: 'Failed to delete payment',
            error: error.message,
        });
    }
};
