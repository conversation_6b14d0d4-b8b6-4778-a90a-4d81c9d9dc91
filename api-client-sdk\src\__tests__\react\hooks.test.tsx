import { jest, describe, it, expect, beforeEach } from '@jest/globals';
import { useUsers, useAuth, useApi } from '../../react.bak/hooks';

// Mock the store provider
jest.mock('../../react/provider', () => ({
  useStore: jest.fn(() => ({
    userStore: {
      users: [{ id: 1, username: 'testuser', email: '<EMAIL>' }],
      selectedUser: { id: 1, username: 'testuser', email: '<EMAIL>' },
      isLoading: false,
      error: null,
      currentPage: 1,
      itemsPerPage: 10,
      totalItems: 100,
      totalPages: 10,
      fetchUsers: jest.fn().mockResolvedValue({ success: true }),
      fetchUserById: jest.fn().mockResolvedValue({ success: true }),
      selectUser: jest.fn(),
      getUserById: jest.fn()
    },
    authStore: {
      isAuthenticated: true,
      currentUser: { id: 1, username: 'testuser' },
      isLoading: false,
      error: null,
      isAdmin: true,
      isDriver: false,
      login: jest.fn().mockResolvedValue({ success: true }),
      loginAdmin: jest.fn().mockResolvedValue({ success: true }),
      loginDriver: jest.fn().mockResolvedValue({ success: true }),
      logout: jest.fn().mockResolvedValue({ success: true })
    }
  }))
}));

describe('React Hooks', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('useUsers', () => {
    it('should return user data and actions', () => {
      const result = useUsers();
      
      expect(result.users).toHaveLength(1);
      expect(result.selectedUser).toBeDefined();
      expect(result.isLoading).toBe(false);
      expect(result.error).toBeNull();
      expect(result.pagination).toBeDefined();
      expect(result.fetchUsers).toBeDefined();
      expect(result.fetchUserById).toBeDefined();
      expect(result.selectUser).toBeDefined();
    });

    it('should call fetchUsers correctly', async () => {
      const result = useUsers();
      const params = { page: 1, limit: 10 };
      
      await result.fetchUsers(params);
      
      // Check the mock store's fetchUsers was called with params
      expect(result.fetchUsers).toHaveBeenCalledWith(params);
    });
  });

  describe('useAuth', () => {
    it('should return auth state and actions', () => {
      const result = useAuth();
      
      expect(result.isAuthenticated).toBe(true);
      expect(result.currentUser).toBeDefined();
      expect(result.isLoading).toBe(false);
      expect(result.error).toBeNull();
      expect(result.isAdmin).toBe(true);
      expect(result.isDriver).toBe(false);
      expect(result.login).toBeDefined();
      expect(result.loginAdmin).toBeDefined();
      expect(result.loginDriver).toBeDefined();
      expect(result.logout).toBeDefined();
    });

    it('should call login correctly', async () => {
      const result = useAuth();
      const credentials = { email: '<EMAIL>', password: 'password' };
      
      await result.login(credentials);
      
      expect(result.login).toHaveBeenCalledWith(credentials);
    });
  });

  describe('useApi', () => {
    it('should combine multiple hooks', () => {
      const result = useApi();
      
      expect(result.auth).toBeDefined();
      expect(result.users).toBeDefined();
      
      expect(result.auth.isAuthenticated).toBe(true);
      expect(result.users.users).toHaveLength(1);
    });
  });
}); 