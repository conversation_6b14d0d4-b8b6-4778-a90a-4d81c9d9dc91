import { z } from 'zod';
import { CarTypeSchema } from './car-type';
import { CustomerSchema } from './customer';
import { CarSchema } from './car';
import { DriverSchema } from './driver';
import { PaymentSchema } from './payment';

/**
 * Booking status type
 */
export const BookingStatusEnum = z.enum([
  'pending',
  'accepted',
  'rejected',
  'cancelled',
  'completed'
]);

/**
 * Booking schema with validation
 */
export const BookingSchema = z.object({
  id: z.string().uuid(),
  trip_type: z.string().default('transfer'),
  pickup_address: z.string(),
  booking_code: z.string().nullable().optional(),
  pickup_lat: z.string(),
  pickup_long: z.string(),
  destination_address: z.string().nullable().optional(),
  destination_lat: z.string().nullable().optional(),
  destination_long: z.string().nullable().optional(),
  one_way_trip: z.boolean().default(true),
  return_trip_time: z.string().or(z.date()).transform(val => val ? new Date(val) : null).nullable().optional(),
  adults: z.number().int().min(1),
  children: z.number().int().nullable().optional(),
  children_chairs_under_five: z.number().int().nullable().optional(),
  children_chairs_above_five: z.number().int().nullable().optional(),
  pets: z.number().int().nullable().optional(),
  flight_number: z.string(),
  status: BookingStatusEnum.default('pending'),
  scheduled_at: z.string().or(z.date()).transform(val => new Date(val)),
  other_details: z.string().nullable().optional(),
  recommended_amount_to_pay: z.number(),
  estimated_service_hours: z.number().int().nullable().optional(),
  final_amount_to_pay: z.number(),
  trip_distance: z.number().nullable().optional(),
  return_trip_distance: z.number().nullable().optional(),
  trip_duration: z.number().int().nullable().optional(),
  return_trip_duration: z.number().int().nullable().optional(),
  booking_finished_at: z.string().or(z.date()).transform(val => val ? new Date(val) : null).nullable().optional(),
  driver_observation: z.string().nullable().optional(),
  created_at: z.string().or(z.date()).transform(val => new Date(val)),
  updated_at: z.string().or(z.date()).transform(val => new Date(val)),
  updated_by: z.number().nullable().optional(),
  car_type: z.lazy(() => CarTypeSchema.optional()),
  customer: z.lazy(() => CustomerSchema.optional()),
  car: z.lazy(() => CarSchema.optional()),
  driver: z.lazy(() => DriverSchema.optional()),
  payment: z.lazy(() => PaymentSchema.optional()),
  // Purposely omitted bookingBags for now as it's a circular reference
});

/**
 * Booking creation schema
 */
export const BookingCreateSchema = BookingSchema.omit({ 
  id: true, 
  created_at: true, 
  updated_at: true,
  status: true
});

/**
 * Booking update schema
 */
export const BookingUpdateSchema = BookingSchema
  .partial()
  .omit({ 
    id: true, 
    created_at: true, 
    updated_at: true 
  });

// Type definitions derived from Zod schemas
export type BookingStatus = z.infer<typeof BookingStatusEnum>;
export type Booking = z.infer<typeof BookingSchema>;
export type BookingCreate = z.infer<typeof BookingCreateSchema>;
export type BookingUpdate = z.infer<typeof BookingUpdateSchema>; 