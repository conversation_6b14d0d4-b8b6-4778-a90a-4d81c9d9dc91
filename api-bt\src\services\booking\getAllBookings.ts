import { getRepository } from 'typeorm';
import { Booking } from '../../models/Booking';
import { paginate } from '../../utils/pagination';

export const getAllBookings = async (
    page = 1,
    limit = 10,
) => {
  const bookingRepository = getRepository(Booking);
  const options: any = {
    skip: (page - 1) * limit,
    take: limit,
    relations: ['car_type', 'customer', 'payment', 'payment.type', 'driver', 'driver.car', 'car_type.transfer_prices', 'car'],
    order: {
      created_at: 'DESC',  // Order by created_at in descending order
    },
  };

  const [data, totalRecords] = await bookingRepository.findAndCount(options);

  return paginate<Booking>(data, page, limit, totalRecords);
};
