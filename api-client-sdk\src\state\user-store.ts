import { types, flow, Instance, SnapshotIn } from 'mobx-state-tree';
import { userService } from '../services/user-service';
import { User, UserModel } from './models/user-model';
import { PaginationParams } from '../utils/pagination';

/**
 * User store model for managing users
 */
export const UserStore = types
  .model('UserStore', {
    users: types.array(UserModel),
    selectedUser: types.maybe(types.reference(UserModel)),
    isLoading: types.optional(types.boolean, false),
    error: types.maybe(types.string),
    currentPage: types.optional(types.number, 1),
    itemsPerPage: types.optional(types.number, 10),
    totalItems: types.optional(types.number, 0),
    totalPages: types.optional(types.number, 0),
  })
  .views((self) => ({
    /**
     * Get user by ID
     */
    getUserById(id: number): User | undefined {
      return self.users.find((user) => user.id === id);
    },
  }))
  .actions((self) => {
    /**
     * Set the loading state
     */
    const setLoading = (loading: boolean) => {
      self.isLoading = loading;
    };

    /**
     * Set error message
     */
    const setError = (error: string | null) => {
      self.error = error || undefined;
    };

    /**
     * Set pagination metadata
     */
    const setPaginationMeta = (page: number, limit: number, total: number) => {
      self.currentPage = page;
      self.itemsPerPage = limit;
      self.totalItems = total;
      self.totalPages = Math.ceil(total / limit);
    };

    /**
     * Select a user
     */
    const selectUser = (userId: number) => {
      const user = self.users.find((u) => u.id === userId);
      if (user) {
        self.selectedUser = user;
      }
    };

    /**
     * Fetch users with pagination
     */
    const fetchUsers = flow(function* (params: PaginationParams = {}) {
      setLoading(true);
      setError(null);
      
      try {
        const response = yield userService.getAllUsers({
          page: self.currentPage,
          limit: self.itemsPerPage,
          ...params,
        });
        
        if (response.success && response.data) {
          const { data, meta } = response.data;
          
          // Replace users array with new data
          self.users.replace(data);
          
          // Update pagination metadata
          setPaginationMeta(
            meta.currentPage,
            meta.itemsPerPage,
            meta.totalItems
          );
          
          return { success: true };
        } else {
          const errorMsg = response.error?.msg || 'Failed to fetch users';
          setError(errorMsg);
          return { success: false, error: errorMsg };
        }
      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : 'Failed to fetch users';
        setError(errorMsg);
        return { success: false, error: errorMsg };
      } finally {
        setLoading(false);
      }
    });

    /**
     * Fetch a single user by ID
     */
    const fetchUserById = flow(function* (id: number) {
      setLoading(true);
      setError(null);
      
      try {
        const response = yield userService.getUserById(id);
        
        if (response.success && response.data) {
          // Check if user already exists in the store
          const existingUserIndex = self.users.findIndex((user) => user.id === id);
          
          if (existingUserIndex >= 0) {
            // Update existing user
            self.users[existingUserIndex] = response.data;
          } else {
            // Add new user to the array
            self.users.push(response.data);
          }
          
          // Set as selected user
          selectUser(id);
          
          return { success: true };
        } else {
          const errorMsg = response.error?.msg || `Failed to fetch user with ID: ${id}`;
          setError(errorMsg);
          return { success: false, error: errorMsg };
        }
      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : `Failed to fetch user with ID: ${id}`;
        setError(errorMsg);
        return { success: false, error: errorMsg };
      } finally {
        setLoading(false);
      }
    });

    return {
      setLoading,
      setError,
      setPaginationMeta,
      selectUser,
      fetchUsers,
      fetchUserById,
    };
  });

// Type definitions
export interface IUserStore extends Instance<typeof UserStore> {}
export interface IUserStoreSnapshotIn extends SnapshotIn<typeof UserStore> {}

// Create and export a default instance of the user store
export const createUserStore = () => UserStore.create({
  users: [],
  isLoading: false,
  currentPage: 1,
  itemsPerPage: 10,
  totalItems: 0,
  totalPages: 0,
}); 