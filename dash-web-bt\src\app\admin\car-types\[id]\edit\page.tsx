'use client';

import { routes } from '@/config/routes';
import PageHeader from '@/app/shared/lib/page-header';
import CreateEdit from "@/app/shared/app/car-types/create-edit";
import {useGetCarType} from "@/services/query/car-type/admin/useGetCarType";

const pageHeader = {
  title: 'Edit Car Type',
  breadcrumb: [
    {
      href: routes.dashboard,
      name: 'Dashboard',
    },
    {
      href: routes.carTypes,
      name: 'Car Types',
    },
    {
      name: 'Edit Car Type',
    },
  ],
};

export default function EditShipmentsPage({
  params,
}: {
  params: { id: string };
}) {
  const id = params.id;

  const {
    // isPending,
    // isError,
    // error,
    data: carType,
    // isFetching,
    // isPlaceholderData,
    // refetch,
    // status,
  } = useGetCarType(id);

  return (
    <>
      <PageHeader title={pageHeader.title} breadcrumb={pageHeader.breadcrumb}>
        {/*<ImportButton title={'Import File'} />*/}
      </PageHeader>

      {
        carType?.data ? <CreateEdit id={id} carType={carType?.data} /> : null
      }
    </>
  );
}
