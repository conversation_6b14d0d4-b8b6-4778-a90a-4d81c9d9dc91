import { Request, Response } from 'express';
import {
  getBookingByBookingCode,
} from '../../services/booking';

export const getBookingByBookingCodeHandler = async (req: Request, res: Response) => {
  const { booking_code } = req.params;
  const response = await getBookingByBookingCode(booking_code);
  if (!response.success) {
    return res.status(404).json(response);
  }
  return res.json(response);
};
