"use client";

import { routes } from '@/config/routes';
import PageHeader from '@/app/shared/lib/page-header';
import ImportButton from '@/app/shared/lib/import-button';
import CreateEdit from '@/app/shared/app/drivers/create-edit';
import {useGetDriver} from "@/services/query/driver/useGetDriver";

const pageHeader = {
  title: 'Edit Driver',
  breadcrumb: [
    {
      href: routes.dashboard,
      name: 'Dashboard',
    },
    {
      href: routes.drivers,
      name: 'Drivers',
    },
    {
      name: 'Edit Driver',
    },
  ],
};

export default function EditShipmentsPage({
  params,
}: {
  params: { id: string };
}) {

  const id = params.id;

  const {
    isPending,
    isError,
    error,
    data,
    isFetching,
    isPlaceholderData,
    refetch,
    status,
  } = useGetDriver(id);

  return (
    <>
      <PageHeader title={pageHeader.title} breadcrumb={pageHeader.breadcrumb}>
        <ImportButton title={'Import File'} />
      </PageHeader>

      {
        data?.data ? <CreateEdit id={id} driver={data?.data} /> : null
      }

    </>
  );
}
