import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  <PERSON>inC<PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
// eslint-disable-next-line import/no-cycle
import { Bag } from './Bag';
import {CarType} from "./CarType";

@Entity()
export class CarTypeBag {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ManyToOne(() => Bag, { onDelete: 'CASCADE' })
  @JoinColumn({ name: "bag_id" })
  bag: Bag;

  @ManyToOne(() => CarType, (carType: CarType) => carType.bags, { onDelete: 'CASCADE' })
  @JoinColumn({ name: "car_type_id" })
  carType: CarType;

  @Column()
  quantity: number;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
