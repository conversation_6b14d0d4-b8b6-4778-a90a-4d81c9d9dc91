import { z } from 'zod';

/**
 * Role schema
 */
export const RoleSchema = z.object({
  id: z.number(),
  name: z.string(),
  description: z.string().optional(),
});

/**
 * User schema with validation
 */
export const UserSchema = z.object({
  id: z.number(),
  username: z.string().min(4).max(15),
  email: z.string().email(),
  password: z.string().optional(), // Never returned from API, only used for creation
  avatar_url: z.string().url().optional().nullable(),
  country: z.string().optional().nullable(),
  bio: z.string().optional().nullable(),
  expo_push_token: z.string().optional().nullable(),
  created_at: z.string().or(z.date()).transform(val => new Date(val)),
  first_name: z.string(),
  last_name: z.string(),
  role: RoleSchema.optional().nullable(),
  driver: z.any().optional().nullable(), // Will be a Driver type, but defined in driver.ts
});

/**
 * User creation schema
 */
export const UserCreateSchema = UserSchema.omit({ id: true, created_at: true })
  .extend({
    password: z.string().min(6),
    confirmed_password: z.string().min(6),
  })
  .refine(data => data.password === data.confirmed_password, {
    message: 'Passwords do not match',
    path: ['confirmed_password'],
  });

/**
 * User update schema
 */
export const UserUpdateSchema = UserSchema
  .partial()
  .omit({ id: true, created_at: true })
  .extend({
    password: z.string().min(6).optional(),
    confirmed_password: z.string().min(6).optional(),
    // Allow empty strings for these fields
    bio: z.string().optional(),
    avatar_url: z.string().optional(),
    country: z.string().optional(),
  })
  .refine(
    data => !data.password || !data.confirmed_password || data.password === data.confirmed_password,
    {
      message: 'Passwords do not match',
      path: ['confirmed_password'],
    }
  );

// Type definitions derived from Zod schemas
export type Role = z.infer<typeof RoleSchema>;
export type User = z.infer<typeof UserSchema>;
export type UserCreate = z.infer<typeof UserCreateSchema>;
export type UserUpdate = z.infer<typeof UserUpdateSchema>; 