import {MigrationInterface, QueryRunner, TableColumn} from "typeorm";

export class addMaxDistanceToCarTypePrice1710887677686 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.addColumn('car_type', new TableColumn({
            name: 'max_distance',
            type: 'int',
            isNullable: true // Make false if this column should not be nullable
        }));
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropColumn('car_type', 'max_distance');
    }

}
