'use client';

import toast from 'react-hot-toast';
import {useAtom} from 'jotai';
import {zodResolver} from '@hookform/resolvers/zod';
import {Controller, SubmitHandler, useForm} from 'react-hook-form';
import FormSummary from '@/app/shared/app/bookings/create-multi-step/form-summary';
import {
    formDataAtom,
    useStepperOne,
} from '@/app/shared/app/bookings/create-multi-step';
import {
    customerDetailsSchema,
    CustomerDetailsSchema,
} from './form/multistep-form.schema';
import {Input, Text} from "rizzui";
import FormGroup from "@/app/shared/lib/form-group";
import React, {useEffect} from "react";
import 'react-phone-input-2/lib/style.css'
import TelephoneInput from "@/components/app/PhoneInput";
import validateEmail from "@/utils/email-validator";
import {useBookingSession} from "@/hooks/use-booking-ls";
export default function StepFourCustomerDetails() {
    const {step, gotoNextStep} = useStepperOne();
    const [formData, setFormData] = useAtom(formDataAtom);
    const currentStep = 4;
    const bookingSession = useBookingSession();

    const {
        handleSubmit,
        register,
        formState: {errors},
        control,
        setValue,
    } = useForm<CustomerDetailsSchema>({
        resolver: zodResolver(customerDetailsSchema),
        defaultValues: {
            customer_first_name: formData.customer_first_name ?? undefined,
            customer_last_name: formData.customer_last_name ?? undefined,
            customer_phone_number: `${formData.customer_phone_number}` ?? undefined,
            customer_email: formData.customer_email ?? undefined,
        },
    });

    const onSubmit: SubmitHandler<CustomerDetailsSchema> = (data) => {
        console.log('data', data);

        // # Validations
        if(!data.customer_first_name) {
            toast.error('Please tell us your name.')
            return;
        }

        if(data.customer_email && !validateEmail(data.customer_email)) {
            toast.error('Please provide a valid e-mail address.')
            return;
        }

        bookingSession?.updateOrInitSession(currentStep + 1, data);

        setFormData((prev) => ({
            ...prev,
            ...data,
        }));
        gotoNextStep();
    };

    return (
        <>
            <div className="col-span-full flex flex-col justify-center @5xl:col-span-5">
                <FormSummary
                    title="Customer Details"
                    description="Tell us about yourself! Also, are you a returning customer ? If so, make sure you send us the phone number you used in the past."
                />
            </div>
            <div className="col-span-full items-center justify-center text-creamyWhite">
                <form
                    id={`rhf-${step.toString()}`}
                    onSubmit={handleSubmit(onSubmit)}
                    className="flex-grow rounded-lg mb-16 text-creamyWhite">
                    <FormGroup
                        title="Enter your details here"
                        description=""
                        headingColor='text-creamyWhite'
                    >
                        <Controller
                            control={control}
                            name="customer_phone_number"
                            render={({field: {value, onChange}}) => {
                                return (
                                    <>
                                        <TelephoneInput value={value} onChange={onChange} error={errors.customer_phone_number?.message} />
                                    </>
                                )
                            }}
                        />

                        <Input
                            label="Email"
                            placeholder="..."
                            type={'text'}
                            labelClassName="font-medium text-creamyWhite"
                            {...register('customer_email')}
                            error={errors.customer_email?.message as string}
                        />

                        <Input
                            label="First Name"
                            placeholder="..."
                            type={'text'}
                            labelClassName="font-medium text-creamyWhite"
                            {...register('customer_first_name')}
                            error={errors.customer_first_name?.message as string}
                        />

                        <Input
                            label="Last Name"
                            placeholder="..."
                            type={'text'}
                            labelClassName="font-medium text-creamyWhite"
                            {...register('customer_last_name')}
                            error={errors.customer_last_name?.message as string}
                        />
                    </FormGroup>
                </form>
            </div>
        </>
    );
}
