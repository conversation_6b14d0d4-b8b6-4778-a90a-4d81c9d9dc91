import {getRepository} from 'typeorm';
import {Booking} from '../../models/Booking';
import {Driver} from '../../models/Driver';
import {Car} from '../../models/Car';
import {Customer} from '../../models/Customer';
import {Payment} from '../../models/Payment';
import {interpretDatabaseError} from '../../utils/interpretDatabaseError';
import {getAllSettings} from "../setting";

let MAX_MINUTES_BEFORE_BOOKING_MODS: number;

export const updateBooking = async (id: string, data: Partial<Booking>) => {
    const bookingRepository = getRepository(Booking);
    const driverRepository = getRepository(Driver);
    const carRepository = getRepository(Car);
    const customerRepository = getRepository(Customer);
    const paymentRepository = getRepository(Payment);

    try {
        const booking = await bookingRepository.findOne(id, {
            relations: ['customer', 'payment', 'driver']
        });

        if (!booking) {
            return {success: false, data: null, error: 'Booking not found'};
        }

        // # Get Global Settings
        const globalSettings = await getAllSettings();
        if (globalSettings && globalSettings.data && globalSettings.data.length > 0) {
            MAX_MINUTES_BEFORE_BOOKING_MODS = Number(globalSettings.data.find((gs: any) => gs.name === 'max_minutes_before_booking_mods')?.value);
        }

        if(data.customer && data.customer.is_phone_validated && (data.customer.is_phone_validated !== booking.customer.is_phone_validated)) {
            await customerRepository.update(booking.customer.id, {
                is_phone_validated: data.customer.is_phone_validated,
            });
        }

        const currentTime = new Date();
        const scheduledAtMinusBuffer = new Date(new Date(booking.scheduled_at).getTime() - MAX_MINUTES_BEFORE_BOOKING_MODS * 60000);

        // # Validate that booking cannot be updated past the allowed modification window
        if ((currentTime > scheduledAtMinusBuffer) && (data.driver || data.status)) {
            return {
                success: false,
                data: null,
                error: 'Booking cannot be updated as the scheduled time has passed the allowed modification window.'
            };
        }

        // console.log('booking', booking.payment)
        // console.log('data.payment', data.payment)

        if(data.payment && data.payment.amount_paid && data.payment.amount_paid > 0) {
            const updatePayment = {
                amount: data.payment.amount_paid,
                paid_status: data.payment.amount_paid >= booking.payment.amount,
            };
            // console.log('data.payment.amount_paid', typeof data.payment.amount_paid, data.payment.amount_paid)
            // console.log('booking.payment.amount', typeof booking.payment.amount, booking.payment.amount)
            // console.log('+data.payment.amount_paid >= +booking.payment.amount', data.payment.amount_paid >= booking.payment.amount, updatePayment)
            await paymentRepository.update(booking.payment.id, updatePayment);

            // # Enable this if you want this check
            // if(data.payment?.amount_paid > booking.payment.amount_paid) {
            //     await paymentRepository.update(booking.payment.id, {
            //         amount: data.payment.amount_paid,
            //         paid_status: data.payment.amount_paid >= booking.payment.amount,
            //     });
            // } else {
            //     return {
            //         success: false,
            //         data: null,
            //         error: 'Cannot update the amount of this booking payment because the new amount is smaller then the already existing one.',
            //     };
            // }
        }

        // # Validate - update status to completed cannot occur unless the booking has been entirely paid.
        if(booking && data.status === 'completed') {
            // if(new Date(booking.scheduled_at).getTime() >= new Date().getTime()) {
            //     return {
            //         success: false,
            //         data: null,
            //         error: 'Cannot update the status of this booking to completed because the scheduled at date is still in the future.',
            //     };
            // }
            if((booking.status !== 'accepted')) {
                return {
                    success: false,
                    data: null,
                    error: 'Cannot update the status of this booking to completed because the booking does not have the status of Accepted.',
                };
            }
            if(!booking.driver) {
                return {
                    success: false,
                    data: null,
                    error: 'Cannot update the status of this booking to completed because the booking does not have a driver attached.',
                };
            }
            if(
                (data.payment && data.payment.amount_paid && (data.payment.amount_paid >= booking.payment.amount)) ||
                (booking.payment.amount_paid >= booking.payment.amount)
            ) {
                booking.status = 'completed';
            } else {
                return {
                    success: false,
                    data: null,
                    error: 'Cannot update the status of this booking to `completed` because it was not yet fully paid.',
                };
            }

            booking.booking_finished_at = data.booking_finished_at || new Date();
        }

        // # Validate that booking_finished_at is not in the future
        if (data.booking_finished_at && new Date(data.booking_finished_at) > currentTime) {
            return {
                success: false,
                data: null,
                error: 'Booking finished time cannot be set to a future date.'
            };
        }

        if (data.driver) {
            const driver = await driverRepository.findOne(data.driver, {relations: ['car']});
            if (!driver) {
                return {success: false, data: null, error: 'Driver not found'};
            }
            if (!driver.car) {
                return {success: false, data: null, error: 'Selected driver does not have a car associated'};
            }

            booking.driver = driver;

            if (booking.driver.car) {
                const car = await carRepository.findOne(booking.driver.car.id);
                if (!car) {
                    return {success: false, data: null, error: 'Car not found'};
                }
                booking.car = car;
            }

            if (booking.car && booking.status === 'pending') {
                booking.status = 'accepted';
            }
        }

        bookingRepository.merge(booking, data);

        const updatedBooking = await bookingRepository.save(booking);

        return {success: true, data: updatedBooking, error: null};

    } catch (error) {
        const interpretedError = interpretDatabaseError(error);
        return {success: false, data: null, error: interpretedError};
    }
};
