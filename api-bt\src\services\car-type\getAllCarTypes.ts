import { getRepository, MoreThanOrEqual } from 'typeorm';
import { CarType } from '../../models/CarType'; // Adjust the path as necessary
import { paginate, PaginatedResult } from '../../utils/pagination';

export const getAllCarTypes = async (
    page = 1,
    limit = 10,
    seats?: number, // Make seats an optional parameter
): Promise<PaginatedResult<CarType>> => {
  const carTypeRepository = getRepository(CarType);

  const options: any = {
    skip: (page - 1) * limit,
    take: limit,
    relations: ['cars', 'bags', 'bags.bag', 'transfer_prices'],
    order: {
      created_at: 'DESC',  // Order by created_at in descending order
    },
  };

  // Add a where condition if seats are provided
  if (seats && seats > 0) {
    options.where = {
      seats: MoreThanOrEqual(seats),
    };
  }

  const [data, totalRecords] = await carTypeRepository.findAndCount(options);

  return paginate<CarType>(data, page, limit, totalRecords);
};
