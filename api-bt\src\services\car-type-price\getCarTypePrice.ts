import { getRepository } from 'typeorm';
import { CarTypePrice } from '../../models/CarTypePrice';
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const getCarTypePrice = async (id: string) => {
    const carTypePriceRepository = getRepository(CarTypePrice);
    try {
        const carTypePrice = await carTypePriceRepository.findOne(id);
        if (!carTypePrice) {
            return { success: false, data: null, error: 'CarTypePrice not found' };
        }
        return { success: true, data: carTypePrice, error: null };
    } catch (error) {
        const interpretedError = interpretDatabaseError(error);
        return { success: false, data: null, error: interpretedError };
    }
};
