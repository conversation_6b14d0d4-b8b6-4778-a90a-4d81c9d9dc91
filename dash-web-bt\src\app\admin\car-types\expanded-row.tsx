import { Title, Text } from 'rizzui';
import React from 'react';
import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime';
import { routes } from "@/config/routes";
import { PiPen, PiDiscFill, PiMoney, PiX, PiCar } from 'react-icons/pi';

interface Bag {
    id: string;
    quantity: number;
    bag: {
        id: string;
        name: string;
        description: string;
    };
}

interface Price {
    id: string;
    name: string;
    price_per_km: number;
    range_max_distance: number;
}

interface Car {
    id: string;
    name: string;
    image: string;
    brand: string;
    model: string;
    allow_pets: boolean;
    licence_plate: string;
    bags_capacity: number;
}

interface CarTypeProps {
    data: {
        cars: Car[];
        bags: Bag[];
        transfer_prices: Price[];
    };
    router: AppRouterInstance;
}

export default function ExpandedRow({ data, router }: CarTypeProps) {

    if (!data) {
        return (
            <div className="flex justify-center items-center py-8">
                <Text className="text-center text-gray-500">
                    No current driver data associated with this car.
                </Text>
            </div>
        );
    }

    const handleEditCar = (carId: string) => {
        router.push(routes.carEdit(carId));
    };

    const handleEditPrice = (priceId: string) => {
        router.push(routes.carTypeEdit(priceId));
    };

    const handleEditBag = (bagId: string) => {
        router.push(routes.bagEdit(bagId));
    };

    return (
        <div className="grid grid-cols-1 divide-y px-4 py-6 shadow rounded-lg">
            {data?.cars?.map((car) => (
                <article
                    key={car.id}
                    className="py-4 flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-6 p-4 rounded-lg shadow-md"
                >
                    <img
                        src={car.image}
                        alt={car.name}
                        className="w-full sm:w-24 h-24 object-cover rounded-lg shadow-lg border-2 border-blue-300"
                    />
                    <div className="flex-1">
                        <div className="flex items-center">
                            <PiCar className="text-blue-600 text-2xl mr-2"/>
                            <Title as="h4" className="text-lg font-bold text-blue-700">
                                {car.name}
                            </Title>
                        </div>
                        <Text className="text-sm text-gray-700 mt-1">
                            <span className="font-medium text-blue-600">{car.brand}</span> {car.model}
                            <span className="block mt-1">License Plate: <span
                                className="text-blue-600 text-xl">{car.licence_plate}</span></span>
                        </Text>
                        <Text className="text-sm text-gray-700 mt-2">
                            Bag Capacity: <span className="font-medium text-blue-600">{car.bags_capacity}</span>,
                            Pets Allowed: <span
                            className="font-medium text-blue-600">{car.allow_pets ? 'Yes' : 'No'}</span>
                        </Text>
                    </div>
                    <button
                        onClick={() => handleEditCar(car.id)}
                        className="px-2 py-1 bg-green-500 text-white rounded-md hover:bg-green-600 transition text-xs flex items-center shadow-md"
                    >
                        <PiPen className="mr-1"/>
                        Edit
                    </button>
                </article>
            ))}

            <div className="py-6">
                <Title as="h5" className="text-md font-semibold text-gray-800">
                    Prices
                </Title>
                <div className="space-y-4">
                    {data?.transfer_prices?.map((price) => (
                        <div
                            key={price.id}
                            className="relative border border-gray-200 rounded-lg p-4 flex items-center"
                        >
                            <PiMoney className="text-green-500 text-xl mr-4"/>
                            <div className="flex-1">
                                <Text className="text-sm text-gray-800 font-semibold">
                                    {price.name}
                                </Text>
                                <div className="flex items-center text-gray-600 mt-2">
                                    <PiDiscFill className="text-blue-600 mr-2"/>
                                    <Text className="text-lg font-bold text-gray-800">
                                        ${price.price_per_km}
                                    </Text>
                                    <Text className="text-sm ml-1">
                                        /km
                                    </Text>
                                    <PiX className="text-gray-500 mx-2"/>
                                    <Text className="text-sm">
                                        up to {price.range_max_distance}km
                                    </Text>
                                </div>
                            </div>
                            <button
                                onClick={() => handleEditPrice(price.id)}
                                className="px-2 py-1 bg-green-500 text-white rounded-md hover:bg-green-600 transition text-xs flex items-center shadow-md"
                            >
                                <PiPen className="mr-1"/>
                                Edit
                            </button>
                        </div>
                    ))}
                </div>
            </div>

            <div className="py-6">
                <Title as="h5" className="text-md font-semibold text-gray-800">
                    Bags
                </Title>
                <div className="space-y-4">
                    {data.bags.map((bag) => (
                        <div
                            key={bag.id}
                            className="flex justify-between items-center border border-gray-200 rounded-lg p-4  shadow-md"
                        >
                            <div className="flex items-center">
                                <div className="bg-green-500 text-white p-2 rounded-full mr-4">
                                    <PiDiscFill className="text-lg"/>
                                </div>
                                <Text className="text-sm text-gray-800">
                                    <span
                                        className="font-bold text-green-700">{bag.quantity} x</span> {bag.bag.name}: {bag.bag.description}
                                </Text>
                            </div>
                            <button
                                onClick={() => handleEditBag(bag.bag.id)}
                                className="px-2 py-1 bg-green-500 text-white rounded-md hover:bg-green-600 transition text-xs flex items-center shadow-md"
                            >
                                <PiPen className="mr-1"/>
                                Edit
                            </button>
                        </div>
                    ))}
                </div>
            </div>

        </div>
    );
}
