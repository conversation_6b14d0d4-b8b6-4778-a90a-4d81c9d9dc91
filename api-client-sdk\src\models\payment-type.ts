import { z } from 'zod';

/**
 * PaymentType schema with validation
 */
export const PaymentTypeSchema = z.object({
  id: z.number(),
  name: z.string(),
  description: z.string().nullable().optional(),
  created_at: z.string().or(z.date()).transform(val => new Date(val)),
  updated_at: z.string().or(z.date()).transform(val => new Date(val)),
  updated_by: z.number().nullable().optional(),
});

/**
 * PaymentType creation schema
 */
export const PaymentTypeCreateSchema = PaymentTypeSchema.omit({ 
  id: true, 
  created_at: true, 
  updated_at: true 
});

/**
 * PaymentType update schema
 */
export const PaymentTypeUpdateSchema = PaymentTypeSchema
  .partial()
  .omit({ 
    id: true, 
    created_at: true, 
    updated_at: true 
  });

// Type definitions derived from Zod schemas
export type PaymentType = z.infer<typeof PaymentTypeSchema>;
export type PaymentTypeCreate = z.infer<typeof PaymentTypeCreateSchema>;
export type PaymentTypeUpdate = z.infer<typeof PaymentTypeUpdateSchema>; 