import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class licencePlateOnCarNowUnique1723555882192 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.changeColumn(
            'car',
            'licence_plate',
            new TableColumn({
                name: 'licence_plate',
                type: 'text',
                isNullable: true,
                isUnique: true, // Set the column to be unique
            })
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.changeColumn(
            'car',
            'licence_plate',
            new TableColumn({
                name: 'licence_plate',
                type: 'text',
                isNullable: true,
                isUnique: false, // Revert the unique constraint
            })
        );
    }

}
