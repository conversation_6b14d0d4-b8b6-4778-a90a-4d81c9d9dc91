'use client';

import { PiPlusBold } from 'react-icons/pi';
import { routes } from '@/config/routes';
import { Button } from 'rizzui';
import PageHeader from '@/app/shared/lib/page-header';
import ExportButton from '@/app/shared/lib/export-button';
import { appointmentData } from '@/data/appointment-data';
import { useRouter } from 'next/navigation';

const pageHeader = {
  title: 'Tickets List',
  breadcrumb: [
    {
      href: routes.dashboard,
      name: 'Home',
    },
    {
      name: 'Tickets List',
    },
  ],
};

interface HeaderProps {
  className?: string;
}

export default function BookingsListPageHeader({ className }: HeaderProps) {
  const router = useRouter();

  let handleCreate = () => {
    router.push(routes.ticketCreate); // Replace '/path-to-navigate' with your target path
  };

  return (
    <PageHeader title={pageHeader.title} breadcrumb={pageHeader.breadcrumb}>
      <div className="mt-4 flex flex-col items-center gap-3 @sm:flex-row @lg:mt-0">

      </div>
    </PageHeader>
  );
}
