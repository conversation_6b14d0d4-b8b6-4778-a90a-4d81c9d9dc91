import {z} from 'zod';
import {CarTypeResponseSchema} from "@/utils/validators/app/entities";

// step 2
export const journeyDetailsSchema = z.object({
    // # Journey Details
    trip_type: z.string().max(20),
    pickup_address: z.string(),
    pickup_lat: z.string(),
    pickup_long: z.string(),
    destination_address: z.string().optional().nullable(),
    destination_lat: z.string().optional().nullable(),
    destination_long: z.string().optional().nullable(),
    scheduled_at: z.string(),
    one_way_trip: z.boolean(),
    return_trip_time: z.string().optional().nullable(),
    trip_duration: z.number().optional().nullable(), // Change amount_to_pay into final_amount_to_pay
    trip_distance: z.number().optional().nullable(),
    return_trip_duration: z.number().optional().nullable(), // Change amount_to_pay into final_amount_to_pay
    return_trip_distance: z.number().optional().nullable(),
    estimated_service_hours: z.number().optional().nullable(),
});

// generate form types from zod validation schema
export type JourneyDetailsSchema = z.infer<typeof journeyDetailsSchema>;

// step 3
export const carTypeSchema = z.object({
    car_type: CarTypeResponseSchema.or(z.string().uuid()),
    bags: z.array(z.any()),
});

export type CarTypeSchema = z.infer<typeof carTypeSchema>;

// step 4
export const passengerDetailsSchema = z.object({
    // # Passenger Details
    adults: z.number(),
    children: z.number().optional().nullable(),
    children_chairs_under_five: z.number().optional().nullable(),
    children_chairs_above_five: z.number().optional().nullable(),
    infant_seats: z.number().optional().nullable(),
    pets: z.number().optional().nullable(),
    // flight_number: z.string().regex(/\b([A-Z]{2}|[A-Z]\d|\d[A-Z])\s?\d{3,4}\b/),
    flight_number: z.string(),
    other_details: z.string().optional().nullable(),
});

export type PassengerDetailsSchema = z.infer<typeof passengerDetailsSchema>;

// step 5
export const customerDetailsSchema = z.object({
    // # Customer Details
    // Customer Details
    customer_phone_number: z.string(),
    customer_first_name: z.string(),
    customer_last_name: z.string().optional().nullable(),
    customer_email: z.string().email(),
});

export type CustomerDetailsSchema = z.infer<typeof customerDetailsSchema>;

// step 6
export const paymentDetailsSchema = z.object({
    // # Payment Details
    partial_payment: z.string(), // Change to partial_payment boolean default false
    recommended_amount_to_pay: z.number().optional().nullable(), // Create recommended_amount_to_pay
    final_amount_to_pay: z.number(), // Create recommended_amount_to_pay
    // tip: z.number().optional().nullable(), // Change amount_to_pay into final_amount_to_pay
    payment_type: z.string().uuid(),
});

export type PaymentDetailsSchema = z.infer<typeof paymentDetailsSchema>;
