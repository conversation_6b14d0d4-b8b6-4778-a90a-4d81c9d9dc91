'use client';

import Link from 'next/link';
import Image from 'next/image';
import cn from '@/utils/class-names';
import React from "react";
import {But<PERSON>} from "rizzui";
import {FaArrowCircleLeft} from "react-icons/fa";
import {useRouter} from "next/navigation";
import {routes} from "@/config/routes";

interface FooterProps {
  className?: string;
}

export default function Header({ className }: FooterProps) {
  // const isMobile = useMedia('(max-width: 767px)', false);
  const router = useRouter();

  const exit = () => {
    router.push(routes.landing);
  }

  const faq = () => router.push(routes.landing)

  return (
    <header
      className={cn(
        'flex w-full items-center justify-between px-4 py-5 md:h-20 md:px-5 lg:px-8 4xl:px-10',
        className
      )}
    >

      <div className="flex items-center gap-2">
        <Button variant="text" className="text-white hover:enabled:text-white" onClick={faq}>
          Questions?
        </Button>
        <Button
            onClick={exit}
          rounded="pill"
          variant="outline"
          className="gap-2 whitespace-nowrap border-white text-white hover:border-white hover:bg-white hover:text-black"
        >
          <FaArrowCircleLeft className="h-4 w-4" />
          Exit
        </Button>
      </div>
    </header>
  );
}
