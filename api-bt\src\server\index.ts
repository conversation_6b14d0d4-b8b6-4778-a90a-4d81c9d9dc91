import 'dotenv/config';

import compression from 'compression';
import cors from 'cors';
import express from 'express';
import passport from 'passport';
import { rateLimit } from 'express-rate-limit'
import { checkToken, checkAdmin, checkDriver } from '../config/safeRoutes';

import initPassport from '../config/passport';
import {
  authRouter,
  bagRouter,
  zoneRouter,
  driverRouter,
  customerRouter,
  carTypeRouter,
  ticketRouter,
  carRouter,
  bookingRouter,
  paymentTypeRouter,
  paymentRouter,
  carTypeBagRouter,
  carTypePriceRouter,
  settingRouter,
  userRouter,
  roleRouter,
} from '../routes/admin';

import {
  publicBookingRouter,
  publicTicketRouter,
  publicCarTypeRouter,
  publicSettingRouter,
  publicPaymentTypeRouter,
} from '../routes/public';

import {
driverAuthRouter,
driverBookingRouter,
driverCustomerRouter,
driverSettingRouter,
driverUserRouter,
} from '../routes/driver';


import {
  webhook,
} from '../routes/webhook';


import { connect } from './database';

// Instantiate rate limiter
const limiter = rateLimit({
	windowMs: 1 * 60 * 1000, // 1 minute
	limit: 7, // Limit each IP to  requests per `window` (here, per 1 minute).
	standardHeaders: 'draft-7', // draft-6: `RateLimit-*` headers; draft-7: combined `RateLimit` header
	legacyHeaders: false, // Disable the `X-RateLimit-*` headers.
	// store: ... , // Redis, Memcached, etc. See below.
});

// Instantiate express
const server = express();
server.use(compression());

// Passport Config
initPassport(passport);
server.use(passport.initialize());

// Connect to sqlite
if (process.env.NODE_ENV !== 'test') {
  connect();
}

server.use(cors());
server.use(express.json());

// Initialize admin routes middleware
server.use('/v1/admin/auth', limiter, authRouter);
server.use('/v1/admin', checkToken, checkAdmin, bagRouter);
server.use('/v1/admin', checkToken, checkAdmin, zoneRouter);
server.use('/v1/admin', checkToken, checkAdmin, driverRouter);
server.use('/v1/admin', checkToken, checkAdmin, customerRouter);
server.use('/v1/admin', checkToken, checkAdmin, carTypeRouter);
server.use('/v1/admin', checkToken, checkAdmin, ticketRouter);
server.use('/v1/admin', checkToken, checkAdmin, carRouter);
server.use('/v1/admin', checkToken, checkAdmin, bookingRouter);
server.use('/v1/admin', checkToken, checkAdmin, paymentTypeRouter);
server.use('/v1/admin', checkToken, checkAdmin, paymentRouter);
server.use('/v1/admin', checkToken, checkAdmin, carTypeBagRouter);
server.use('/v1/admin', checkToken, checkAdmin, carTypePriceRouter);
server.use('/v1/admin', checkToken, checkAdmin, settingRouter);
server.use('/v1/admin', checkToken, checkAdmin, userRouter);
server.use('/v1/admin', checkToken, checkAdmin, roleRouter);

// Initialize driver routes middleware
server.use('/v1/driver/auth', driverAuthRouter);
server.use('/v1/driver', checkToken, checkDriver, driverBookingRouter);
server.use('/v1/driver', checkToken, checkDriver, driverCustomerRouter);
server.use('/v1/driver', checkToken, checkDriver, driverSettingRouter);
server.use('/v1/driver', checkToken, checkDriver, driverUserRouter);

// Initialize public routes middleware
server.use('/v1/public', publicBookingRouter);
server.use('/v1/public', publicTicketRouter);
server.use('/v1/public', publicCarTypeRouter);
server.use('/v1/public', publicSettingRouter);
server.use('/v1/public', publicPaymentTypeRouter);

// Initialize webhook routes middleware
server.use('/v1/webhook/payment/stripe', webhook.payment.stripe.charge);

export default server;