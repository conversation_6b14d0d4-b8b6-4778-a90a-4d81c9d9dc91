import { isValidResponse, normalizeResponse } from '../response-utils';
import { ApiResponse } from '../types';

describe('isValidResponse', () => {
  it('should return false for null or undefined', () => {
    expect(isValidResponse(null)).toBe(false);
    expect(isValidResponse(undefined)).toBe(false);
  });

  it('should return false for non-object values', () => {
    expect(isValidResponse(123)).toBe(false);
    expect(isValidResponse('string')).toBe(false);
    expect(isValidResponse(true)).toBe(false);
  });

  it('should return false for objects without success property', () => {
    expect(isValidResponse({})).toBe(false);
    expect(isValidResponse({ data: 'test' })).toBe(false);
  });

  it('should return true for objects with success property', () => {
    expect(isValidResponse({ success: true })).toBe(true);
    expect(isValidResponse({ success: false })).toBe(true);
    expect(isValidResponse({ success: true, data: 'test' })).toBe(true);
  });
});

describe('normalizeResponse', () => {
  it('should return valid responses unchanged', () => {
    const validResponse: ApiResponse<string> = { 
      success: true, 
      data: 'test' 
    };
    
    expect(normalizeResponse(validResponse)).toEqual(validResponse);
    
    const validErrorResponse: ApiResponse<never> = { 
      success: false, 
      error: { 
        success: false, 
        msg: 'Error' 
      } 
    };
    
    expect(normalizeResponse(validErrorResponse)).toEqual(validErrorResponse);
  });

  it('should handle null or undefined responses', () => {
    const expectedErrorResponse = {
      success: false,
      error: {
        success: false,
        msg: 'Empty response received',
      }
    };
    
    expect(normalizeResponse(null)).toEqual(expectedErrorResponse);
    expect(normalizeResponse(undefined)).toEqual(expectedErrorResponse);
  });

  it('should normalize objects with data property', () => {
    const rawResponse = { data: 'test data' };
    const expectedResponse = {
      success: true,
      data: 'test data'
    };
    
    expect(normalizeResponse(rawResponse)).toEqual(expectedResponse);
  });

  it('should treat objects without data property as data', () => {
    const rawResponse = { test: 'value', other: 123 };
    const expectedResponse = {
      success: true,
      data: { test: 'value', other: 123 }
    };
    
    expect(normalizeResponse(rawResponse)).toEqual(expectedResponse);
  });

  it('should wrap primitive values as data', () => {
    expect(normalizeResponse('test string')).toEqual({
      success: true,
      data: 'test string'
    });
    
    expect(normalizeResponse(123)).toEqual({
      success: true,
      data: 123
    });
    
    expect(normalizeResponse(true)).toEqual({
      success: true,
      data: true
    });
  });
}); 