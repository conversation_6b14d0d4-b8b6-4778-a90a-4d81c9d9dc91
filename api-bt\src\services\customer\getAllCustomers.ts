import { getRepository } from 'typeorm';
import { Customer } from '../../models/Customer'; // Adjust the import path as per your project structure
import { paginate, PaginatedResult } from '../../utils/pagination'; // Adjust the import path as per your project structure

export const getAllCustomers = async (
  page = 1,
  limit = 10,
): Promise<PaginatedResult<Customer>> => {
  const customerRepository = getRepository(Customer);
  const options: any = {
    skip: (page - 1) * limit,
    take: limit,
    relations: ['user', 'tickets', 'orders'], // Adjust based on your needs
    order: {
      created_at: 'DESC',  // Order by created_at in descending order
    },
  };

  try {
    const [data, totalRecords] = await customerRepository.findAndCount(options);
    return paginate<Customer>(data, page, limit, totalRecords);
  } catch (error) {
    console.error('Error fetching customers with pagination:', error);
    throw new Error('An error occurred while fetching customers.');
  }
};
