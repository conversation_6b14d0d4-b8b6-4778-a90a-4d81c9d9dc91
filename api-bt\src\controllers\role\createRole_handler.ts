import { Request, Response } from 'express';
import {
  createRole,
} from '../../services/role';

export const createRole_handler = async (req: Request, res: Response) => {
  try {
    const data = req.body;
    const result = await createRole(data);
    if (!result.success) {
      return res.status(404).json(result);
    }
    return res.json(result);
  } catch (error: any) {
    return res.status(500).json({
      success: false,
      message: 'Failed to create role',
      errorType: 'InternalServerError',
      data: undefined,
    });
  }
};
