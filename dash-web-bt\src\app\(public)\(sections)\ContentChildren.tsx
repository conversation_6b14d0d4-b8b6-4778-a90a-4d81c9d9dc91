import {useI18n} from "@/hooks/use-translation";
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>un} from "react-icons/pi";
import dynamic from "next/dynamic";

import {
    FaBook,
    FaCircleQuestion,
    FaEnvelope,
    FaFileContract,
    FaPeopleGroup,
    FaQuoteLeft,
    FaRocket
} from "react-icons/fa6";
import CurrentBookingDraft from "@/app/(public)/(sections)/CurrentBookingDraft";
import Navbar from "@/app/(public)/(sections)/Navbar";
import InputPickupAddress from "@/app/(public)/(sections)/InputPickupAddress";
import {InfiniteMovingCards} from "@/components/app/InfiniteMovingCards";
import TabsSection from "@/app/(public)/(sections)/TabsSection";
import {Accordion} from "@/app/(public)/(sections)/Accordion";
import {Toaster} from "react-hot-toast";
import ScrollToTop from "@/components/app/ScrollToTop";
import React from "react";
import testimonials from '../testimonials';
const MainHeroSection = dynamic(
    () => import('@/app/(public)/(sections)/MainHeroSection'),
    { ssr: false }
)
const StickyScrollRevealDemo = dynamic(
    () => import('@/app/(public)/(sections)/StickyScrollRevealSection'),
    { ssr: false }
)

const ContactSection = dynamic(() => import("@/app/(public)/(sections)/ContactSection"), {
    ssr: true,
});

const Footer = dynamic(() => import("@/app/(public)/(sections)/(old)/Footer"), {
    ssr: true,
});

const TracingBeamDemo = dynamic(
    () => import('@/app/(public)/(sections)/TracingBeam'),
    { ssr: false }
)

export default function ContentChildren() {
    const { t } = useI18n();

    const faqData = [
        {
            title: t("faq.pickup.title"),
            icon: <PiSun />,
            defaultOpen: true,
            content: t("faq.pickup.content"),
        },
        {
            title: t("faq.rates.title"),
            icon: <PiMoon />,
            defaultOpen: false,
            content: t("faq.rates.content"),
        },
        {
            title: t("faq.advance_booking.title"),
            icon: <PiLink />,
            defaultOpen: false,
            content: t("faq.advance_booking.content"),
        },
        {
            title: t("faq.cancel_booking.title"),
            icon: <PiSun />,
            defaultOpen: false,
            content: t("faq.cancel_booking.content"),
        },
        {
            title: t("faq.booking_status.title"),
            icon: <PiMoon />,
            defaultOpen: false,
            content: t("faq.booking_status.content"),
        },
        {
            title: t("faq.invoice.title"),
            icon: <PiLink />,
            defaultOpen: false,
            content: t("faq.invoice.content"),
        },
    ];

    const navItems = [
        {
            name: t('nav.book'),
            link: '#book',
            icon: <FaBook className="h-4 w-4 text-neutral-500 dark:text-white" />,
        },
        // {
        //     name: t('nav.team'),
        //     link: '#team',
        //     icon: <FaPeopleGroup className="h-4 w-4 text-neutral-500 dark:text-white" />,
        // },
        {
            name: t('nav.services'),
            link: '#experience',
            icon: <FaRocket className="h-4 w-4 text-neutral-500 dark:text-white" />,
        },
        {
            name: t('nav.testimonials'),
            link: '#testimonials',
            icon: <FaQuoteLeft className="h-4 w-4 text-neutral-500 dark:text-white" />,
        },
        {
            name: t('nav.faq'),
            link: '#faq',
            icon: <FaCircleQuestion className="h-4 w-4 text-neutral-500 dark:text-white" />,
        },
        {
            name: t('nav.contact'),
            link: '#contact',
            icon: <FaEnvelope className="h-4 w-4 text-neutral-500 dark:text-white" />,
        },
        {
            name: t('nav.policies'),
            link: '/gdpr',
            icon: <FaFileContract className="h-4 w-4 text-neutral-500 dark:text-white" />,
        },
    ];

    return (
        <>
            <CurrentBookingDraft />

            <div className="relative w-full">
                <Navbar items={navItems} />
            </div>

            <MainHeroSection />

            <div className="pt-16 bg-black">
                <section className="flex flex-col items-center justify-center p-10 bg-black" id='book'>
                    <InputPickupAddress />
                </section>

                <div
                    id='testimonials'
                    className="flex flex-col antialiased bg-black dark:bg-grid-white/[0.05] items-center justify-center relative overflow-hidden"
                    style={{ "paddingBottom": "26rem" }}>
                    <InfiniteMovingCards
                        items={testimonials}
                        direction="right"
                        speed="slow"
                    />
                </div>
            </div>

            {/* <div id="team">
                <TabsSection />
            </div> */}

            <div id='experience'>
                <TracingBeamDemo />
            </div>

            <StickyScrollRevealDemo />

            <div id='faq'>
                <Accordion data={faqData} />
            </div>

            <ContactSection />

            <Footer />

            <Toaster />

            <ScrollToTop />
        </>
    );
}