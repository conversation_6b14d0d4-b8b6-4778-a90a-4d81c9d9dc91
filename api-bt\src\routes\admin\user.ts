import express from 'express';
import {
  createUser_handler,
  deleteUser_handler, getAllUsers_handler, getUser_handler, updateUser_handler,
} from '../../controllers/user';

export const userRouter = express.Router();

userRouter.post('/users', createUser_handler);
userRouter.get('/users', getAllUsers_handler);
userRouter.get('/users/:id', getUser_handler);
userRouter.put('/users/:id', updateUser_handler);
userRouter.delete('/users/:id', deleteUser_handler);
