export const car_types: any[] = [
    {
        "id": "031f7185-e66f-4735-b936-41a31ec60327",
        "name": "[-1]    Metrou",
        "default_price_per_km": 12,
        "seats": 50,
        "hourly_rate": 12,
        "automatic_acceptance": true,
        "use_default_ppk": false,
        "created_at": "2024-09-21T15:13:06.336Z",
        "updated_at": "2024-09-21T15:13:35.738Z",
        "updated_by": null,
        "cars": [],
        "bags": [
            {
                "quantity": 10,
                "created_at": "2024-09-21T15:13:35.898Z",
                "updated_at": "2024-09-21T15:13:35.898Z",
                "bag": {
                    "id": "71cbe9d6-5b52-48d6-936a-b29a4161c7f9",
                    "name": "Big Bag",
                    "description": "",
                    "created_at": "2024-09-21T13:19:31.418Z",
                    "updated_at": "2024-09-21T13:19:31.418Z"
                }
            }
        ],
        "transfer_prices": [
            {
                "name": "Range-2",
                "price_per_km": "0",
                "range_max_distance": 9055555,
                "fixed_charge_value": 44,
                "created_at": "2024-09-21T15:13:36.070Z",
                "updated_at": "2024-09-21T15:13:36.070Z",
                "updated_by": null
            },
            {
                "name": "Range-1",
                "price_per_km": "0",
                "range_max_distance": 60,
                "fixed_charge_value": 44,
                "created_at": "2024-09-21T15:13:36.015Z",
                "updated_at": "2024-09-21T15:13:36.015Z",
                "updated_by": null
            }
        ]
    },
    {
        "id": "558f89ac-30df-4da6-b36b-2c9855fdd983",
        "name": "[1]Avion",
        "default_price_per_km": 55,
        "seats": 1155,
        "hourly_rate": 55,
        "automatic_acceptance": false,
        "use_default_ppk": false,
        "created_at": "2024-09-21T15:11:49.309Z",
        "updated_at": "2024-09-21T15:11:49.309Z",
        "updated_by": null,
        "cars": [],
        "bags": [
            {
                "quantity": 10,
                "created_at": "2024-09-21T15:11:49.410Z",
                "updated_at": "2024-09-21T15:11:49.410Z",
                "bag": {
                    "id": "d4b88fd0-889a-4363-a4fb-4fda40c253dd",
                    "name": "Satchel",
                    "description": "",
                    "created_at": "2024-09-21T13:19:27.358Z",
                    "updated_at": "2024-09-21T13:19:27.358Z"
                }
            }
        ],
        "transfer_prices": [
            {
                "name": "Range-1",
                "price_per_km": "0",
                "range_max_distance": 60,
                "fixed_charge_value": 50,
                "created_at": "2024-09-21T15:11:49.475Z",
                "updated_at": "2024-09-21T15:11:49.475Z",
                "updated_by": null
            },
            {
                "name": "Range-2",
                "price_per_km": "0",
                "range_max_distance": 90555555,
                "fixed_charge_value": 150,
                "created_at": "2024-09-21T15:11:49.525Z",
                "updated_at": "2024-09-21T15:11:49.525Z",
                "updated_by": null
            }
        ]
    },
    {
        "id": "e25910ac-65cd-485d-9df1-a79828f96449",
        "name": "Tren",
        "default_price_per_km": 22,
        "seats": 122,
        "hourly_rate": 22,
        "automatic_acceptance": true,
        "use_default_ppk": false,
        "created_at": "2024-09-21T15:03:49.162Z",
        "updated_at": "2024-09-21T15:03:49.162Z",
        "updated_by": null,
        "cars": [],
        "bags": [
            {
                "quantity": 10,
                "created_at": "2024-09-21T15:03:49.462Z",
                "updated_at": "2024-09-21T15:03:49.462Z",
                "bag": {
                    "id": "027d235e-4658-4104-8c5f-197f83cfc716",
                    "name": "Very Big Bag",
                    "description": "",
                    "created_at": "2024-09-21T13:19:35.037Z",
                    "updated_at": "2024-09-21T13:19:41.656Z"
                }
            },
            {
                "quantity": 10,
                "created_at": "2024-09-21T15:03:49.265Z",
                "updated_at": "2024-09-21T15:03:49.265Z",
                "bag": {
                    "id": "d4b88fd0-889a-4363-a4fb-4fda40c253dd",
                    "name": "Satchel",
                    "description": "",
                    "created_at": "2024-09-21T13:19:27.358Z",
                    "updated_at": "2024-09-21T13:19:27.358Z"
                }
            },
            {
                "quantity": 10,
                "created_at": "2024-09-21T15:03:49.366Z",
                "updated_at": "2024-09-21T15:03:49.366Z",
                "bag": {
                    "id": "71cbe9d6-5b52-48d6-936a-b29a4161c7f9",
                    "name": "Big Bag",
                    "description": "",
                    "created_at": "2024-09-21T13:19:31.418Z",
                    "updated_at": "2024-09-21T13:19:31.418Z"
                }
            }
        ],
        "transfer_prices": [
            {
                "name": "Range-1",
                "price_per_km": "0",
                "range_max_distance": 60,
                "fixed_charge_value": 50,
                "created_at": "2024-09-21T15:03:49.520Z",
                "updated_at": "2024-09-21T15:03:49.520Z",
                "updated_by": null
            },
            {
                "name": "Range-2",
                "price_per_km": "0",
                "range_max_distance": 90111111,
                "fixed_charge_value": 150,
                "created_at": "2024-09-21T15:03:49.581Z",
                "updated_at": "2024-09-21T15:03:49.581Z",
                "updated_by": null
            }
        ]
    },
    {
        "id": "8e99bd37-cf60-4c0b-839e-87b48e0845e7",
        "name": "[0] Caruta",
        "default_price_per_km": 55,
        "seats": 4,
        "hourly_rate": 15,
        "automatic_acceptance": true,
        "use_default_ppk": false,
        "created_at": "2024-09-21T15:02:34.434Z",
        "updated_at": "2024-09-21T15:05:45.103Z",
        "updated_by": null,
        "cars": [],
        "bags": [
            {
                "quantity": 6,
                "created_at": "2024-09-21T15:05:45.373Z",
                "updated_at": "2024-09-21T15:05:45.373Z",
                "bag": {
                    "id": "71cbe9d6-5b52-48d6-936a-b29a4161c7f9",
                    "name": "Big Bag",
                    "description": "",
                    "created_at": "2024-09-21T13:19:31.418Z",
                    "updated_at": "2024-09-21T13:19:31.418Z"
                }
            },
            {
                "quantity": 6,
                "created_at": "2024-09-21T15:05:45.260Z",
                "updated_at": "2024-09-21T15:05:45.260Z",
                "bag": {
                    "id": "d4b88fd0-889a-4363-a4fb-4fda40c253dd",
                    "name": "Satchel",
                    "description": "",
                    "created_at": "2024-09-21T13:19:27.358Z",
                    "updated_at": "2024-09-21T13:19:27.358Z"
                }
            },
            {
                "quantity": 4,
                "created_at": "2024-09-21T15:05:45.482Z",
                "updated_at": "2024-09-21T15:05:45.482Z",
                "bag": {
                    "id": "027d235e-4658-4104-8c5f-197f83cfc716",
                    "name": "Very Big Bag",
                    "description": "",
                    "created_at": "2024-09-21T13:19:35.037Z",
                    "updated_at": "2024-09-21T13:19:41.656Z"
                }
            }
        ],
        "transfer_prices": [
            {
                "name": "Range-3",
                "price_per_km": "22",
                "range_max_distance": 120,
                "fixed_charge_value": 0,
                "created_at": "2024-09-21T15:05:45.691Z",
                "updated_at": "2024-09-21T15:05:45.691Z",
                "updated_by": null
            },
            {
                "name": "Range-2",
                "price_per_km": "22",
                "range_max_distance": 90,
                "fixed_charge_value": 0,
                "created_at": "2024-09-21T15:05:45.641Z",
                "updated_at": "2024-09-21T15:05:45.641Z",
                "updated_by": null
            },
            {
                "name": "Range-4",
                "price_per_km": "16",
                "range_max_distance": 150,
                "fixed_charge_value": 0,
                "created_at": "2024-09-21T15:05:45.741Z",
                "updated_at": "2024-09-21T15:05:45.741Z",
                "updated_by": null
            },
            {
                "name": "Range-5",
                "price_per_km": "14",
                "range_max_distance": 180,
                "fixed_charge_value": 0,
                "created_at": "2024-09-21T15:05:45.795Z",
                "updated_at": "2024-09-21T15:05:45.795Z",
                "updated_by": null
            },
            {
                "name": "Range-1",
                "price_per_km": "22",
                "range_max_distance": 60,
                "fixed_charge_value": 0,
                "created_at": "2024-09-21T15:05:45.588Z",
                "updated_at": "2024-09-21T15:05:45.588Z",
                "updated_by": null
            },
            {
                "name": "Range-6",
                "price_per_km": "15",
                "range_max_distance": 210555,
                "fixed_charge_value": 0,
                "created_at": "2024-09-21T15:05:45.841Z",
                "updated_at": "2024-09-21T15:05:45.841Z",
                "updated_by": null
            }
        ]
    },
    {
        "id": "13bf4dc1-dac4-4037-a100-d8a6014a5b14",
        "name": "[1]Van",
        "default_price_per_km": 22,
        "seats": 12,
        "hourly_rate": 16,
        "automatic_acceptance": true,
        "use_default_ppk": false,
        "created_at": "2024-09-21T13:21:44.411Z",
        "updated_at": "2024-09-21T15:03:07.074Z",
        "updated_by": null,
        "cars": [
            {
                "name": "Toyota Corolla",
                "image": "https://upload.wikimedia.org/wikipedia/commons/thumb/c/c1/2016_Chrysler_300_Limited_AWD_front_4.22.19.jpg/1920px-2016_Chrysler_300_Limited_AWD_front_4.22.19.jpg",
                "brand": "Toyota ",
                "model": "Corolla",
                "allow_pets": true,
                "licence_plate": "MF 11 K",
                "bags_capacity": 11,
                "created_at": "2024-09-21T13:22:58.153Z",
                "updated_at": "2024-09-21T13:22:58.153Z",
                "updated_by": null
            }
        ],
        "bags": [
            {
                "quantity": 2,
                "created_at": "2024-09-21T15:03:07.233Z",
                "updated_at": "2024-09-21T15:03:07.233Z",
                "bag": {
                    "id": "d4b88fd0-889a-4363-a4fb-4fda40c253dd",
                    "name": "Satchel",
                    "description": "",
                    "created_at": "2024-09-21T13:19:27.358Z",
                    "updated_at": "2024-09-21T13:19:27.358Z"
                }
            },
            {
                "quantity": 4,
                "created_at": "2024-09-21T15:03:07.433Z",
                "updated_at": "2024-09-21T15:03:07.433Z",
                "bag": {
                    "id": "027d235e-4658-4104-8c5f-197f83cfc716",
                    "name": "Very Big Bag",
                    "description": "",
                    "created_at": "2024-09-21T13:19:35.037Z",
                    "updated_at": "2024-09-21T13:19:41.656Z"
                }
            },
            {
                "quantity": 4,
                "created_at": "2024-09-21T15:03:07.333Z",
                "updated_at": "2024-09-21T15:03:07.333Z",
                "bag": {
                    "id": "71cbe9d6-5b52-48d6-936a-b29a4161c7f9",
                    "name": "Big Bag",
                    "description": "",
                    "created_at": "2024-09-21T13:19:31.418Z",
                    "updated_at": "2024-09-21T13:19:31.418Z"
                }
            }
        ],
        "transfer_prices": [
            {
                "name": "Range-1",
                "price_per_km": "0",
                "range_max_distance": 60,
                "fixed_charge_value": 50,
                "created_at": "2024-09-21T15:03:07.534Z",
                "updated_at": "2024-09-21T15:03:07.534Z",
                "updated_by": null
            },
            {
                "name": "Range-2",
                "price_per_km": "0",
                "range_max_distance": 90,
                "fixed_charge_value": 40,
                "created_at": "2024-09-21T15:03:07.585Z",
                "updated_at": "2024-09-21T15:03:07.585Z",
                "updated_by": null
            },
            {
                "name": "Range-3",
                "price_per_km": "0",
                "range_max_distance": 120,
                "fixed_charge_value": 30,
                "created_at": "2024-09-21T15:03:07.635Z",
                "updated_at": "2024-09-21T15:03:07.635Z",
                "updated_by": null
            },
            {
                "name": "Range-4",
                "price_per_km": "5",
                "range_max_distance": 150,
                "fixed_charge_value": 20,
                "created_at": "2024-09-21T15:03:07.685Z",
                "updated_at": "2024-09-21T15:03:07.685Z",
                "updated_by": null
            },
            {
                "name": "Range-5",
                "price_per_km": "3",
                "range_max_distance": 180,
                "fixed_charge_value": 10,
                "created_at": "2024-09-21T15:03:07.745Z",
                "updated_at": "2024-09-21T15:03:07.745Z",
                "updated_by": null
            },
            {
                "name": "Range-6",
                "price_per_km": "2",
                "range_max_distance": 21000,
                "fixed_charge_value": 0,
                "created_at": "2024-09-21T15:03:07.796Z",
                "updated_at": "2024-09-21T15:03:07.796Z",
                "updated_by": null
            }
        ]
    },
    {
        "id": "9f12e350-2a74-4587-96e3-5a9ba5ebe926",
        "name": "[3]  Limo",
        "default_price_per_km": 15,
        "seats": 4,
        "hourly_rate": 15,
        "automatic_acceptance": true,
        "use_default_ppk": false,
        "created_at": "2024-09-21T13:21:01.894Z",
        "updated_at": "2024-09-21T15:03:19.002Z",
        "updated_by": null,
        "cars": [
            {
                "name": "Chrysler 300",
                "image": "https://www.usnews.com/object/image/0000018c-5f0b-dc6c-aded-ffbf2d960000/https-cars-dms-usnews-com-static-uploads-images-auto-custom-15294-original-2024-toyota-corolla-angular-front-1.jpg?update-time=1693333947000&size=responsiveGallery",
                "brand": "Chrysler ",
                "model": "Chrysler 300",
                "allow_pets": true,
                "licence_plate": "MF 12 K",
                "bags_capacity": 12,
                "created_at": "2024-09-21T13:22:15.751Z",
                "updated_at": "2024-09-21T13:22:15.751Z",
                "updated_by": null
            }
        ],
        "bags": [
            {
                "quantity": 6,
                "created_at": "2024-09-21T15:03:19.253Z",
                "updated_at": "2024-09-21T15:03:19.253Z",
                "bag": {
                    "id": "71cbe9d6-5b52-48d6-936a-b29a4161c7f9",
                    "name": "Big Bag",
                    "description": "",
                    "created_at": "2024-09-21T13:19:31.418Z",
                    "updated_at": "2024-09-21T13:19:31.418Z"
                }
            },
            {
                "quantity": 3,
                "created_at": "2024-09-21T15:03:19.153Z",
                "updated_at": "2024-09-21T15:03:19.153Z",
                "bag": {
                    "id": "d4b88fd0-889a-4363-a4fb-4fda40c253dd",
                    "name": "Satchel",
                    "description": "",
                    "created_at": "2024-09-21T13:19:27.358Z",
                    "updated_at": "2024-09-21T13:19:27.358Z"
                }
            }
        ],
        "transfer_prices": [
            {
                "name": "Range-1",
                "price_per_km": "0",
                "range_max_distance": 60,
                "fixed_charge_value": 50,
                "created_at": "2024-09-21T15:03:19.354Z",
                "updated_at": "2024-09-21T15:03:19.354Z",
                "updated_by": null
            },
            {
                "name": "Range-11",
                "price_per_km": "1",
                "range_max_distance": 36000,
                "fixed_charge_value": 0,
                "created_at": "2024-09-21T15:03:19.914Z",
                "updated_at": "2024-09-21T15:03:19.914Z",
                "updated_by": null
            },
            {
                "name": "Range-10",
                "price_per_km": "2",
                "range_max_distance": 330,
                "fixed_charge_value": 0,
                "created_at": "2024-09-21T15:03:19.852Z",
                "updated_at": "2024-09-21T15:03:19.852Z",
                "updated_by": null
            },
            {
                "name": "Range-9",
                "price_per_km": "2",
                "range_max_distance": 300,
                "fixed_charge_value": 0,
                "created_at": "2024-09-21T15:03:19.800Z",
                "updated_at": "2024-09-21T15:03:19.800Z",
                "updated_by": null
            },
            {
                "name": "Range-8",
                "price_per_km": "3",
                "range_max_distance": 270,
                "fixed_charge_value": 0,
                "created_at": "2024-09-21T15:03:19.743Z",
                "updated_at": "2024-09-21T15:03:19.743Z",
                "updated_by": null
            },
            {
                "name": "Range-7",
                "price_per_km": "4",
                "range_max_distance": 240,
                "fixed_charge_value": 0,
                "created_at": "2024-09-21T15:03:19.689Z",
                "updated_at": "2024-09-21T15:03:19.689Z",
                "updated_by": null
            },
            {
                "name": "Range-6",
                "price_per_km": "0",
                "range_max_distance": 210,
                "fixed_charge_value": 0,
                "created_at": "2024-09-21T15:03:19.632Z",
                "updated_at": "2024-09-21T15:03:19.632Z",
                "updated_by": null
            },
            {
                "name": "Range-5",
                "price_per_km": "0",
                "range_max_distance": 180,
                "fixed_charge_value": 0,
                "created_at": "2024-09-21T15:03:19.580Z",
                "updated_at": "2024-09-21T15:03:19.580Z",
                "updated_by": null
            },
            {
                "name": "Range-4",
                "price_per_km": "3",
                "range_max_distance": 150,
                "fixed_charge_value": 0,
                "created_at": "2024-09-21T15:03:19.528Z",
                "updated_at": "2024-09-21T15:03:19.528Z",
                "updated_by": null
            },
            {
                "name": "Range-3",
                "price_per_km": "4",
                "range_max_distance": 120,
                "fixed_charge_value": 10,
                "created_at": "2024-09-21T15:03:19.467Z",
                "updated_at": "2024-09-21T15:03:19.467Z",
                "updated_by": null
            },
            {
                "name": "Range-2",
                "price_per_km": "0",
                "range_max_distance": 90,
                "fixed_charge_value": 40,
                "created_at": "2024-09-21T15:03:19.405Z",
                "updated_at": "2024-09-21T15:03:19.405Z",
                "updated_by": null
            }
        ]
    }
];