'use client';

import ScheduleTable from "./table";
import {routes} from "@/config/routes";
import {usePaginatedBookings} from "@/services/query/booking/admin/usePaginatedBookings";
import BookingsStats from '@/app/shared/lib/appointment/appointment-list/stats';
import BookingsListPageHeader from "@/app/admin/bookings/list/page-header";
import React, {useState} from "react";
import {useGetMonthlyBookingStatistics} from "@/services/query/booking/admin/useGetMonthlyBookingStatistics";
import {Loader} from "rizzui";

const pageHeader = {
    title: 'Collapsible Table',
    breadcrumb: [
        {
            href: routes.dashboard,
            name: 'Home',
        },
        {
            name: 'Tables',
        },
        {
            name: 'Collapsible',
        },
    ],
};

export default function BookingsPage() {
    const [currentPage, setCurrentPage] = useState(1);
    const [currentLimit, setCurrentLimit] = useState(10);
    const [filters, setFilters] = useState();

    const {
        isPending,
        isError,
        error,
        data,
        isFetching,
        isPlaceholderData,
        refetch,
        status,
    } = usePaginatedBookings(currentPage, currentLimit, filters);

    const {
        isPending: mbsIsPending,
        isError: mbsIsError,
        error: mbsError,
        data: monthlyStats,
        isFetching: mbsIsFetching,
        isPlaceholderData: mbsIsPlaceHolderData,
        refetch: mbsRefetch,
        status: mbsStatus,
    } = useGetMonthlyBookingStatistics();

    const onSelectedPage = (page: number) => {
        setCurrentPage(page);
    }

    const onPageLimitChange = (limit: number) => {
        setCurrentLimit(limit);
    }

    const onFiltersChanged = (filters: any) => {
        setFilters(filters);
        refetch();
    }

    return <>
        <BookingsListPageHeader />

        <div className="flex flex-col gap-10 @container">
            <BookingsStats monthlyStats={monthlyStats} />

            {
                data ?
                    <ScheduleTable
                        initialData={data}
                        onSelectedPage={onSelectedPage}
                        onPageLimitChange={onPageLimitChange}
                        onFiltersChanged={onFiltersChanged}
                        variant="elegant"
                        className="[&_.table-filter]:hidden [&_.table-pagination]:hidden"
                    /> : <div className="grid h-32 flex-grow place-content-center items-center">
                        <Loader size="sm"/>
                    </div>
            }
        </div>
    </>;
}
