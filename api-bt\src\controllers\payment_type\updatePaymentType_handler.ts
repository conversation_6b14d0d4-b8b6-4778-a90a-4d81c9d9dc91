import { Request, Response } from 'express';
import {
    updatePaymentType,
} from '../../services/payment-type'; // Adjust the import path as necessary

export const updatePaymentTypeHandler = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        const updateData = req.body;
        const result = await updatePaymentType(id, updateData);
        if (!result.success) {
            return res.status(404).json(result);
        }
        return res.json(result);
    } catch (error: any) {
        return res.status(500).json({
            success: false,
            message: 'Failed to update payment type',
            error: error.message,
        });
    }
};
