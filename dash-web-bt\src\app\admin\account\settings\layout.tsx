import PageHeader from '@/app/shared/lib/page-header';
import ProfileSettingsNav from '@/app/shared/app/account/settings/navigation';

const pageHeader = {
  title: 'Settings',
  breadcrumb: [
    {
      href: '/',
      name: 'Home',
    },
    {
      name: '<PERSON><PERSON><PERSON>',
    },
  ],
};

export default function ProfileSettingsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      <PageHeader title={pageHeader.title} breadcrumb={pageHeader.breadcrumb} />
      <ProfileSettingsNav />
      {children}
    </>
  );
}
