import { getRepository } from 'typeorm';
import { PaymentType } from '../../models/PaymentType'; // Adjust the path as necessary

export const createPaymentType = async (data: Partial<PaymentType>) => {
  const paymentTypeRepository = getRepository(PaymentType);
  try {
    const newPaymentType = paymentTypeRepository.create(data);
    await paymentTypeRepository.save(newPaymentType);
    return { success: true, data: newPaymentType, error: null };
  } catch (error: any) {
    return { success: false, data: null, error: error.message };
  }
};
