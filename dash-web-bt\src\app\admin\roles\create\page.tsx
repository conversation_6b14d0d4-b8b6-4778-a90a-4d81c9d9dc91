import { routes } from '@/config/routes';
import PageHeader from '@/app/shared/lib/page-header';
import { metaObject } from '@/config/site.config';
import CreateEdit from '@/app/shared/app/roles/create-edit';

export const metadata = {
  ...metaObject('Create Role'),
};

const pageHeader = {
  title: 'Create Role',
  breadcrumb: [
    {
      href: routes.dashboard,
      name: 'Dashboard',
    },
    {
      href: routes.roles,
      name: 'Roles',
    },
    {
      name: 'Create Role',
    },
  ],
};

export default function CreateShipmentPage() {
  return (
    <>
      <PageHeader title={pageHeader.title} breadcrumb={pageHeader.breadcrumb}>
      </PageHeader>

      <CreateEdit />
    </>
  );
}
