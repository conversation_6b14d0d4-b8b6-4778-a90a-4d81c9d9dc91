import { Request, Response } from 'express';
import {
  createUser,
} from '../../services/user';

export const createUser_handler = async (req: Request, res: Response) => {
  try {
    const data = req.body;
    const result = await createUser(data);
    if (!result.success) {
      return res.status(200).json(result);
    }
    return res.json(result);
  } catch (error: any) {
    return res.status(500).json({
      success: false,
      message: 'Failed to create user',
      errorType: 'InternalServerError',
      data: undefined,
    });
  }
};
