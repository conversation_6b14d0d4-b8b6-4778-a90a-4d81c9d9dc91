import {MigrationInterface, QueryRunner, TableColumn} from "typeorm";

export class changedValueFieldsToDecimal1711745713717 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Updating fields in the `payment` table
        await queryRunner.changeColumn('payment', 'amount',
            new TableColumn({
                name: 'amount',
                type: 'decimal',
                precision: 10,
                scale: 2,
            })
        );

        await queryRunner.changeColumn('payment', 'amount_paid',
            new TableColumn({
                name: 'amount_paid',
                type: 'decimal',
                precision: 10,
                scale: 2,
                isNullable: true,
            })
        );

        await queryRunner.changeColumn('payment', 'tip',
            new TableColumn({
                name: 'tip',
                type: 'decimal',
                precision: 10,
                scale: 2,
                isNullable: true,
            })
        );

        // Updating fields in the `booking` table
        await queryRunner.changeColumn('booking', 'recommended_amount_to_pay',
            new TableColumn({
                name: 'recommended_amount_to_pay',
                type: 'decimal',
                precision: 10,
                scale: 2,
            })
        );

        await queryRunner.changeColumn('booking', 'final_amount_to_pay',
            new TableColumn({
                name: 'final_amount_to_pay',
                type: 'decimal',
                precision: 10,
                scale: 2,
            })
        );

        // Include similar changes for any other fields and tables as per your requirements
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Reverting changes in the `payment` table
        await queryRunner.changeColumn('payment', 'amount',
            new TableColumn({
                name: 'amount',
                type: 'int',
            })
        );

        await queryRunner.changeColumn('payment', 'amount_paid',
            new TableColumn({
                name: 'amount_paid',
                type: 'int',
                isNullable: true,
            })
        );

        // Reverting changes in the `booking` table
        await queryRunner.changeColumn('booking', 'recommended_amount_to_pay',
            new TableColumn({
                name: 'recommended_amount_to_pay',
                type: 'int',
            })
        );

        await queryRunner.changeColumn('booking', 'final_amount_to_pay',
            new TableColumn({
                name: 'final_amount_to_pay',
                type: 'int',
            })
        );

        await queryRunner.changeColumn('booking', 'tip',
            new TableColumn({
                name: 'tip',
                type: 'int',
            })
        );
    }

}
