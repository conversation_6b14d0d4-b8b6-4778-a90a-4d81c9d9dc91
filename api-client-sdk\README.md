# API Client SDK

A comprehensive TypeScript client SDK for the API service with modular architecture and clean separation of concerns.

## Features

- **Core API Client**: Clean and simple HTTP client using Axios.
- **Zod Validation**: Type-safe API responses and requests.
- **Modular Architecture**: Separation of concerns with independent layers.
- **Service Layer**: Encapsulated business logic.
- **TanStack Query Integration**: React Query hooks for data fetching.
- **State Management**: MobX and MobX State Tree implementation.
- **React Integration**: Hooks and providers for React applications.
- **TypeScript Support**: Full type safety across the library.

## Installation

```bash
npm install api-client-sdk
# or
yarn add api-client-sdk
```

## Usage

### Basic Usage

```typescript
import { configureApi, authService } from 'api-client-sdk';

// Configure the API client
configureApi({
  baseUrl: 'https://api.example.com',
});

// Login
const login = async () => {
  const response = await authService.login({
    email: '<EMAIL>',
    password: 'password',
  });
  
  if (response.success) {
    console.log('Logged in successfully!', response.user);
  } else {
    console.error('Login failed:', response.msg);
  }
};
```

### React Usage

```tsx
import React from 'react';
import { ApiProvider, useAuth, useUsers } from 'api-client-sdk';

function App() {
  return (
    <ApiProvider>
      <UserList />
      <LoginForm />
    </ApiProvider>
  );
}

function LoginForm() {
  const { login, isLoading, error } = useAuth();
  const [email, setEmail] = React.useState('');
  const [password, setPassword] = React.useState('');
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    await login({ email, password });
  };
  
  return (
    <form onSubmit={handleSubmit}>
      {error && <div className="error">{error}</div>}
      <input
        type="email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        placeholder="Email"
      />
      <input
        type="password"
        value={password}
        onChange={(e) => setPassword(e.target.value)}
        placeholder="Password"
      />
      <button type="submit" disabled={isLoading}>
        {isLoading ? 'Logging in...' : 'Login'}
      </button>
    </form>
  );
}

function UserList() {
  const { users, fetchUsers, isLoading } = useUsers();
  
  React.useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);
  
  if (isLoading) return <div>Loading users...</div>;
  
  return (
    <div>
      <h2>Users</h2>
      <ul>
        {users.map((user) => (
          <li key={user.id}>{user.fullName}</li>
        ))}
      </ul>
    </div>
  );
}
```

### TanStack Query Integration

```tsx
import React from 'react';
import { QueryClientProvider, QueryClient } from 'react-query';
import { useUsers as useUsersQuery } from 'api-client-sdk';

const queryClient = new QueryClient();

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <UserList />
    </QueryClientProvider>
  );
}

function UserList() {
  const { data, isLoading, error } = useUsersQuery();
  
  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  
  return (
    <div>
      <h2>Users</h2>
      <ul>
        {data?.data?.data.map((user) => (
          <li key={user.id}>{user.fullName}</li>
        ))}
      </ul>
    </div>
  );
}
```

## Architecture

The SDK is organized into several layers:

1. **Core Layer**: Base API client, configuration, and types.
2. **Models Layer**: Data models with Zod validation.
3. **Services Layer**: Business logic and API operations.
4. **State Layer**: MobX and MST state management.
5. **TanStack Query Layer**: React Query hooks.
6. **React Layer**: React components and hooks.

## License

MIT 