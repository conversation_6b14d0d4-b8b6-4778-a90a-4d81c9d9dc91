import { Request, Response } from 'express';
import {
    createPayment,
} from '../../services/payment'; // Adjust the import path as necessary

export const createPaymentHandler = async (req: Request, res: Response) => {
    try {
        const data = req.body;
        const result = await createPayment(data);
        if (!result.success) {
            return res.status(400).json(result);
        }
        return res.status(201).json(result);
    } catch (error: any) {
        return res.status(500).json({
            success: false,
            message: 'Failed to create payment',
            error: error.message,
        });
    }
};
