import { Request, Response } from 'express';
import { updateCustomer } from '../../services/customer';

export const updateCustomer_handler = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    const result = await updateCustomer(id, updateData);
    return res.json(result);
  } catch (error: any) {
    return res.status(500).json({ error: error.message });
  }
};
