import express from 'express';

import {
  createCarType_Bag_bulk_handler,
  createCarType_Bag_handler,
  deleteCarType_Bag_handler,
  updateCarType_Bag_handler,
  getCarType_Bag_handler,
  getAllCarType_Bags_handler,
} from '../../controllers/car-type-bag'; // Adjust the path as necessary

export const carTypeBagRouter = express.Router();

carTypeBagRouter.post('/car-type-bags', createCarType_Bag_handler);
carTypeBagRouter.post('/car-type-bags/bulk', createCarType_Bag_bulk_handler);
carTypeBagRouter.delete('/car-type-bags/:id', deleteCarType_Bag_handler);
carTypeBagRouter.get('/car-type-bags', getAllCarType_Bags_handler);
carTypeBagRouter.get('/car-type-bags/:id', getCarType_Bag_handler);
carTypeBagRouter.put('/car-type-bags/:id', updateCarType_Bag_handler);
