import { routes } from '@/config/routes';
import {useRouter} from "next/navigation";
import { Title, Text } from 'rizzui';

export default function ExpandedRow({ drivers, type }: any) {
  const router = useRouter();

  // car type is an object with the following shape
  // {
  //   bags: []
  //   created_at: "2024-04-28T18:40:43.645Z"
  //   default_price_per_km: 8
  //   hourly_rate: 22
  //   id: "8e0efbcf-bfd1-418f-96ad-b6c13b2d4e5e"
  //   name: "Standard"
  //   seats: 4
  // }
  const goToCarType = () => {
    router.push(routes.carTypeDetails(type.id));
  };
  return (
    <div>
      {
        type && (
          <article className="flex items-center justify-between py-2 first-of-type:pt-2.5 last-of-type:pb-2.5">
            <div className="flex items-start">
              <header>
                <Title as="h4" className="text-sm font-medium">
                  <a href={routes.carTypeDetails(type.id)}>
                    Car Type:  {type.name}
                  </a>
                </Title>
                <Text className="text-xs text-gray-500">Hourly rate: {type.hourly_rate}</Text>
                <Text className="text-xs text-gray-500">Default price per km: {type.default_price_per_km}</Text>
                <Text className="text-xs text-gray-500">Seats: {type.seats}</Text>
              </header>
            </div>
          </article>
        )
      }
      {
        drivers && drivers.length > 0 && (
          <>
            <Title as="h4" className="text-sm font-medium">Drivers</Title>
            {drivers.map((driver: any) => (
              <article
                key={driver.id}
                className="flex items-center justify-between py-2 first-of-type:pt-2.5 last-of-type:pb-2.5"
              >
                <div className="flex items-start">
                  <header>
                    <Title className="text-xs text-gray-500">
                      <a href={routes.driverEdit(driver.id)}>{driver.first_name} {driver.last_name}</a>
                    </Title>
                    {/* Uncomment and modify the following line if you want to display additional driver info */}
                    {/* <Text className="text-xs text-gray-500">Phone number: {driver.phone_number}</Text> */}
                  </header>
                </div>
              </article>
            ))}
          </>
        )
      }
  </div>
  );
}
