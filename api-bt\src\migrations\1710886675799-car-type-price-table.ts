import {MigrationInterface, QueryRunner, Table} from "typeorm";

export class carTypePriceTable1710886675799 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(
            new Table({
                name: 'car_type_price',
                columns: [
                    {
                        name: 'id',
                        type: 'uuid',
                        isPrimary: true,
                        isGenerated: true,
                        generationStrategy: 'uuid',
                    },
                    {
                        name: 'name',
                        type: 'varchar',
                        length: '50',
                    },
                    {
                        name: 'price_per_km',
                        type: 'decimal',
                        precision: 10, // Adjust precision as needed
                        scale: 2, // Adjust scale as needed
                    },
                    {
                        name: 'created_at',
                        type: 'timestamp',
                        default: 'now()',
                    },
                    {
                        name: 'updated_at',
                        type: 'timestamp',
                        default: 'now()',
                    },
                    {
                        name: 'updated_by',
                        type: 'int',
                        isNullable: true,
                    },
                ],
            }),
            true,
        );

        // If there's a relationship to be established, create foreign keys accordingly
        // Example for a foreign key:
        // await queryRunner.createForeignKey('car_type_price', new TableForeignKey({
        //   columnNames: ['your_foreign_key_column'],
        //   referencedTableName: 'the_referenced_table',
        //   referencedColumnNames: ['id_in_referenced_table'],
        // }));
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropTable('car_type_price');
    }

}
