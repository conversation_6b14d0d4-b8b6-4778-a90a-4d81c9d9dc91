import { getRepository } from 'typeorm';
import { Driver } from '../../models/Driver';
import { Car } from '../../models/Car';
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const updateDriver = async (id: string, updateData: Partial<Driver>) => {
  const driverRepository = getRepository(Driver);
  const carRepository = getRepository(Car);

  try {
    // If updateData contains car ID, handle the association
    if (updateData.car && updateData.car.id) {
      const car = await carRepository.findOne(updateData.car.id);
      if (!car) {
        return { success: false, data: null, error: 'Car not found' };
      }
      updateData.car = car;
    }

    const updateResult = await driverRepository.update(id, updateData);
    if (updateResult.affected === 0) {
      return { success: false, data: null, error: 'Driver not found' };
    }

    const updatedDriver = await driverRepository.findOne(id, { relations: ["car"] });
    return { success: true, data: updatedDriver, error: null };
  } catch (error) {
    const interpretedError = interpretDatabaseError(error);
    return { success: false, data: null, error: interpretedError };
  }
};