import {MigrationInterface, QueryRunner, Table, TableForeignKey} from "typeorm";

export class bookingBag1710110225759 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(
            new Table({
                name: 'booking_bag',
                columns: [
                    {
                        name: 'id',
                        type: 'uuid',
                        isPrimary: true,
                        isGenerated: true,
                        generationStrategy: 'uuid',
                    },
                    {
                        name: 'bag_id',
                        type: 'uuid',
                    },
                    {
                        name: 'booking_id',
                        type: 'uuid',
                    },
                    {
                        name: 'quantity',
                        type: 'int',
                    },
                    {
                        name: 'created_at',
                        type: 'timestamp',
                        default: 'CURRENT_TIMESTAMP',
                    },
                    {
                        name: 'updated_at',
                        type: 'timestamp',
                        default: 'CURRENT_TIMESTAMP',
                    },
                ],
            }),
            true,
        );

        await queryRunner.createForeignKey(
            'booking_bag',
            new TableForeignKey({
                columnNames: ['bag_id'],
                referencedTableName: 'bag',
                referencedColumnNames: ['id'],
                onDelete: 'CASCADE',
            }),
        );

        await queryRunner.createForeignKey(
            'booking_bag',
            new TableForeignKey({
                columnNames: ['booking_id'],
                referencedTableName: 'booking',
                referencedColumnNames: ['id'],
                onDelete: 'CASCADE',
            }),
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropTable('booking_bag');
    }

}
