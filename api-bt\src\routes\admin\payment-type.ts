import express from 'express';
import {
    deletePaymentTypeHand<PERSON>,
    getAllPaymentTypesHandler,
    getPaymentTypeHandler,
    updatePaymentTypeHandler,
    createPaymentTypeHandler,

} from '../../controllers/payment_type';

export const paymentTypeRouter = express.Router();

paymentTypeRouter.post('/payment-types', createPaymentTypeHandler);
paymentTypeRouter.get('/payment-types', getAllPaymentTypesHandler);
paymentTypeRouter.get('/payment-types/:id', getPaymentTypeHandler);
paymentTypeRouter.put('/payment-types/:id', updatePaymentTypeHandler);
paymentTypeRouter.delete('/payment-types/:id', deletePaymentTypeHandler);
