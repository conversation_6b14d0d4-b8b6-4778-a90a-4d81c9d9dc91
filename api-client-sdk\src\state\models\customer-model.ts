import { types, Instance, SnapshotIn } from 'mobx-state-tree';

/**
 * Customer model in MST
 */
export const CustomerModel = types.model('CustomerModel', {
  id: types.identifier,
  first_name: types.string,
  last_name: types.string,
  email: types.string,
  phone: types.string,
  country_code: types.maybeNull(types.string),
  country: types.maybeNull(types.string),
  city: types.maybeNull(types.string),
  address: types.maybeNull(types.string),
  special_instructions: types.maybeNull(types.string),
  created_at: types.string, 
  updated_at: types.string, 
  updated_by: types.maybeNull(types.number),
})
.views(self => ({
  /**
   * Get full name
   */
  get fullName(): string {
    return `${self.first_name} ${self.last_name}`;
  },
  
  /**
   * Get created date as Date object
   */
  get createdDate(): Date {
    return new Date(self.created_at);
  },
  
  /**
   * Get updated date as Date object
   */
  get updatedDate(): Date {
    return new Date(self.updated_at);
  },
  
  /**
   * Get full address
   */
  get fullAddress(): string {
    const parts = [];
    if (self.address) parts.push(self.address);
    if (self.city) parts.push(self.city);
    if (self.country) parts.push(self.country);
    
    return parts.join(', ') || 'No address provided';
  },
  
  /**
   * Get formatted phone with country code
   */
  get formattedPhone(): string {
    return self.country_code ? `${self.country_code} ${self.phone}` : self.phone;
  }
}));

// Type definitions for TypeScript
export interface Customer extends Instance<typeof CustomerModel> {}
export interface CustomerSnapshotIn extends SnapshotIn<typeof CustomerModel> {} 