import {Controller, useFormContext} from 'react-hook-form';
import FormGroup from '@/app/shared/lib/form-group';
import {Input, Button, RadioGroup, AdvancedRadio, Select} from 'rizzui';
import cn from '@/utils/class-names';
import {PiCheckCircleFill} from "react-icons/pi";
import React, {useEffect, useState} from "react";
import {carTypeService} from "@/services/api/admin";
// import {usePaginatedPaymentTypes} from "@/services/query/payment-type/usePaginatedPaymentTypes";

interface FormRecipientInfoProps {
    className?: string;
}

function findBestMatch(prices: any[], distance: number) {
    let bestMatch = null;

    for (const price of prices) {
        // Convert price_per_km to a number, assuming it's stored as a string.
        price.price_per_km = Number(price.price_per_km);
        price.max_distance = Number(price.max_distance);
        price.min_distance = Number(price.min_distance);

        if (price.min_distance <= distance && price.max_distance >= distance) {
            // If exactly matches the criteria, return immediately.
            return price;
        } else if (distance > price.max_distance) {
            // If the distance is greater than max_distance, look for the option with the highest max_distance.
            if (!bestMatch || price.max_distance > bestMatch.max_distance) {
                bestMatch = price;
            }
        }
    }

    // Return the best match found, or null if no suitable match was found.
    return bestMatch;
}

export default function PaymentDetails({
                                           className,
                                       }: FormRecipientInfoProps) {
    const {
        register,
        control,
        formState: {errors},
        setValue,
        getValues,
        watch,
    } = useFormContext();

    // paymentTypes

    // const {
    //     isPending,
    //     isError,
    //     error,
    //     data: paymentTypes,
    //     isFetching,
    //     isPlaceholderData,
    //     refetch,
    //     status,
    // } = usePaginatedPaymentTypes(1, 30);

    const [customerPaymentValue, setCustomerPaymentValue] = useState(0);

    const trip_distance = watch('trip_distance');
    const return_trip_distance = watch('return_trip_distance');
    const estimated_service_hours = watch('estimated_service_hours');

    const tripType = watch('trip_type');
    const carType = watch('car_type');
    const oneWayTrip = watch('one_way_trip');

    const final_amount_to_pay = watch('final_amount_to_pay');

    useEffect(() => {
        computeRecommendedPrice();
    }, [estimated_service_hours, return_trip_distance, trip_distance, tripType, carType, oneWayTrip]);

    useEffect(() => {
        const val: number = (Number(final_amount_to_pay) * 0.25);
        setCustomerPaymentValue(val);
    }, [final_amount_to_pay]);
    function computeRecommendedPrice() {
        if (carType) {
            try {
                const carTypeId = typeof carType === 'string' ? carType : carType.id;
                carTypeService.getCarType(carTypeId).then((data) => {
                    if (tripType === 'transfer' && data.data.transfer_prices) {
                        // compute recommended amount to pay and final amount to pay based on trip distance and return trip distance
                        const matchedPrice = findBestMatch(data.data.transfer_prices, Number(trip_distance));
                        let final_amount_to_pay;
                        if(matchedPrice) {
                            final_amount_to_pay = Number(trip_distance) * Number(matchedPrice.price_per_km);

                            if (!oneWayTrip) {
                                final_amount_to_pay = (Number(trip_distance) + Number(return_trip_distance)) * Number(matchedPrice.price_per_km);
                            }
                        } else {
                            final_amount_to_pay = Number(data.data.default_price_per_km)
                        }

                        setValue('recommended_amount_to_pay', final_amount_to_pay.toFixed(2));
                        setValue('final_amount_to_pay', final_amount_to_pay.toFixed(2));
                    }

                    if (tripType === 'hourly' && data.data.hourly_rate && estimated_service_hours) {
                        // compute recommended amount to pay and final amount to pay based on estimated_service_hours times hourly rate
                        const recommended_amount_to_pay = estimated_service_hours * data.data.hourly_rate;

                        setValue('recommended_amount_to_pay', recommended_amount_to_pay.toFixed(2));
                        setValue('final_amount_to_pay', recommended_amount_to_pay.toFixed(2));

                    }
                })
            } catch (e) {
                console.error(e);
            }
        }
    }

    return (
        <FormGroup
            title="Payment Info"
            description="Payment information here"
            className={cn(className)}
        >
            <Input
                step={0.01}
                label="Recommended Amount to Pay (€)"
                placeholder="ie: 1.25"
                disabled={true}
                type={'number'}
                labelClassName="font-medium text-gray-900"
                {...register('recommended_amount_to_pay', {valueAsNumber: true})}
                error={errors.recommended_amount_to_pay?.message as string}
            />

            <Input
                disabled={true}
                step={0.01}
                label="Final Amount to Pay (€)"
                placeholder="ie: 1.25"
                type={'number'}
                labelClassName="font-medium text-gray-900"
                {...register('final_amount_to_pay', {valueAsNumber: true})}
                error={errors.final_amount_to_pay?.message as string}
            />

            <Controller
                name="partial_payment"
                control={control}
                render={({field: {value, onChange}}) => (
                    <RadioGroup
                        value={value}
                        setValue={(d) => {
                            onChange(d);
                        }}
                        className="col-span-full grid gap-4 @lg:grid-cols-2"
                    >
                        <AdvancedRadio
                            disabled={true}
                            value="full"
                            className=" [&_.rizzui-advanced-checkbox]:!px-5 [&_.rizzui-advanced-checkbox]:!py-4"
                            inputClassName="[&~span]:border-0 [&~span]:ring-1 [&~span]:ring-gray-200 [&~span:hover]:ring-primary [&:checked~span:hover]:ring-primary [&:checked~span]:border-1 [&:checked~.rizzui-advanced-checkbox]:ring-2 [&~span_.icon]:opacity-0 [&:checked~span_.icon]:opacity-100"
                        >
                            <div className="flex justify-between">
                                <span className="font-medium text-gray-900">Full payment</span>
                                <PiCheckCircleFill className="icon h-5 w-5 text-primary"/>
                            </div>
                            <p className="text-gray-500">The customer paid in full.</p>
                        </AdvancedRadio>
                        <AdvancedRadio
                            disabled={true}
                            value="partial"
                            className=" [&_.rizzui-advanced-checkbox]:!px-5 [&_.rizzui-advanced-checkbox]:!py-4"
                            inputClassName="[&~span]:border-0 [&~span]:ring-1 [&~span]:ring-gray-200 [&~span:hover]:ring-primary [&:checked~span:hover]:ring-primary [&:checked~span]:border-1 [&:checked~.rizzui-advanced-checkbox]:ring-2 [&~span_.icon]:opacity-0 [&:checked~span_.icon]:opacity-100"
                        >
                            <div className="flex justify-between">
                                <span className="font-medium text-gray-900">Partial Payment</span>
                                <PiCheckCircleFill className="icon h-5 w-5 text-primary"/>
                            </div>
                            <p className="text-gray-500">Pay partially now (€{customerPaymentValue}).</p>
                        </AdvancedRadio>
                    </RadioGroup>
                )}
            />

            {/*{*/}
            {/*    paymentTypes && paymentTypes?.data.length ? (*/}
            {/*        <NoSSR>*/}
            {/*            <Controller*/}
            {/*                control={control}*/}
            {/*                name="payment.type"*/}
            {/*                render={({field: {value, onChange}}) => {*/}
            {/*                    console.log('value!!!!!!!!!!', value)*/}
            {/*                    return (*/}
            {/*                        <Select*/}
            {/*                            label="Payment Type"*/}
            {/*                            className=""*/}
            {/*                            labelClassName="text-gray-900"*/}
            {/*                            dropdownClassName="p-2 gap-1 grid !z-10"*/}
            {/*                            inPortal={false}*/}
            {/*                            value={value?.payment?.type?.id}*/}
            {/*                            onChange={onChange}*/}
            {/*                            options={paymentTypes.data.map((paymentType) => ({*/}
            {/*                                value: paymentType.id,*/}
            {/*                                label: paymentType.name,*/}
            {/*                            }))}*/}
            {/*                            getOptionValue={(option) => option.value}*/}
            {/*                            displayValue={(selected: string) =>*/}
            {/*                                paymentTypes?.data.find((c) => c.id === selected)?.name ?? ''*/}
            {/*                            }*/}
            {/*                            error={errors?.payment_type?.message as string}*/}
            {/*                        />*/}
            {/*                    )*/}
            {/*                }}*/}
            {/*            />*/}
            {/*        </NoSSR>*/}
            {/*    ) : null*/}
            {/*}*/}

            <Input
                step={0.01}
                label="Tip Given (€)"
                placeholder="ie: 1.25"
                type={'number'}
                labelClassName="font-medium text-gray-900"
                {...register('tip', {valueAsNumber: true})}
                error={errors.tip?.message as string}
            />

        </FormGroup>
    );
}
