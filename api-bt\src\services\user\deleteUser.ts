import { getRepository } from 'typeorm';
import { User } from '../../models/User';
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const deleteUser = async (id: string) => {
  const userRepository = getRepository(User);

  try {
    // Delete the driver
    // force remove cascade active sessions
    const deleteResult = await userRepository.delete(id);
    if (deleteResult.affected === 0) {
      return { success: false, data: null, error: 'User not found' };
    }
    return { success: true, data: { id }, error: null };
  } catch (error) {
    const interpretedError = interpretDatabaseError(error);
    return { success: false, data: null, error: interpretedError };
  }
};