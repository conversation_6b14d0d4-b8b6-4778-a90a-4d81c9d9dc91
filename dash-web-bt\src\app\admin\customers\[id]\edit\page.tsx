"use client";

import { routes } from '@/config/routes';
import PageHeader from '@/app/shared/lib/page-header';
import ImportButton from '@/app/shared/lib/import-button';
import CreateEdit from '@/app/shared/app/customers/create-edit';
import {useGetCustomer} from "@/services/query/customer/useGetCustomer";
import {useEffect} from "react";

const pageHeader = {
  title: 'Edit Customer',
  breadcrumb: [
    {
      href: routes.dashboard,
      name: 'Dashboard',
    },
    {
      href: routes.customers,
      name: 'Customers',
    },
    {
      name: 'Edit Customer',
    },
  ],
};

export default function EditCustomersPage({
  params,
}: {
  params: { id: string };
}) {

  const id = params.id;

  const {
    isPending,
    isError,
    error,
    data,
    isFetching,
    isPlaceholderData,
    refetch,
    status,
  } = useGetCustomer(id);

  return (
    <>
      <PageHeader title={pageHeader.title} breadcrumb={pageHeader.breadcrumb}>
        {/*<ImportButton title={'Import File'} />*/}
      </PageHeader>

      {
        data?.data ? <CreateEdit id={id} customer={data?.data} /> : null
      }

    </>
  );
}
