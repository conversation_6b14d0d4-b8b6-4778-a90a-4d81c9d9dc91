'use client';

import BudgetStatus from './budget-status';
import FinancialStats from './transaction-states';
import TotalStatistics from './total-statistics';
import TransactionHistoryTable from './stripe-payments-history';
import {useGetMonthlyBookingStatistics} from "@/services/query/booking/admin/useGetMonthlyBookingStatistics";
import React, {useState} from "react";
import {usePaginatedCustomers} from "@/services/query/customer/usePaginatedCustomers";
import {usePaginatedStripePayments} from "@/services/query/payment/usePaginatedStripePayments";
import {Loader} from "rizzui";

export default function FinancialDashboard() {

    const {
        // isPending,
        // isError,
        // error,
        data: monthlyStats,
        // isFetching,
        // isPlaceholderData,
        // refetch,
        // status,
    } = useGetMonthlyBookingStatistics();

    const [currentPage, setCurrentPage] = useState(1);
    const [currentLimit, setCurrentLimit] = useState(10);
    const [startingAfter, setStartingAfter] = useState(null);

    const {
        isPending,
        isError,
        error,
        data,
        isFetching,
        isPlaceholderData,
        refetch,
        status,
    } = usePaginatedStripePayments(currentPage, currentLimit, startingAfter);

    const onSelectedPage = (page: number) => {
        setCurrentPage(page);
    }

    const onPageLimitChange = (limit: number) => {
        setCurrentLimit(limit);
    }

    // console.log('datadatadata', data)

    return (
        <div className="grid grid-cols-6 gap-6 @container">
            <FinancialStats monthlyStats={monthlyStats} className="col-span-full"/>
            {
                data ? (
                    <TransactionHistoryTable
                        initialData={data}
                        onSelectedPage={onSelectedPage}
                        onPageLimitChange={onPageLimitChange}
                        variant="elegant"
                        className="col-span-full"
                    />
                ) : 
                <div className="flex items-center justify-center h-32 col-span-full">
                    <Loader size="lg"/>
                </div>
            }
            {/* <TotalStatistics className="col-span-full @[90rem]:col-span-4 items-center"/> */}
            {/*<BudgetStatus className="col-span-full @[59rem]:col-span-3 @[90rem]:col-span-2"/>*/}
        </div>
    );
}
