import { MigrationInterface, QueryRunner, TableC<PERSON>umn, TableForeign<PERSON>ey } from "typeorm";

export class addedCarToBooking1723401583907 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        const table = await queryRunner.getTable('booking');

        // Check if the 'car_id' column already exists
        const carIdColumn = table!.findColumnByName('car_id');
        if (!carIdColumn) {
            // Add the 'car_id' column to the 'booking' table
            await queryRunner.addColumn('booking', new TableColumn({
                name: 'car_id',
                type: 'uuid',
                isNullable: true,
            }));

            // Add the foreign key constraint
            await queryRunner.createForeignKey('booking', new TableForeignKey({
                columnNames: ['car_id'],
                referencedColumnNames: ['id'],
                referencedTableName: 'car',
                onDelete: 'CASCADE',
            }));
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        const table = await queryRunner.getTable('booking');

        // Check if the 'car_id' column exists before attempting to drop it
        const carIdColumn = table!.findColumnByName('car_id');
        if (carIdColumn) {
            // Find the foreign key related to the 'car_id' column
            const foreignKey = table!.foreignKeys.find(fk => fk.columnNames.indexOf('car_id') !== -1);
            if (foreignKey) {
                // Drop the foreign key
                await queryRunner.dropForeignKey('booking', foreignKey);
            }

            // Remove the 'car_id' column from the 'booking' table
            await queryRunner.dropColumn('booking', 'car_id');
        }
    }
}
