'use client';

import React, { useState, useEffect, FormEvent } from 'react';
import Container from '@/app/(public)/(sections)/(old)/container';
import { MBButton } from "@/components/app/MovingBorder";
import { useCreateTicket } from '@/services/mutations/ticket/public/create';
import axios from 'axios';
import toast from 'react-hot-toast';
import { TicketSchemaType } from '@/utils/validators/app/entities';
import { useActiveBookingsSession } from "@/hooks/use-active-bookings-ls";
import { SearchableSelect } from "@/components/app/searchable-select";
import moment from "moment";
import { useI18n } from "@/hooks/use-translation";

interface FormData {
    name: string;
    email: string;
    phone: string;
    booking_code: string;
    message: string;
}

const ContactSection: React.FC = () => {
    const [formData, setFormData] = useState<FormData>({ name: '', email: '', phone: '', booking_code: '', message: '' });
    const [isLoading, setLoading] = useState(false);
    const [selectedActiveBooking, setSelectedActiveBooking] = useState<string | null>(null);
    const createTicket = useCreateTicket();
    const { t } = useI18n();

    const activeBookingsSession = useActiveBookingsSession();
    const currentActiveBookings = activeBookingsSession?.getActiveBookings();

    useEffect(() => {
        if (currentActiveBookings && currentActiveBookings.length > 0) {
            const mostRecentBooking = currentActiveBookings.reduce((prev: any, current: any) => {
                return moment(prev.created_at).isAfter(moment(current.created_at)) ? prev : current;
            });

            if (mostRecentBooking.booking_code !== selectedActiveBooking) {
                setSelectedActiveBooking(mostRecentBooking.booking_code);
                setFormData((prevData) => ({
                    ...prevData,
                    booking_code: mostRecentBooking.booking_code,
                }));
            }
        }
    }, [currentActiveBookings, selectedActiveBooking]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setFormData({ ...formData, [e.target.name]: e.target.value });
    };

    const send = (data: FormData) => {
        setLoading(true);
        const ticketData: TicketSchemaType & FormData = {
            message: data.message,
            name: data.name,
            phone: data.phone,
            email: data.email,
            booking_code: data.booking_code
        };
        createTicket.mutate({ payload: ticketData }, {
            onError: (error: any) => {
                console.log('error', error)
                if (axios.isAxiosError(error) && error.response) {
                    toast.error(error.response.data?.error?.message);
                } else {
                    toast.error("An error occurred");
                }
                setLoading(false);
            },
            onSuccess: () => {
                setFormData({
                    name: '',
                    email: '',
                    phone: '',
                    booking_code: '',
                    message: ''
                })
                toast.success("Your message has been sent!");
                setLoading(false);
            },
        });
    };

    const handleSubmit = (e: FormEvent) => {
        e.preventDefault();
        console.log(formData);
        send(formData);
    }

    const onSelectActiveBooking = (d: any) => {
        setSelectedActiveBooking(d.value);
        setFormData((prevData) => ({
            ...prevData,
            booking_code: d.value,
        }));
    }

    return (
        <div className="py-6 px-4 md:px-10">
            <section className="text-gray-800" id="contact">
                <Container>
                    <form className="text-gray-600 body-font relative" onSubmit={handleSubmit}>
                        <div className="container mx-auto px-2 py-6 md:px-5 md:py-8">
                            <div className="text-center w-full mb-8">
                                <h1 className="text-3xl md:text-4xl font-bold title-font mb-4 md:mb-6 text-gray-900">
                                    {t('contact.section.title')}
                                </h1>
                                <p className="lg:w-2/3 mx-auto leading-relaxed text-base">
                                    {t('contact.section.subtitle')}
                                </p>
                            </div>
                            <div className="lg:w-1/2 md:w-2/3 mx-auto rounded-lg p-4 md:p-8 shadow-lg">
                                <div className="flex flex-wrap -m-2">
                                    <div className="p-2 w-full md:w-1/2">
                                        <div className="relative">
                                            <label htmlFor="name"
                                                   className="leading-7 text-sm text-gray-600">{t('contact.form.name.label')}</label>
                                            <input type="text" id="name" name="name" value={formData.name}
                                                   onChange={handleChange}
                                                   className="w-full bg-gray-100 bg-opacity-50 rounded border border-gray-300 focus:border-indigo-500 focus:bg-white focus:ring-2 focus:ring-indigo-200 text-base outline-none py-2 px-3 leading-8 transition-colors duration-200 ease-in-out"/>
                                        </div>
                                    </div>
                                    <div className="p-2 w-full md:w-1/2">
                                        <div className="relative">
                                            <label htmlFor="email"
                                                   className="leading-7 text-sm text-gray-600">{t('contact.form.email.label')}</label>
                                            <input type="email" id="email" name="email" value={formData.email}
                                                   onChange={handleChange}
                                                   className="w-full bg-gray-100 bg-opacity-50 rounded border border-gray-300 focus:border-indigo-500 focus:bg-white focus:ring-2 focus:ring-indigo-200 text-base outline-none py-2 px-3 leading-8 transition-colors duration-200 ease-in-out"/>
                                        </div>
                                    </div>
                                    <div className="p-2 w-full md:w-1/2">
                                        <div className="relative">
                                            <label htmlFor="phone"
                                                   className="leading-7 text-sm text-gray-600">{t('contact.form.phone.label')}</label>
                                            <input type="text" id="phone" name="phone" value={formData.phone}
                                                   onChange={handleChange}
                                                   className="w-full bg-gray-100 bg-opacity-50 rounded border border-gray-300 focus:border-indigo-500 focus:bg-white focus:ring-2 focus:ring-indigo-200 text-base outline-none y-2 px-3 leading-8 transition-colors duration-200 ease-in-out"/>
                                        </div>
                                    </div>
                                    <div className="p-2 w-full">
                                        <div className="relative">
                                            <label htmlFor="message"
                                                   className="leading-7 text-sm text-gray-600">{t('contact.form.message.label')}</label>
                                            <textarea id="message" name="message" value={formData.message}
                                                      onChange={handleChange}
                                                      className="w-full bg-gray-100 bg-opacity-50 rounded border border-gray-300 focus:border-indigo-500 focus:bg-white focus:ring-2 focus:ring-indigo-200 text-base outline-none py-2 px-3 leading-8 transition-colors duration-200 ease-in-out"/>
                                        </div>
                                    </div>
                                    <div className="p-2 w-full flex flex-col md:flex-row justify-between items-center">
                                        <div className="w-full mb-4 md:mb-0 md:w-2/3 md:pr-2">
                                            <div className="relative">
                                                <label htmlFor="booking_select"
                                                       className="leading-7 text-sm text-gray-600">{t('contact.form.booking_select.label')}</label>
                                                <SearchableSelect
                                                    data={currentActiveBookings?.map((cab: any) => {
                                                        return {
                                                            value: cab.booking_code,
                                                            label: `${moment(cab.created_at).format('MM-DD-YY HH:mm')}/${cab.booking_code}`
                                                        }
                                                    })}
                                                    isFetching={false}
                                                    value={selectedActiveBooking}
                                                    onChange={onSelectActiveBooking}
                                                    disabled={!currentActiveBookings || currentActiveBookings.length === 0}
                                                    placeholder={t('contact.form.booking_select.placeholder')}
                                                />
                                            </div>
                                        </div>
                                        <div className="w-full md:w-1/3">
                                            <div className="relative">
                                                <label htmlFor="booking_code"
                                                       className="leading-7 text-sm text-gray-600"></label>
                                                <input type="text" id="booking_code" name="booking_code"
                                                       value={formData.booking_code}
                                                       onChange={handleChange}
                                                       className="w-full bg-gray-100 bg-opacity-50 rounded border border-gray-300 focus:border-indigo-500 focus:bg-white focus:ring-2 focus:ring-indigo-200 text-base outline-none h-10 mt-6 leading-8 transition-colors duration-200 ease-in-out"/>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="p-2 w-full flex justify-center mt-6">
                                        <MBButton type="submit" className="text-chillGold">
                                            {t('contact.form.submit.btn')}
                                        </MBButton>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </Container>
            </section>
        </div>
    );
};

export default ContactSection;
