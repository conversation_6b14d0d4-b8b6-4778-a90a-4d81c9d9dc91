import { z } from 'zod';

/**
 * CarType schema with validation
 */
export const CarTypeSchema = z.object({
  id: z.string().uuid(),
  name: z.string(),
  description: z.string().nullable().optional(),
  price_per_km: z.number(),
  base_fare: z.number(),
  price_per_minute_wait: z.number().nullable().optional(),
  image: z.string().url().nullable().optional(),
  capacity: z.number().int().min(1),
  created_at: z.string().or(z.date()).transform(val => new Date(val)),
  updated_at: z.string().or(z.date()).transform(val => new Date(val)),
  updated_by: z.number().nullable().optional(),
});

/**
 * CarType creation schema
 */
export const CarTypeCreateSchema = CarTypeSchema.omit({ 
  id: true, 
  created_at: true, 
  updated_at: true 
});

/**
 * CarType update schema
 */
export const CarTypeUpdateSchema = CarTypeSchema
  .partial()
  .omit({ 
    id: true, 
    created_at: true, 
    updated_at: true 
  });

// Type definitions derived from Zod schemas
export type CarType = z.infer<typeof CarTypeSchema>;
export type CarTypeCreate = z.infer<typeof CarTypeCreateSchema>;
export type CarTypeUpdate = z.infer<typeof CarTypeUpdateSchema>; 