import { NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';

export async function middleware(req: any) {
    const session = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });

    // If the user is trying to access the sign in page and is already authenticated, redirect them to /admin
    if (req.nextUrl.pathname.startsWith('/login') && session) {
        return NextResponse.redirect(new URL('/admin', req.url));
    }

    // Continue with the request if no redirection is needed
    return NextResponse.next();
}
