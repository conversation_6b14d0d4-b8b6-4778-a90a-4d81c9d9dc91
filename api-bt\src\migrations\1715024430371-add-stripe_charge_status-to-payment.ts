import {MigrationInterface, QueryRunner, TableColumn} from "typeorm";

export class addStripeChargeStatusToPayment1715024430371 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        const paymentTable = await queryRunner.getTable('payment');
        if (paymentTable) {
            if (!paymentTable.columns.find(column => column.name === 'disabled')) {
                await queryRunner.addColumn('payment', new TableColumn({
                    name: 'stripe_charge_status',
                    type: 'boolean',
                    isNullable: false,
                    default: false,
                }));
            }
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropColumn('payment', 'stripe_charge_status');
    }

}
