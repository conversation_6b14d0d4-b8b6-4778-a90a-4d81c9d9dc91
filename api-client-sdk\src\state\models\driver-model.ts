import { types, Instance, SnapshotIn } from 'mobx-state-tree';
import { CarModel } from './car-model';
import { UserModel } from './user-model';

/**
 * Driver model in MST
 */
export const DriverModel = types.model('DriverModel', {
  id: types.identifier,
  first_name: types.maybeNull(types.string),
  last_name: types.maybeNull(types.string),
  created_at: types.string, // We'll convert to Date in views if needed
  updated_at: types.string, // We'll convert to Date in views if needed
  updated_by: types.maybeNull(types.number),
  car: types.maybeNull(types.late(() => CarModel)),
  user: types.maybeNull(types.late(() => UserModel)),
})
.views(self => ({
  /**
   * Get full name
   */
  get fullName(): string {
    if (self.first_name && self.last_name) {
      return `${self.first_name} ${self.last_name}`;
    }
    
    if (self.user && self.user.first_name && self.user.last_name) {
      return `${self.user.first_name} ${self.user.last_name}`;
    }
    
    return 'N/A';
  },
  
  /**
   * Get created date as Date object
   */
  get createdDate(): Date {
    return new Date(self.created_at);
  },
  
  /**
   * Get updated date as Date object
   */
  get updatedDate(): Date {
    return new Date(self.updated_at);
  }
}));

// Type definitions for TypeScript
export interface Driver extends Instance<typeof DriverModel> {}
export interface DriverSnapshotIn extends SnapshotIn<typeof DriverModel> {} 