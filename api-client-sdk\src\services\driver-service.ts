import { apiClient } from '../core/api-client';
import { Driver, DriverCreate, DriverUpdate, DriverSchema } from '../models/driver';
import { PaginatedResponse, PaginationParams, createPaginationParams } from '../utils/pagination';
import { ApiResponse } from '../core/types';

/**
 * Driver service for handling driver operations
 */
export class DriverService {
  private static instance: DriverService;

  private constructor() {}

  /**
   * Get the singleton instance of the DriverService
   */
  public static getInstance(): DriverService {
    if (!DriverService.instance) {
      DriverService.instance = new DriverService();
    }
    return DriverService.instance;
  }

  /**
   * Get all drivers with pagination
   * @param params Pagination parameters
   * @returns Paginated list of drivers
   */
  public async getAllDrivers(params: PaginationParams = {}): Promise<ApiResponse<PaginatedResponse<Driver>>> {
    try {
      const paginationParams = createPaginationParams(params);
      const response = await apiClient.get<PaginatedResponse<Driver>>('/drivers', paginationParams);
      
      if (response.success && response.data) {
        // Validate each driver in the response
        const validatedDrivers = response.data.data.map(driver => DriverSchema.parse(driver));
        return {
          success: true,
          data: {
            ...response.data,
            data: validatedDrivers,
          },
        };
      }
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: {
          success: false,
          msg: error instanceof Error ? error.message : 'Failed to fetch drivers',
        },
      };
    }
  }

  /**
   * Get a driver by ID
   * @param id Driver ID
   * @returns Driver data
   */
  public async getDriverById(id: string): Promise<ApiResponse<Driver>> {
    try {
      const response = await apiClient.get<Driver>(`/drivers/${id}`);
      
      if (response.success && response.data) {
        // Validate driver data
        const validatedDriver = DriverSchema.parse(response.data);
        return {
          success: true,
          data: validatedDriver,
        };
      }
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: {
          success: false,
          msg: error instanceof Error ? error.message : `Failed to fetch driver with ID: ${id}`,
        },
      };
    }
  }

  /**
   * Create a new driver
   * @param driverData Driver creation data
   * @returns Created driver data
   */
  public async createDriver(driverData: DriverCreate): Promise<ApiResponse<Driver>> {
    try {
      const response = await apiClient.post<Driver>('/drivers', driverData);
      
      if (response.success && response.data) {
        // Validate response
        const validatedDriver = DriverSchema.parse(response.data);
        return {
          success: true,
          data: validatedDriver,
        };
      }
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: {
          success: false,
          msg: error instanceof Error ? error.message : 'Failed to create driver',
        },
      };
    }
  }

  /**
   * Update a driver
   * @param id Driver ID
   * @param driverData Driver update data
   * @returns Updated driver data
   */
  public async updateDriver(id: string, driverData: DriverUpdate): Promise<ApiResponse<Driver>> {
    try {
      const response = await apiClient.put<Driver>(`/drivers/${id}`, driverData);
      
      if (response.success && response.data) {
        // Validate response
        const validatedDriver = DriverSchema.parse(response.data);
        return {
          success: true,
          data: validatedDriver,
        };
      }
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: {
          success: false,
          msg: error instanceof Error ? error.message : `Failed to update driver with ID: ${id}`,
        },
      };
    }
  }

  /**
   * Delete a driver
   * @param id Driver ID
   * @returns API response
   */
  public async deleteDriver(id: string): Promise<ApiResponse<{ success: boolean }>> {
    try {
      const response = await apiClient.delete<{ success: boolean }>(`/drivers/${id}`);
      return response;
    } catch (error) {
      return {
        success: false,
        error: {
          success: false,
          msg: error instanceof Error ? error.message : `Failed to delete driver with ID: ${id}`,
        },
      };
    }
  }

  /**
   * Get drivers by car ID
   * @param carId Car ID
   * @param params Pagination parameters
   * @returns Paginated list of drivers for the specified car
   */
  public async getDriversByCar(carId: string, params: PaginationParams = {}): Promise<ApiResponse<PaginatedResponse<Driver>>> {
    try {
      const paginationParams = createPaginationParams(params);
      const response = await apiClient.get<PaginatedResponse<Driver>>(`/cars/${carId}/drivers`, paginationParams);
      
      if (response.success && response.data) {
        // Validate each driver in the response
        const validatedDrivers = response.data.data.map(driver => DriverSchema.parse(driver));
        return {
          success: true,
          data: {
            ...response.data,
            data: validatedDrivers,
          },
        };
      }
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: {
          success: false,
          msg: error instanceof Error ? error.message : `Failed to fetch drivers for car with ID: ${carId}`,
        },
      };
    }
  }
}

// Export a singleton instance of the driver service
export const driverService = DriverService.getInstance(); 