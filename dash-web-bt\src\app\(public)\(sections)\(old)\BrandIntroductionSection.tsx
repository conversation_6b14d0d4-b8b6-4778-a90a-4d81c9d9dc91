// BrandIntroductionSection.tsx
import React from 'react';
import Container from '@/app/(public)/(sections)/(old)/container';
import { GiSteeringWheel } from 'react-icons/gi'; // A steering wheel icon for driving/transport theme

const BrandIntroductionSection: React.FC = () => {
    return (
        <section className="py-16 bg-white text-gray-800" id="about">
            <Container>
                <div className="text-center">
                    <GiSteeringWheel className="mx-auto text-6xl text-primary-500 mb-4" />
                    <h2 className="text-3xl font-semibold mb-6">
                        Profesionales, fiables y dinámicos
                    </h2>
                    <p className="max-w-3xl mx-auto mb-4">
                        Bienvenido a Click4Transfer, líder en servicios de transporte de lujo. Nuestra flota moderna y conductores expertos garantizan un viaje seguro, confortable y en gran estilo.
                    </p>
                    <p className="max-w-3xl mx-auto">
                        Comprometidos con la excelencia, cada detalle de su viaje está meticulosamente planificado para asegurar una experiencia inigualable.
                    </p>
                </div>
            </Container>
        </section>
    );
};

export default BrandIntroductionSection;
