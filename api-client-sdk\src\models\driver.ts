import { z } from 'zod';
import { CarSchema } from './car';
import { UserSchema } from './user';

/**
 * Driver schema with validation
 */
export const DriverSchema = z.object({
  id: z.string().uuid(),
  first_name: z.string().nullable().optional(),
  last_name: z.string().nullable().optional(),
  created_at: z.string().or(z.date()).transform(val => new Date(val)),
  updated_at: z.string().or(z.date()).transform(val => new Date(val)),
  updated_by: z.number().nullable().optional(),
  car: z.lazy(() => CarSchema.optional().nullable()),
  user: z.lazy(() => UserSchema.optional().nullable()),
});

/**
 * Driver creation schema
 */
export const DriverCreateSchema = DriverSchema.omit({ 
  id: true, 
  created_at: true, 
  updated_at: true
});

/**
 * Driver update schema
 */
export const DriverUpdateSchema = DriverSchema
  .partial()
  .omit({ 
    id: true, 
    created_at: true, 
    updated_at: true 
  });

// Type definitions derived from Zod schemas
export type Driver = z.infer<typeof DriverSchema>;
export type DriverCreate = z.infer<typeof DriverCreateSchema>;
export type DriverUpdate = z.infer<typeof DriverUpdateSchema>; 