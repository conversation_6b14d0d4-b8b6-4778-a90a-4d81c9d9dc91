import { getRepository } from 'typeorm';
import { Bag } from '../../models/Bag'; // Adjust the path as necessary
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const createBag = async (data: Partial<Bag>) => {
  const bagRepository = getRepository(Bag);
  try {
    const newBag = bagRepository.create(data);
    await bagRepository.save(newBag);
    return { success: true, data: newBag, error: null };
  } catch (error) {
    const interpretedError = interpretDatabaseError(error);
    return { success: false, data: null, error: interpretedError };
  }
};
