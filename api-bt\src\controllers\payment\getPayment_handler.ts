import { Request, Response } from 'express';
import {
    getPayment,
} from '../../services/payment'; // Adjust the import path as necessary

export const getPaymentHandler = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        const result = await getPayment(id);
        if (!result.success) {
            return res.status(404).json(result);
        }
        return res.json(result);
    } catch (error: any) {
        return res.status(500).json({
            success: false,
            message: 'Failed to get payment',
            error: error.message,
        });
    }
};
