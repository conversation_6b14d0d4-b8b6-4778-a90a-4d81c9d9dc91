import { Request, Response } from 'express';
import {
    createCarType_Bag,
} from '../../services/car-type-bag';

export const createCarType_Bag_handler = async (req: Request, res: Response) => {
    try {
        const data = req.body;
        const result = await createCarType_Bag(data);
        if (!result.success) {
            return res.status(404).json(result);
        }
        return res.json(result);
    } catch (error: any) {
        return res.status(500).json({
            success: false,
            message: 'Failed to create CarType_Bag',
            errorType: 'InternalServerError',
            data: undefined,
        });
    }
};
