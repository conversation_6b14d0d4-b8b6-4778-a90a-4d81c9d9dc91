# API Client SDK Documentation

## Table of Contents

1. [Introduction](#introduction)
2. [Installation](#installation)
3. [Configuration](#configuration)
4. [Core Concepts](#core-concepts)
   - [API Client](#api-client)
   - [Services](#services)
   - [Models](#models)
   - [Error Handling](#error-handling)
5. [Authentication](#authentication)
6. [Services API](#services-api)
   - [Auth Service](#auth-service)
   - [User Service](#user-service)
   - [Driver Service](#driver-service)
   - [Booking Service](#booking-service)
   - [Car Service](#car-service)
   - [Customer Service](#customer-service)
   - [Payment Service](#payment-service)
7. [Tanstack Query Integration](#tanstack-query-integration)
8. [State Management](#state-management)
9. [React Integration](#react-integration)
10. [Advanced Usage](#advanced-usage)
    - [Extending the SDK](#extending-the-sdk)
    - [Custom Services](#custom-services)

## Introduction

The API Client SDK is a comprehensive client library for interacting with the API service. It provides a modular architecture with clean separation of concerns, making it easy to use with both vanilla TypeScript/JavaScript and React applications.

Key features:
- Fully typed API with TypeScript
- Zod validation for request and response data
- Clean architecture with separation of concerns
- HTTP client based on Axios
- MobX and MobX State Tree for state management
- TanStack Query (React Query) integration
- React hooks and providers for easy integration

## Installation

```bash
# Using npm
npm install api-client-sdk

# Using yarn
yarn add api-client-sdk

# Using pnpm
pnpm add api-client-sdk
```

## Configuration

Configure the SDK before using it:

```typescript
import { configureApi } from 'api-client-sdk';

// Basic configuration
configureApi({
  baseUrl: 'https://api.example.com',
});

// Advanced configuration
configureApi({
  baseUrl: 'https://api.example.com',
  timeout: 30000, // 30 seconds
  headers: {
    'X-Custom-Header': 'value',
  },
});
```

## Core Concepts

### API Client

The core of the SDK is an API client that handles HTTP requests to the API server. It provides methods for making GET, POST, PUT, DELETE, and PATCH requests.

```typescript
import { apiClient } from 'api-client-sdk';

// GET request
const response = await apiClient.get('/users', { page: 1, limit: 10 });

// POST request
const createResponse = await apiClient.post('/users', { name: 'John', email: '<EMAIL>' });

// PUT request
const updateResponse = await apiClient.put('/users/123', { name: 'John Doe' });

// DELETE request
const deleteResponse = await apiClient.delete('/users/123');

// PATCH request
const patchResponse = await apiClient.patch('/users/123/status', { status: 'active' });
```

### Services

Services are higher-level abstractions that encapsulate business logic for specific resources. They use the API client internally and provide more specific methods for interacting with the API.

```typescript
import { userService } from 'api-client-sdk';

// Get users with pagination
const users = await userService.getAllUsers({ page: 1, limit: 10 });

// Get a specific user
const user = await userService.getUserById(123);

// Create a new user
const newUser = await userService.createUser({
  username: 'johndoe',
  email: '<EMAIL>',
  password: 'password123',
  confirmed_password: 'password123',
  first_name: 'John',
  last_name: 'Doe',
});

// Update a user
const updatedUser = await userService.updateUser(123, {
  first_name: 'John',
  last_name: 'Doe',
});

// Delete a user
const deleteResult = await userService.deleteUser(123);
```

### Models

Models provide type definitions and validation for API data using Zod. The SDK includes models for all resources exposed by the API.

```typescript
import { UserSchema, UserCreateSchema, UserUpdateSchema } from 'api-client-sdk';

// Validate user data
const userData = { /* ... */ };
const result = UserSchema.safeParse(userData);

if (result.success) {
  // Data is valid
  const user = result.data;
  console.log(user.id, user.username);
} else {
  // Data is invalid
  console.error(result.error);
}

// Type definitions
import { User, UserCreate, UserUpdate } from 'api-client-sdk';

const user: User = { /* ... */ };
const createData: UserCreate = { /* ... */ };
const updateData: UserUpdate = { /* ... */ };
```

### Error Handling

The SDK provides standardized error handling across all services. All service methods return an `ApiResponse<T>` type that includes a success flag and either data or an error.

```typescript
import { userService } from 'api-client-sdk';

const response = await userService.getUserById(123);

if (response.success) {
  // Request was successful
  const user = response.data;
  console.log(user);
} else {
  // Request failed
  console.error(response.error.msg);
}
```

## Authentication

The SDK provides authentication through the auth service.

```typescript
import { authService } from 'api-client-sdk';

// Login
const loginResponse = await authService.login({
  email: '<EMAIL>',
  password: 'password123',
});

if (loginResponse.success) {
  // User is authenticated
  const user = loginResponse.user;
  const token = loginResponse.token;
} else {
  // Authentication failed
  console.error(loginResponse.msg);
}

// Check if user is authenticated
const isAuthenticated = authService.isAuthenticated();

// Get current user
const currentUser = authService.getCurrentUser();

// Logout
await authService.logout();
```

The SDK automatically handles authentication tokens for subsequent requests after login.

## Services API

### Auth Service

```typescript
import { authService } from 'api-client-sdk';

// Regular user login
const loginResponse = await authService.login({
  email: '<EMAIL>',
  password: 'password123',
});

// Admin login
const adminLoginResponse = await authService.loginAdmin({
  email: '<EMAIL>',
  password: 'admin123',
});

// Driver login
const driverLoginResponse = await authService.loginDriver({
  email: '<EMAIL>',
  password: 'driver123',
});

// Logout
const logoutResponse = await authService.logout();

// Get current user
const currentUser = authService.getCurrentUser();

// Check if authenticated
const isAuthenticated = authService.isAuthenticated();

// Get current session
const session = authService.getSession();

// Restore a previous session
authService.restoreSession({
  token: 'stored-token',
  user: storedUserObject,
});

// Clear session
authService.clearSession();
```

### User Service

```typescript
import { userService } from 'api-client-sdk';

// Get all users with pagination
const usersResponse = await userService.getAllUsers({
  page: 1,
  limit: 10,
  sort: 'created_at',
  order: 'DESC',
});

// Get user by ID
const userResponse = await userService.getUserById(123);

// Create a new user
const createResponse = await userService.createUser({
  username: 'johndoe',
  email: '<EMAIL>',
  password: 'password123',
  confirmed_password: 'password123',
  first_name: 'John',
  last_name: 'Doe',
});

// Update a user
const updateResponse = await userService.updateUser(123, {
  first_name: 'John',
  last_name: 'Smith',
});

// Delete a user
const deleteResponse = await userService.deleteUser(123);
```

### Driver Service

```typescript
import { driverService } from 'api-client-sdk';

// Get all drivers with pagination
const driversResponse = await driverService.getAllDrivers({
  page: 1,
  limit: 10,
});

// Get driver by ID
const driverResponse = await driverService.getDriverById('driver-id');

// Create a new driver
const createResponse = await driverService.createDriver({
  first_name: 'John',
  last_name: 'Doe',
  user: { id: 123 },
  car: { id: 'car-id' },
});

// Update a driver
const updateResponse = await driverService.updateDriver('driver-id', {
  first_name: 'John',
  last_name: 'Smith',
});

// Delete a driver
const deleteResponse = await driverService.deleteDriver('driver-id');

// Get drivers by car
const carDriversResponse = await driverService.getDriversByCar('car-id', {
  page: 1,
  limit: 10,
});
```

### Booking Service

```typescript
import { bookingService } from 'api-client-sdk';

// Get all bookings with pagination
const bookingsResponse = await bookingService.getAllBookings({
  page: 1,
  limit: 10,
});

// Get booking by ID
const bookingResponse = await bookingService.getBookingById('booking-id');

// Create a new booking
const createResponse = await bookingService.createBooking({
  pickup_address: '123 Main St',
  pickup_lat: '37.7749',
  pickup_long: '-122.4194',
  destination_address: '456 Market St',
  destination_lat: '37.7750',
  destination_long: '-122.4183',
  scheduled_at: '2023-01-01T12:00:00Z',
  adults: 2,
  flight_number: 'AA123',
  recommended_amount_to_pay: 50,
  final_amount_to_pay: 50,
  car_type: { id: 'car-type-id' },
  customer: { id: 'customer-id' },
});

// Update a booking
const updateResponse = await bookingService.updateBooking('booking-id', {
  scheduled_at: '2023-01-02T12:00:00Z',
});

// Update booking status
const statusResponse = await bookingService.updateBookingStatus('booking-id', 'accepted');

// Assign driver to booking
const assignResponse = await bookingService.assignDriver('booking-id', 'driver-id');

// Delete a booking
const deleteResponse = await bookingService.deleteBooking('booking-id');

// Get bookings by customer
const customerBookingsResponse = await bookingService.getBookingsByCustomer('customer-id', {
  page: 1,
  limit: 10,
});

// Get bookings by driver
const driverBookingsResponse = await bookingService.getBookingsByDriver('driver-id', {
  page: 1,
  limit: 10,
});
```

## TanStack Query Integration

The SDK provides TanStack Query (React Query) hooks for all services, making it easy to use with React applications.

```tsx
import { 
  useLogin, 
  useUser, 
  useUsers, 
  useCreateUser, 
  useUpdateUser, 
  useDeleteUser 
} from 'api-client-sdk';

// Login mutation
const LoginForm = () => {
  const { mutate: login, isLoading, error } = useLogin();
  
  const handleSubmit = (e) => {
    e.preventDefault();
    login({ 
      email: '<EMAIL>', 
      password: 'password123' 
    });
  };
  
  return (/* ... */);
};

// Fetch users
const UserList = () => {
  const { data, isLoading, error } = useUsers({ page: 1, limit: 10 });
  
  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  
  return (
    <ul>
      {data?.data?.data.map(user => (
        <li key={user.id}>{user.fullName}</li>
      ))}
    </ul>
  );
};

// Fetch a single user
const UserDetails = ({ id }) => {
  const { data, isLoading, error } = useUser(id);
  
  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  
  return (/* ... */);
};

// Create user mutation
const CreateUserForm = () => {
  const { mutate: createUser, isLoading } = useCreateUser();
  
  const handleSubmit = (userData) => {
    createUser(userData);
  };
  
  return (/* ... */);
};

// Update user mutation
const UpdateUserForm = ({ id }) => {
  const { mutate: updateUser, isLoading } = useUpdateUser();
  
  const handleSubmit = (userData) => {
    updateUser({ id, data: userData });
  };
  
  return (/* ... */);
};

// Delete user mutation
const DeleteUserButton = ({ id }) => {
  const { mutate: deleteUser, isLoading } = useDeleteUser();
  
  const handleDelete = () => {
    deleteUser(id);
  };
  
  return (
    <button onClick={handleDelete} disabled={isLoading}>
      {isLoading ? 'Deleting...' : 'Delete'}
    </button>
  );
};
```

## State Management

The SDK provides state management with MobX State Tree (MST) for client-side data management.

```tsx
import { useStore } from 'api-client-sdk';

const UserProfile = () => {
  const { authStore, userStore } = useStore();
  
  // Access current user from auth store
  const currentUser = authStore.currentUser;
  
  // Load user data
  useEffect(() => {
    userStore.fetchUserById(currentUser.id);
  }, [userStore, currentUser.id]);
  
  // Access user data from store
  const user = userStore.selectedUser;
  const isLoading = userStore.isLoading;
  
  if (isLoading) return <div>Loading...</div>;
  if (!user) return <div>User not found</div>;
  
  return (/* ... */);
};
```

## React Integration

The SDK includes React integration through the `ApiProvider` component and custom hooks.

```tsx
import { ApiProvider, useAuth, useUsers, useApi } from 'api-client-sdk';

// Wrap your application with the provider
const App = () => {
  return (
    <ApiProvider>
      <YourApplication />
    </ApiProvider>
  );
};

// Use the auth hook
const LoginForm = () => {
  const { login, isAuthenticated, isLoading, error } = useAuth();
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    await login({ 
      email: '<EMAIL>', 
      password: 'password123' 
    });
  };
  
  return (/* ... */);
};

// Use the users hook
const UserList = () => {
  const { users, fetchUsers, isLoading, error } = useUsers();
  
  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);
  
  return (/* ... */);
};

// Use the combined API hook
const Dashboard = () => {
  const { auth, users } = useApi();
  
  return (/* ... */);
};
```

## Advanced Usage

### Extending the SDK

You can extend the SDK with custom functionality:

```typescript
import { apiClient, ApiResponse } from 'api-client-sdk';

// Create a custom service
class CustomService {
  private static instance: CustomService;
  
  private constructor() {}
  
  public static getInstance(): CustomService {
    if (!CustomService.instance) {
      CustomService.instance = new CustomService();
    }
    return CustomService.instance;
  }
  
  public async doSomething(data: any): Promise<ApiResponse<any>> {
    try {
      const response = await apiClient.post('/custom-endpoint', data);
      return response;
    } catch (error) {
      return {
        success: false,
        error: {
          success: false,
          msg: error instanceof Error ? error.message : 'Operation failed',
        },
      };
    }
  }
}

export const customService = CustomService.getInstance();
```

### Custom Services

You can create custom services for specific business needs:

```typescript
import { ApiResponse } from 'api-client-sdk';
import { apiClient } from 'api-client-sdk';

// Define your service
export class ReportService {
  private static instance: ReportService;
  
  private constructor() {}
  
  public static getInstance(): ReportService {
    if (!ReportService.instance) {
      ReportService.instance = new ReportService();
    }
    return ReportService.instance;
  }
  
  public async generateReport(params: any): Promise<ApiResponse<any>> {
    try {
      const response = await apiClient.post('/reports/generate', params);
      return response;
    } catch (error) {
      return {
        success: false,
        error: {
          success: false,
          msg: error instanceof Error ? error.message : 'Failed to generate report',
        },
      };
    }
  }
  
  public async downloadReport(id: string): Promise<ApiResponse<any>> {
    try {
      const response = await apiClient.get(`/reports/${id}/download`);
      return response;
    } catch (error) {
      return {
        success: false,
        error: {
          success: false,
          msg: error instanceof Error ? error.message : 'Failed to download report',
        },
      };
    }
  }
}

export const reportService = ReportService.getInstance();
``` 