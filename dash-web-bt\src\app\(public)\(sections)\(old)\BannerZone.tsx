'use client';

import React, {useEffect} from 'react';
import Container from '@/app/(public)/(sections)/(old)/container';
import {Button} from 'rizzui';
import Autocomplete from "@/components/lib/google-map/autocomplete";
import {useRouter} from 'next/navigation';
import toast from "react-hot-toast";
import {routes} from "@/config/routes";
import {BackgroundGradient} from "@/components/app/BackgroundGradient";

const BannerZone: React.FC = (props) => {
    const router = useRouter();
    const [pickupLocation, setPickupLocation] = React.useState<string | null>(null);

    // useEffect(() => {
    //     try {
    //         if('geolocation' in navigator) {
    //             // Retrieve latitude & longitude coordinates from `navigator.geolocation` Web API
    //             navigator.geolocation.getCurrentPosition(({ coords }) => {
    //                 const { latitude, longitude } = coords;
    //                     console.log('! coords-', coords)
    //
    //                 setAddress({
    //                     formatted_address: 'Current Location',
    //                     geometry: {location: {lat: () => latitude, lng: () => longitude}}
    //                 });
    //             }, (error) => console.error(error),
    //                 {enableHighAccuracy: true})
    //         }
    //     } catch (e: any) {
    //         console.log('e', e)
    //     }
    // }, []);

    function setAddress(location: any) {
        setPickupLocation(`${location.formatted_address}|${location.geometry.location.lat()}|${location.geometry.location.lng()}`);
    }

    const scheduleBooking = () => {
        if (pickupLocation) {
            router.push(`${routes.multiStepCustomerBooking}?pickup_location=${encodeURIComponent(pickupLocation)}`);
            return;
        }
        toast.error('Let\'s start by entering a pickup location below');
    }

    return (
        <>
            <div className="relative w-full min-h-screen bg-cover bg-center"
                 id='home'
                 style={{backgroundImage: 'url(/banners/b_03.webp)'}}>
                <Container>
                    {/*<div className="">*/}
                    {/*    <img src='/brand/click4transfer-logo.png' alt="Logo" className="h-28"/>*/}
                    {/*</div>*/}
                    <div className="w-full md:w-2/3 lg:w-2/5 p-10 mt-20" style={{
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        borderRadius: '10px',
                        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
                    }}>
                        <div className="text-white text-center md:text-left">
                            <h1 className="text-2xl lg:text-4xl font-bold mb-4" style={{color: '#FEDD00'}}>
                                Welcome to Click4Transfer
                            </h1>
                            <p className="mb-6" style={{color: '#FFFFFF'}}>
                                Your journey begins here! You can start by telling us where to pick you up.
                            </p>
                            <div
                                className="flex flex-col items-center justify-center space-y-4 md:space-y-0 md:space-x-4 md:flex-row">
                                <Autocomplete
                                    apiKey={process.env.NEXT_PUBLIC_GOOGLE_MAP_API_KEY as string}
                                    mapClassName="rounded-lg"
                                    spinnerClassName="grid h-full w-full place-content-center"
                                    className="w-full sm:w-3/3 lg:w-4/5 rounded-lg text-white font-bold text-lg bg-transparent"
                                    hideMap={true}
                                    onPlaceSelect={setAddress}
                                />
                                <BackgroundGradient>
                                    <Button
                                        style={{backgroundColor: 'transparent', color: '#FFFFFF'}}
                                        onClick={scheduleBooking}
                                        // className="w-full sm:w-3/3 lg:w-1/5"
                                    >
                                        Next
                                    </Button>
                                </BackgroundGradient>
                            </div>
                        </div>
                    </div>
                </Container>
            </div>
        </>
    );
};

export default BannerZone;
