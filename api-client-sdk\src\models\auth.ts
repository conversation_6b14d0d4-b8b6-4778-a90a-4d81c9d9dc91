import { z } from 'zod';
import { UserSchema } from './user';

/**
 * Login credentials schema
 */
export const LoginCredentialsSchema = z.object({
  email: z.string().email(),
  password: z.string().min(1),
});

/**
 * Login response schema
 */
export const LoginResponseSchema = z.object({
  success: z.boolean(),
  token: z.string().optional(),
  user: UserSchema.optional(),
  msg: z.string().optional(),
});

/**
 * Logout response schema
 */
export const LogoutResponseSchema = z.object({
  success: z.boolean(),
  msg: z.string().optional(),
});

/**
 * Auth session schema
 */
export const AuthSessionSchema = z.object({
  token: z.string(),
  user: UserSchema,
});

// Type definitions derived from Zod schemas
export type LoginCredentials = z.infer<typeof LoginCredentialsSchema>;
export type LoginResponse = z.infer<typeof LoginResponseSchema>;
export type LogoutResponse = z.infer<typeof LogoutResponseSchema>;
export type AuthSession = z.infer<typeof AuthSessionSchema>; 