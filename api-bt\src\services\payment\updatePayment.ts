// services/payment.ts
import { getRepository } from 'typeorm';
import { Payment } from '../../models/Payment'; // Adjust the path as necessary

export const updatePayment = async (id: string, data: Partial<Payment>) => {
  const paymentRepository = getRepository(Payment);
  try {
    const payment = await paymentRepository.findOne(id);
    if (!payment) {
      return { success: false, data: null, error: 'Payment not found' };
    }

    paymentRepository.merge(payment, data);
    const updatedPayment = await paymentRepository.save(payment);
    return { success: true, data: updatedPayment, error: null };
  } catch (error: any) {
    return { success: false, data: null, error: error.message };
  }
};

