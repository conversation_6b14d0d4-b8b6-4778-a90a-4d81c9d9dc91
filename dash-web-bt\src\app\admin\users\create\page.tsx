import { routes } from '@/config/routes';
import PageHeader from '@/app/shared/lib/page-header';
import { metaObject } from '@/config/site.config';
import CreateEdit from '@/app/shared/app/users/create-edit';

export const metadata = {
  ...metaObject('Create User'),
};

const pageHeader = {
  title: 'Create User',
  breadcrumb: [
    {
      href: routes.dashboard,
      name: 'Dashboard',
    },
    {
      href: routes.users,
      name: 'Users',
    },
    {
      name: 'Create User',
    },
  ],
};

export default function CreateShipmentPage() {
  return (
    <>
      <PageHeader title={pageHeader.title} breadcrumb={pageHeader.breadcrumb}>
      </PageHeader>

      <CreateEdit />
    </>
  );
}
