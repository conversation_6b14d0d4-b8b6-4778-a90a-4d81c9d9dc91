'use client';

import HorizontalFormBlockWrapper from "@/app/shared/app/account/settings/horiozontal-block";
import {Button} from "rizzui";
import {useEffect, useState} from "react";
import {useUpdateGlobalSettings} from "@/services/mutations/setting/update-global-settings";
import {useGetGlobalSettings} from "@/services/query/setting/public/useGetGlobalSettings";
import toast from "react-hot-toast";
import {SettingSchemaType} from '@/utils/validators/app/entities/setting';
import axios from "axios";
import {ZodError} from 'zod';

export default function ProfileSettingsFormPage() {
    const [isLoading, setLoading] = useState(false);
    const [passCorrect, setPassCorrect] = useState(false);
    const [newGlobalSettings, setNewGlobalSettings] = useState<SettingSchemaType[] | null>(null);

    const {data: globalSettings} = useGetGlobalSettings();

    const updateGlobalSettings = useUpdateGlobalSettings();

    const saveNewGlobalSettings = async () => {
        setLoading(true);

        if (newGlobalSettings) {
            try {
                updateGlobalSettings.mutate(newGlobalSettings, {
                    onError: (error: any) => {
                        // console.log('error', error);
                        // console.log('newGlobalSettings', newGlobalSettings);
                        if (axios.isAxiosError(error) && error.response) {
                            toast.error(error.response.data.msg);
                        } else if (error instanceof ZodError) {
                            error.errors.forEach(err => toast.error(err.message));
                        } else {
                            toast.error("An error occurred");
                        }
                        setLoading(false);
                    },
                    onSuccess: () => {
                        toast.success('Successfully Updated all global settings!');
                        setLoading(false);
                    },
                });
            } catch (error) {
                if (error instanceof ZodError) {
                    error.errors.forEach(err => toast.error(err.message));
                }
                setLoading(false);
            }
        }
    };

    useEffect(() => {
        setNewGlobalSettings(globalSettings?.data || [])
    }, [globalSettings]);

    const handleChange = (index: number, field: keyof SettingSchemaType, value: string) => {
        const updatedSettings = newGlobalSettings ? [...newGlobalSettings] : [];
        updatedSettings[index] = {...updatedSettings[index], [field]: value};
        setNewGlobalSettings(updatedSettings);
    };

    const handleRemove = (index: number) => {
        if(!passCorrect) {
            const pass = window.prompt('Password ?')
            if(pass !== 'admin') return;
            setPassCorrect(true)
        }

        const updatedSettings = newGlobalSettings ? newGlobalSettings.filter((_, i) => i !== index) : [];
        setNewGlobalSettings(updatedSettings);
    };

    const handleAdd = () => {
        if(!passCorrect) {
            const pass = window.prompt('Password ?')
            if(pass !== 'admin') return;
            setPassCorrect(true)
        }

        const newSetting: SettingSchemaType = {
            name: '',
            value: '',
            scope: 'public',
        };
        const withAdded = newGlobalSettings ? [...newGlobalSettings, newSetting] : [newSetting];
        setNewGlobalSettings(withAdded);
    };

    return (
        <>
            <div className="mx-auto w-full max-w-screen-2xl p-4">
                <HorizontalFormBlockWrapper title="Global Settings" titleClassName="text-base font-medium">
                    <div className="flex justify-between mb-4">
                        <button onClick={handleAdd}
                                className="px-4 py-2 bg-blue-500 text-white rounded shadow hover:bg-blue-600 transition duration-200">
                            Add New Setting
                        </button>
                    </div>
                    {
                        newGlobalSettings ? (
                            newGlobalSettings.map((setting, index) => (
                                <div key={index} className="flex items-center gap-4 mb-4 w-full">
                                    <input
                                        disabled={!passCorrect}
                                        type="text"
                                        value={setting.name}
                                        onChange={(e) => handleChange(index, 'name', e.target.value)}
                                        placeholder="Name"
                                        className="form-input w-1/4"
                                    />
                                    <input
                                        type="text"
                                        value={setting.value}
                                        onChange={(e) => handleChange(index, 'value', e.target.value)}
                                        placeholder="Value"
                                        className="form-input w-1/4"
                                    />
                                    <select
                                        disabled={!passCorrect}

                                        value={setting.scope}
                                        onChange={(e) => handleChange(index, 'scope', e.target.value)}
                                        className="form-select w-1/4"
                                    >
                                        <option value="public">Public</option>
                                        <option value="secret">Secret</option>
                                        <option value="dev">Dev</option>
                                    </select>
                                    <button onClick={() => handleRemove(index)} className="text-red-500 w-1/4">Remove
                                    </button>
                                </div>
                            ))
                        ) : null
                    }
                </HorizontalFormBlockWrapper>
                <div className="mt-6 flex w-full items-center justify-end gap-3">
                    <Button onClick={saveNewGlobalSettings} variant="solid" isLoading={isLoading}
                            className="px-6 py-2 bg-green-500 text-white rounded shadow hover:bg-green-600 transition duration-200">
                        Update Settings
                    </Button>
                </div>
            </div>
        </>
    );
}
