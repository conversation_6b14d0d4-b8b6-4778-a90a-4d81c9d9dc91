import { Request, Response } from 'express';
import { createCar } from '../../services/car';

export const createCar_handler = async (req: Request, res: Response) => {
  try {
    const carData = req.body;
    const selectedDriverIds = req.body.drivers;

    const result = await createCar(carData, selectedDriverIds);

    if (result.success) {
      return res.status(201).json(result); // Return 201 status for a successful creation
    } else {
      return res.status(400).json(result); // Return 400 status for a client error
    }
  } catch (error: any) {
    return res.status(500).json({
      success: false,
      message: 'Failed to create car',
      errorType: 'InternalServerError',
      data: undefined,
    });
  }
};