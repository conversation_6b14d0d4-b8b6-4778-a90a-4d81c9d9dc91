import {
  <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, <PERSON>umn, CreateDateColumn, UpdateDateColumn, OneToMany, JoinColumn,
} from 'typeorm';
// eslint-disable-next-line import/no-cycle
import { Car } from './Car';
import {CarTypePrice} from "./CarTypePrice";
import {CarTypeBag} from "./CarTypeBag";
import {ColumnNumericTransformer} from "../utils/ColumnNumericTransformer";

@Entity()
export class CarType {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ length: 50 })
  name: string;

  // CarTypes has many CarTypePrice below:
  @OneToMany(() => CarTypePrice, (carTypePrice: CarTypePrice) => carTypePrice.car_type) // This creates the inverse relationship
  @JoinColumn({ name: 'car_type_price_id' })
  transfer_prices: CarTypePrice[];

  @Column('numeric', {
    precision: 7,
    scale: 2,
    transformer: new ColumnNumericTransformer(),
    nullable: false,
  })
  default_price_per_km: number;

  @OneToMany(() => Car, (car: Car) => car.type) // This creates the inverse relationship
  cars: Car[];

  @OneToMany(() => CarTypeBag, (carTypeBag: CarTypeBag) => carTypeBag.carType)
  bags: CarTypeBag[];

  @Column('int')
  seats: number;

  @Column('numeric', {
    precision: 7,
    scale: 2,
    transformer: new ColumnNumericTransformer(),
    nullable: true,
  })
  hourly_rate: number;

  @Column('boolean', { default: false })
  automatic_acceptance: boolean;

  @Column('boolean', { default: false })
  use_default_ppk: boolean;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ nullable: true })
  updated_by: number;
}
