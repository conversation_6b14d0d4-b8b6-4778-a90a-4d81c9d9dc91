import { Request, Response } from 'express';
import {
  createBag,
} from '../../services/bag';

export const createBag_handler = async (req: Request, res: Response) => {
  try {
    const data = req.body;
    const result = await createBag(data);
    if (!result.success) {
      return res.status(404).json(result);
    }
    return res.json(result);
  } catch (error: any) {
    return res.status(500).json({
      success: false,
      message: 'Failed to create bag',
      errorType: 'InternalServerError',
      data: undefined,
    });
  }
};
