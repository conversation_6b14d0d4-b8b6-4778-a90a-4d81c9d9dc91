'use client';

import { useIsMounted } from '@/hooks/use-is-mounted';
import CarbonLayout from '@/layouts/carbon/carbon-layout';
import HydrogenLayout from '@/layouts/hydrogen/layout';
import {useLayout} from "@/hooks/use-layout";
import { LAYOUT_OPTIONS } from '@/config/enums';

type LayoutProps = {
  children: React.ReactNode;
};

export default function DefaultLayout({ children }: LayoutProps) {
  return <LayoutProvider>{children}</LayoutProvider>;
}

function LayoutProvider({ children }: LayoutProps) {
  const { layout } = useLayout();

  const isMounted = useIsMounted();

  if (!isMounted) {
    return null;
  }

  if (layout === LAYOUT_OPTIONS.CARBON) {
    return <CarbonLayout>{children}</CarbonLayout>;
  }

  return <HydrogenLayout>{children}</HydrogenLayout>;
}
