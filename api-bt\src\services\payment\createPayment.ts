// services/payment.ts
import { getRepository } from 'typeorm';
import { Payment } from '../../models/Payment'; // Adjust the path as necessary

export const createPayment = async (data: Partial<Payment>) => {
  const paymentRepository = getRepository(Payment);
  try {
    const newPayment = paymentRepository.create(data);
    await paymentRepository.save(newPayment);
    return { success: true, data: newPayment, error: null };
  } catch (error: any) {
    return { success: false, data: null, error: error.message };
  }
};
