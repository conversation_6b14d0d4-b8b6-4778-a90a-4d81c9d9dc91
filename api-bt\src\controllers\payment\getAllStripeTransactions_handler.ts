import { Request, Response } from 'express';
import stripeTransactionService, { StripeStatusType } from '../../services/vendor/stripe/StripeTransactionService'; // Adjust the import path as necessary
import { paginate } from '../../utils/pagination'; // Assuming you have a pagination utility

export const getAllStripeTransactionsHandler = async (req: Request, res: Response) => {
    try {
        const page = parseInt(req.query.page as string, 10) || 1;
        const limit = parseInt(req.query.limit as string, 10) || 10;
        const startingAfter = req.query.startingAfter as string;
        const status = req.query.status as StripeStatusType;
        const customer = req.query.customer as string;

        const result = await stripeTransactionService.fetchTransactions(limit, startingAfter, status, customer);
        if (!result.data) {
            return res.status(404).json({ success: false, message: 'No transactions found' });
        }

        const totalRecords = result.data.length; // Assuming your stripe service provides a total count of records
        const paginatedResult = paginate(result.data, page, limit, totalRecords);

        return res.json(paginatedResult);
    } catch (error: any) {
        return res.status(500).json({
            success: false,
            message: 'Failed to get payment transactions',
            error: error.message,
        });
    }
};
