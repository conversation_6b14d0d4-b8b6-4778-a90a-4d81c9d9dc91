'use client';

import {usePaginatedZones} from "@/services/query/zone/usePaginatedZones";
import Header from "./page-header";
import React, {useState} from "react";
import CarsTable from "./table";
import {Loader} from "rizzui";

export default function CarsPage() {
    const [currentPage, setCurrentPage] = useState(1);
    const [currentLimit, setCurrentLimit] = useState(10);

    const {
        isPending,
        isError,
        error,
        data,
        isFetching,
        isPlaceholderData,
        refetch,
        status,
    } = usePaginatedZones(currentPage, currentLimit);

    const onSelectedPage = (page: number) => {
        setCurrentPage(page);
    }

    const onPageLimitChange = (limit: number) => {
        setCurrentLimit(limit);
    }

    const onFiltersChanged = (filters: any) => {
        refetch();
    }

    return <>
        <Header />

        <div className="flex flex-col gap-10 @container">
            {
                data ?
                    <CarsTable
                        initialData={data}
                        onSelectedPage={onSelectedPage}
                        onPageLimitChange={onPageLimitChange}
                        variant="elegant"
                        className="[&_.table-filter]:hidden [&_.table-pagination]:hidden"
                    /> : <div className="grid h-32 flex-grow place-content-center items-center">
                        <Loader size="lg"/>
                    </div>
            }
        </div>
    </>;
}
