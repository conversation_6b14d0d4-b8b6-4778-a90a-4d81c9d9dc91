import { types, Instance, SnapshotIn, onSnapshot } from 'mobx-state-tree';
import { AuthStore, createAuthStore } from './auth-store';
import { UserStore, createUserStore } from './user-store';

/**
 * Root store that combines all other stores
 */
export const RootStore = types.model('RootStore', {
  authStore: types.optional(AuthStore, () => createAuthStore()),
  userStore: types.optional(UserStore, () => createUserStore()),
  // Add other stores as needed
});

// Type definitions
export interface IRootStore extends Instance<typeof RootStore> {}
export interface IRootStoreSnapshotIn extends SnapshotIn<typeof RootStore> {}

/**
 * Create a root store instance with persistence
 */
export function createRootStore(snapshot?: any): IRootStore {
  const store = RootStore.create(snapshot || {});
  
  // Add persistence if needed
  if (typeof window !== 'undefined') {
    onSnapshot(store, (snapshot) => {
      try {
        // Store state in localStorage
        localStorage.setItem('api-sdk-state', JSON.stringify(snapshot));
      } catch (error) {
        console.error('Failed to persist state:', error);
      }
    });
  }
  
  return store;
}

/**
 * Load persisted state from storage
 */
export function loadPersistedState(): any {
  if (typeof window === 'undefined') return undefined;
  
  try {
    const persisted = localStorage.getItem('api-sdk-state');
    return persisted ? JSON.parse(persisted) : undefined;
  } catch (error) {
    console.error('Failed to load persisted state:', error);
    return undefined;
  }
}

/**
 * Create a store with persisted state if available
 */
export function initializeStore(snapshot?: any): IRootStore {
  const initialState = snapshot || loadPersistedState();
  return createRootStore(initialState);
} 