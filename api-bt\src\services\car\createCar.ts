import { getRepository, getConnection } from 'typeorm';
import { Car } from '../../models/Car';
import { Driver } from '../../models/Driver';
import { ErrorDetails, interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const createCar = async (data: Partial<Car>, driverIds: string[]): Promise<{
  success: boolean, data: Car | undefined | null, error: string | null | ErrorDetails
}> => {
  const carRepository = getRepository(Car);
  const driverRepository = getRepository(Driver);

  try {
    // Using a transaction to ensure atomicity
    const newCar = await getConnection().transaction(async transactionalEntityManager => {
      // Create a new Car entity with the provided data
      const newCar = carRepository.create(data);

      // Save the new Car entity
      await transactionalEntityManager.save(newCar);

      // Retrieve Driver entities by their IDs
      const drivers = await driverRepository.findByIds(driverIds);

      // Associate each driver with the new Car
      drivers.forEach(driver => {
        driver.car = newCar;
      });

      // Save all driver entities with the new association
      await transactionalEntityManager.save(drivers);

      return newCar;
    });

    // Fetch the new car along with its drivers to return
    const savedCar = await carRepository.findOne({ where: { id: newCar.id }, relations: ["drivers"] });

    return { success: true, data: savedCar, error: null };
  } catch (error) {
    const interpretedError = interpretDatabaseError(error);
    return { success: false, data: null, error: interpretedError };
  }
};