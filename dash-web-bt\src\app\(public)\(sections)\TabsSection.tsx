"use client";

import Image from "next/image";
import Tabs from "@/components/app/Tabs";
import React from "react";
import { useI18n } from "@/hooks/use-translation";

export default function TabsSection() {
    const { t } = useI18n();

    const tabs = [
        {
            title: t("team.member1.name"),
            value: "1",
            content: (
                <div className="relative h-full w-full p-4 md:p-10 text-white bg-gradient-to-br from-black to-black rounded-2xl shadow-lg overflow-hidden">
                    <div className="relative h-full w-full">
                        <Image
                            src="https://dummyimage.com/1280x720/000/aaa"
                            alt={t("team.member1.name")}
                            width="1000"
                            height="1000"
                            className="object-cover object-center h-[60%] md:h-[90%] w-full rounded-xl"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-80 rounded-xl"></div>
                    </div>
                    <div
                        className="absolute bottom-0 inset-x-0 bg-gradient-to-r from-black via-transparent to-transparent bg-opacity-50 p-2 md:p-6 rounded-b-2xl shadow-md transition-all duration-300 "
                    >
                        <h2 className="text-xl md:text-3xl font-bold tracking-wide mb-2 md:mb-4">{t("team.member1.name")}</h2>
                        <p className="text-sm md:text-lg font-light">
                            {t("team.member1.description")}
                        </p>
                    </div>
                </div>
            ),
        },
        {
            title: t("team.member2.name"),
            value: "2",
            content: (
                <div className="relative h-full w-full p-4 md:p-10 text-white bg-gradient-to-br from-black to-black rounded-2xl shadow-lg overflow-hidden">
                    <div className="relative h-full w-full">
                        <Image
                            src="https://dummyimage.com/1280x720/000/aaa"
                            alt={t("team.member2.name")}
                            width="1000"
                            height="1000"
                            className="object-cover object-center h-[60%] md:h-[90%] w-full rounded-xl"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-80 rounded-xl"></div>
                    </div>
                    <div
                        className="absolute bottom-0 inset-x-0 bg-gradient-to-r from-black via-transparent to-transparent bg-opacity-50 p-2 md:p-6 rounded-b-2xl shadow-md transition-all duration-300"
                    >
                        <h2 className="text-xl md:text-3xl font-bold tracking-wide mb-2 md:mb-4">{t("team.member2.name")}</h2>
                        <p className="text-sm md:text-lg font-light">
                            {t("team.member2.description")}
                        </p>
                    </div>
                </div>
            ),
        },
        {
            title: t("team.member3.name"),
            value: "3",
            content: (
                <div className="relative h-full w-full p-4 md:p-10 text-white bg-gradient-to-br from-black to-black rounded-2xl shadow-lg overflow-hidden">
                    <div className="relative h-full w-full">
                        <Image
                            src="https://dummyimage.com/1280x720/000/aaa"
                            alt={t("team.member3.name")}
                            width="1000"
                            height="1000"
                            className="object-cover object-center h-[60%] md:h-[90%] w-full rounded-xl"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-80 rounded-xl"></div>
                    </div>
                    <div
                        className="absolute bottom-0 inset-x-0 bg-gradient-to-r from-black via-transparent to-transparent bg-opacity-50 p-2 md:p-6 rounded-b-2xl shadow-md transition-all duration-300">
                        <h2 className="text-xl md:text-3xl font-bold tracking-wide mb-2 md:mb-4">{t("team.member3.name")}</h2>
                        <p className="text-sm md:text-lg font-light">
                            {t("team.member3.description")}
                        </p>
                    </div>
                </div>
            ),
        },
    ];

    return (
        <div
            className="h-[20rem] md:h-[40rem] [perspective:1000px] relative flex flex-col max-w-5xl mx-auto w-full items-start justify-start my-40 p-6">
            <h1 className="text-lg md:text-2xl lg:text-4xl font-bold mb-2 md:mb-4 text-chillGold mb-4">{t("team.title")}</h1>
            <Tabs containerClassName='' tabs={tabs} labelColor='text-chillGold' />
        </div>
    );
}
