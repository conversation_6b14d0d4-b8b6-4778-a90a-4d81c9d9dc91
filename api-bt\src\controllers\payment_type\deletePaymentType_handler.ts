import { Request, Response } from 'express';
import {
    deletePaymentType,
} from '../../services/payment-type'; // Adjust the import path as necessary

export const deletePaymentTypeHandler = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        const result = await deletePaymentType(id);
        if (!result.success) {
            return res.status(404).json(result);
        }
        return res.json(result);
    } catch (error: any) {
        return res.status(500).json({
            success: false,
            message: 'Failed to delete payment type',
            error: error.message,
        });
    }
};
