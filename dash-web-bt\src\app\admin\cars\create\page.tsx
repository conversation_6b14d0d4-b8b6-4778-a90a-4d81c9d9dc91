import { routes } from '@/config/routes';
import PageHeader from '@/app/shared/lib/page-header';
import { metaObject } from '@/config/site.config';
import CreateEdit from '@/app/shared/app/cars/create-edit';

export const metadata = {
  ...metaObject('Create Car'),
};

const pageHeader = {
  title: 'Create Car',
  breadcrumb: [
    {
      href: routes.dashboard,
      name: 'Dashboard',
    },
    {
      href: routes.tickets,
      name: 'Cars',
    },
    {
      name: 'Create Car',
    },
  ],
};

export default function CreateShipmentPage() {
  return (
    <>
      <PageHeader title={pageHeader.title} breadcrumb={pageHeader.breadcrumb}>
      </PageHeader>

      <CreateEdit />
    </>
  );
}
