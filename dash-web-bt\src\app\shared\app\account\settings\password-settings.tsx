'use client';

import {useState} from 'react';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, Controller} from 'react-hook-form';
import {Form} from '@/components/lib/ui/form';
import {Button, Password, Text} from 'rizzui';
import HorizontalFormBlockWrapper from '@/app/shared/app/account/settings/horiozontal-block';
import {
    passwordFormSchema,
    PasswordFormTypes,
} from '@/utils/validators/app/forms/password-settings.schema';
import {ProfileHeader} from "@/app/shared/app/account/settings/profile-header";
import {signOut, useSession} from "next-auth/react";
import {useUpdateUserPassword} from "@/services/mutations/user/update-password";
import toast from "react-hot-toast";
import axios from "axios";

export default function PasswordSettingsView() {
    const [isLoading, setLoading] = useState(false);
    const [reset, setReset] = useState({});
    // next auth get session
    const {data: session} = useSession();

    const updateUserPassword = useUpdateUserPassword();

    const onSubmit: SubmitHandler<PasswordFormTypes> = async (data) => {
        setLoading(true);

        updateUserPassword.mutate(data, {
            onError: (error: any) => {
                // Check if the error is an AxiosError and has a response
                if (axios.isAxiosError(error) && error.response) {
                    // Now TypeScript knows error.response exists and its structure
                    toast.error(error.response.data.msg);
                } else {
                    // Handle other errors
                    toast.error("An error occurred");
                }
                setLoading(false);
            },
            onSuccess: () => {
                toast.success(<Text as="b">Successfully changed your password! Please re-log.</Text>);
                setReset({
                    currentPassword: '',
                    newPassword: '',
                    confirmedPassword: '',
                });
                setLoading(false);
                signOut();
            },
        });
    };

    return (
        <>
            <Form<PasswordFormTypes>
                validationSchema={passwordFormSchema}
                resetValues={reset}
                onSubmit={onSubmit}
                className="@container"
                useFormProps={{
                    mode: 'onChange',
                    defaultValues: {
                        // ...settings,
                    },
                }}
            >
                {({register, control, formState: {errors}, getValues}) => {
                    return (
                        <>
                            <ProfileHeader
                                title={`${session?.user?.user?.username}`}
                                bio={`${session?.user?.user?.bio}`}
                                avatar={session?.user?.user?.avatar_url}
                            >
                            </ProfileHeader>

                            <div className="mx-auto w-full max-w-screen-2xl">

                                <HorizontalFormBlockWrapper
                                    title="Current Password"
                                    titleClassName="text-base font-medium"
                                >
                                    <Controller
                                        control={control}
                                        name="currentPassword"
                                        render={({field: {onChange, value}}) => (
                                            <Password
                                                placeholder="Enter your current password"
                                                onChange={onChange}
                                                error={errors.newPassword?.message}
                                            />
                                        )}
                                    />
                                </HorizontalFormBlockWrapper>

                                <HorizontalFormBlockWrapper
                                    title="New Password"
                                    titleClassName="text-base font-medium"
                                >
                                    <Controller
                                        control={control}
                                        name="newPassword"
                                        render={({field: {onChange, value}}) => (
                                            <Password
                                                placeholder="Enter your new password"
                                                helperText={
                                                    getValues().newPassword?.length < 8 &&
                                                    'Your password must be more than 8 characters'
                                                }
                                                onChange={onChange}
                                                error={errors.newPassword?.message}
                                            />
                                        )}
                                    />
                                </HorizontalFormBlockWrapper>

                                <HorizontalFormBlockWrapper
                                    title="Confirm New Password"
                                    titleClassName="text-base font-medium"
                                >
                                    <Controller
                                        control={control}
                                        name="confirmedPassword"
                                        render={({field: {onChange, value}}) => (
                                            <Password
                                                placeholder="Enter your new password again"
                                                onChange={onChange}
                                                error={errors.confirmedPassword?.message}
                                            />
                                        )}
                                    />
                                </HorizontalFormBlockWrapper>

                                <div className="mt-6 flex w-auto items-center justify-end gap-3">
                                    <Button type="button" variant="outline">
                                        Cancel
                                    </Button>
                                    <Button type="submit" variant="solid" isLoading={isLoading}>
                                        Update Password
                                    </Button>
                                </div>
                            </div>
                        </>
                    );
                }}
            </Form>
        </>
    );
}

