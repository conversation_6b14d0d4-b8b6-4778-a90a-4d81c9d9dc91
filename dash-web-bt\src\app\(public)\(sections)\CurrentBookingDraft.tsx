'use client';

import React, {useEffect, useState} from 'react';
import {useBookingSession} from "@/hooks/use-booking-ls";
import moment from "moment";
import { useI18n } from "@/hooks/use-translation";

export default function CurrentBookingDraft() {
    const bookingSession = useBookingSession();
    const { t } = useI18n();

    const getBookingData = () => {
        return bookingSession?.getSessionIfValid() || null;
    }
    const clearBookingData = () => {
        bookingSession?.clearSession();
    }

    const [bookData, setBookData] = useState<Record<any, any> | null>(null);

    const continueBooking = () => {
        window.location.href = '/booking';
    };

    const removeBooking = () => {
        clearBookingData();
        setBookData(getBookingData());
    }

    useEffect(() => {
    }, [bookData]);

    useEffect(() => {
        setBookData(getBookingData())
    }, [bookingSession]);


    return (
        <>
            {
                bookData ? (
                    <section className="fixed bottom-0 inset-x-0 z-50 bg-metallicSilver shadow-lg">
                        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                            <div className="flex justify-between items-center py-3 sm:py-4 md:flex-row">
                                <div className="text-sm md:text-base lg:text-lg font-bold text-blue-950">
                                    {t('booking.draft.message')} <span
                                    className="font-semibold">{bookData?.adults}</span> {t('booking.draft.adults')}
                                    <span
                                        className="font-semibold">{moment(bookData?.schedule_date).format('DD MMMM YYYY')}</span>.
                                    <span className="ml-2 underline cursor-pointer hover:text-teal-950"
                                          onClick={continueBooking}>{t('booking.draft.continue')}</span>

                                    <span className="ml-2 underline cursor-pointer hover:text-teal-950"
                                          onClick={removeBooking}>{t('booking.draft.remove')}</span>
                                </div>
                            </div>
                        </div>
                    </section>
                ) : null
            }
        </>
    );
}
