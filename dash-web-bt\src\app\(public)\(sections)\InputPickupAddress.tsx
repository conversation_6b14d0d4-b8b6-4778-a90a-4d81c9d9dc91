'use client';

import Autocomplete from "@/components/lib/google-map/autocomplete";
import {<PERSON><PERSON>, Loader} from "rizzui";
import React from "react";
import {routes} from "@/config/routes";
import toast from "react-hot-toast";
import {useRouter} from "next/navigation";
import {useGetServiceAvailability} from "@/services/query/booking/public/useGetServiceAvailability";
import { useI18n } from "@/hooks/use-translation";

export default function InputPickupAddress() {
    const router = useRouter();
    const { t } = useI18n();
    const [pickupLocation, setPickupLocation] = React.useState<string | null>(null);

    const {
        data: serviceAvailability,
        isFetching: serviceAvailabilityFetching,
    } = useGetServiceAvailability();

    function setAddress(location: any) {
        setPickupLocation(`${location.formatted_address}|${location.geometry.location.lat()}|${location.geometry.location.lng()}`);
    }

    const scheduleBooking = () => {
        if (pickupLocation) {
            router.push(`${routes.multiStepCustomerBooking}?pickup_location=${encodeURIComponent(pickupLocation)}`);
            return;
        }
        toast.error(t('input_pickup_address.error_message'));
    }

    return (
        <>
            {
                serviceAvailabilityFetching ? (
                    <div className="grid h-32 flex-grow place-content-center items-center">
                        <Loader size="lg"/>
                    </div>
                ) : null
            }
            {
                serviceAvailability?.data.canBook ? (
                    <div className="flex items-center justify-center min-h-screen p-4 text-white text-center">
                        <div>
                            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 text-chillGold">
                                {t('input_pickup_address.welcome')}
                            </h1>
                            <p className="mb-8 text-xl sm:text-2xl lg:text-3xl" style={{color: '#FFFFFF'}}>
                                {t('input_pickup_address.start_journey')}
                            </p>
                            <div
                                className="flex flex-col items-center justify-center space-y-6 md:space-y-0 md:space-x-6 md:flex-row">
                                <Autocomplete
                                    apiKey={process.env.NEXT_PUBLIC_GOOGLE_MAP_API_KEY as string}
                                    mapClassName="rounded-lg"
                                    spinnerClassName="grid h-full w-full place-content-center"
                                    className="w-full sm:w-3/3 lg:w-4/5 rounded-lg text-white font-bold text-2xl bg-transparent"
                                    hideMap={true}
                                    placeholder={t('input_pickup_address.placeholder')}
                                    onPlaceSelect={setAddress}
                                />

                                <Button
                                    className="w-full sm:w-auto py-6 px-10 text-2xl sm:text-3xl font-semibold rounded-lg transition-all duration-300 transform hover:scale-105"
                                    style={{
                                        background: 'linear-gradient(90deg, #D4AF37, #FFD700)',
                                        color: '#FFFFFF',
                                        border: '2px solid #FFD700',
                                        boxShadow: '0 8px 20px rgba(0, 0, 0, 0.3)',
                                    }}
                                    onClick={scheduleBooking}
                                >
                                    {t('input_pickup_address.next_button')}
                                </Button>

                            </div>
                        </div>
                    </div>
                ) : null
            }
        </>
    )
}
