import { Request, Response } from 'express';
import {getPaymentByIntent, updatePayment,} from "../../../../../services/payment";
import {updateBooking} from "../../../../../services/booking";

export const stripeChargeHook_handler = async (req: Request, res: Response) => {
    try {
        const hookPayload = req.body;

        if(hookPayload && ( hookPayload.type === 'charge.succeeded' || hookPayload.type === 'charge.updated' ) && hookPayload.data && hookPayload.data.object) {
            const hookData = hookPayload.data.object;
            const payment = await getPaymentByIntent(hookData.payment_intent);
            if(!payment.data || !payment.success) {
                return res.status(404).json({
                    success: false,
                    message: null,
                    error: null,
                    data: payment,
                });
            }

            if(payment.data && payment?.success) {
                const amountPaid = Number(hookData.amount / 100);
                if(amountPaid) {
                    // console.log('@@@payment ', payment)

                    const updatedPayment = await updatePayment(payment.data.id, {
                        amount_paid: amountPaid,
                        paid_status: amountPaid >= payment.data?.amount,
                        stripe_charge_status: true,
                    });

                    try {
                        await updateBooking(payment.data.id, {
                            status: 'accepted',
                        });
                    } catch (e) {
                        console.error('Failed to update order accepted status. Check manually in admin')
                    }

                    return res.status(200).json({
                        success: true,
                        data: updatedPayment,
                        error: null,
                        message: 'Success',
                    });
                }
                return res.status(200).json({
                    success: false,
                    data: null,
                    error: 'No Amount Paid',
                    message: 'No Amount Paid',
                });
            }
        }

        return res.status(201).json({
            success: true,
            message: 'No action',
            error: null,
            data: null,
        });

    } catch (error: any) {
        return res.status(500).json({
            success: false,
            message: 'Failed to respond to stripe hit',
            error: error.message,
            data: null,
        });
    }
};
