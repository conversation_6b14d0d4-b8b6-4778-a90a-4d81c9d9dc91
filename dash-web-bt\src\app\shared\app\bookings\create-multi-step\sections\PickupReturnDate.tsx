import moment from "moment/moment";
import { DatePicker } from "@/components/lib/ui/datepicker";

import React from "react";
import { Text } from "rizzui";
import { Controller, FieldErrors, Control } from "react-hook-form";

// Function to determine minDate, minTime, and maxTime based on settings
export const calculateDateLimits = (value: string | undefined, minHoursBeforeCurrent: number, startHour: number, endHour: number) => {
    const now = moment();
    let minDate = now.toDate();
    let minTime;
    let maxTime;
    const selectedDate = moment(value);
    const isToday = selectedDate.isSame(now, "day");

    if (isToday) {
      const earliestAvailable = moment().add(minHoursBeforeCurrent, "hours");
      if (earliestAvailable.hour() < startHour) {
        earliestAvailable.set("hour", startHour).set("minute", 0);
      }
      minTime = earliestAvailable.toDate();

      // Disable hours before the current hour on the current day
      const currentHour = now.hour();
      if (currentHour > startHour) {
        minTime = now.toDate();
      }
    } else {
      minTime = moment().set("hour", startHour).set("minute", 0).toDate();
    }

    maxTime = moment()
      .set("hour", endHour === 24 ? 23 : endHour)
      .set("minute", endHour === 24 ? 59 : 0)
      .toDate();

    return { minDate, minTime, maxTime };
  };

export default function PickupReturnDate({
  control,
  errors,
  isTwoWayTrip,
  tripType,
  publicGlobalSettings,
}: {
  control: Control<any>;
  errors: FieldErrors<any>;
  isTwoWayTrip: boolean;
  tripType: string;
  scheduledAtDate: string;
  publicGlobalSettings: any;
}) {
  // Extract global booking settings
  const settings = publicGlobalSettings?.data?.reduce((acc: any, setting: any) => {
    acc[setting.name] = parseInt(setting.value, 10);
    return acc;
  }, {});

  const startHour = settings?.global_booking_start_hour || 0;
  const endHour = settings?.global_booking_end_hour || 24;
  const minHoursBeforeCurrent = settings?.global_booking_min_hours_before_current_hour || 4;


  return (
    <div className="flex flex-col md:grid md:grid-cols-2 md:gap-4">
      <div className="">
        <div className="col-span-1 mt-4">
          <Text
            as="span"
            className="block text-sm mb-1.5 font-medium text-creamyWhite"
          >
            Pickup Date
          </Text>
          
          <Controller
            control={control}
            name="scheduled_at"
            render={({ field: { value, onChange } }) => {
              // Calculate minDate, minTime, and maxTime based on global settings
              const { minDate, minTime, maxTime } = calculateDateLimits(value, minHoursBeforeCurrent, startHour, endHour);

              return (
                <div className="w-full rounded-lg p-2">
                  <DatePicker
                    selected={value ? new Date(value) : null}
                    onChange={(date: Date) => {
                      const { minDate } = calculateDateLimits(date?.toISOString(), minHoursBeforeCurrent, startHour, endHour);
                      // Validation to ensure the selected date is within allowed range
                      if (date) {
                        if (date >= minDate) {
                          onChange(date.toISOString());
                        } else {
                          onChange(null);
                        }
                      } else {
                        onChange(null);
                      }
                    }}
                    minDate={minDate}
                    minTime={minTime}
                    maxTime={maxTime}
                    dateFormat="MMMM d, yyyy h:mm aa"
                    placeholderText="Select Pickup Date and Time"
                    showTimeSelect={true}
                    popperPlacement="bottom"
                    inputProps={{
                      variant: "text",
                      inputClassName: "p-0 px-1 h-auto [&_input]:text-ellipsis",
                    }}
                    className="w-40 rounded border [&_.rizzui-input-container]:px-3 [&_.rizzui-input-container]:py-1.5 w-full"
                  />
                </div>
              );
            }}
          />
          <div
            role="alert"
            className="text-red text-[13px] mt-0.5 rizzui-input-error-text"
          >
            {errors.scheduled_at?.message as string}
          </div>
        </div>
      </div>

      {
        isTwoWayTrip && tripType === 'transfer' ? (
            <div className="">
            <div className="col-span-1 mt-4">
              <Text
                as="span"
                className="block text-sm mb-1.5 font-medium text-creamyWhite"
              >
                Return Date
              </Text>
              
              <Controller
                control={control}
                name="return_trip_time"
                render={({ field: { value, onChange } }) => {
                  // Calculate minDate, minTime, and maxTime based on global settings
                  const { minDate, minTime, maxTime } = calculateDateLimits(value, minHoursBeforeCurrent, startHour, endHour);
    
                  return (
                    <div className="w-full rounded-lg p-2">
                      <DatePicker
                        selected={value ? new Date(value) : null}
                        onChange={(date: Date) => {
                          const { minDate } = calculateDateLimits(date?.toISOString(), minHoursBeforeCurrent, startHour, endHour);
                          // Validation to ensure the selected date is within allowed range
                          if (date) {
                            if (date >= minDate) {
                              onChange(date.toISOString());
                            } else {
                              onChange(null);
                            }
                          } else {
                            onChange(null);
                          }
                        }}
                        minDate={minDate}
                        minTime={minTime}
                        maxTime={maxTime}
                        dateFormat="MMMM d, yyyy h:mm aa"
                        placeholderText="Select Pickup Date and Time"
                        showTimeSelect={true}
                        popperPlacement="bottom"
                        inputProps={{
                          variant: "text",
                          inputClassName: "p-0 px-1 h-auto [&_input]:text-ellipsis",
                        }}
                        className="w-40 rounded border [&_.rizzui-input-container]:px-3 [&_.rizzui-input-container]:py-1.5 w-full"
                      />
                    </div>
                  );
                }}
              />
              <div
                role="alert"
                className="text-red text-[13px] mt-0.5 rizzui-input-error-text"
              >
                {errors.scheduled_at?.message as string}
              </div>
            </div>
          </div>
        ) : null
      }
    </div>
  );
}
