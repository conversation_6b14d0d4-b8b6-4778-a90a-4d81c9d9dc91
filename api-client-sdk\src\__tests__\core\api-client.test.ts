import axios from 'axios';
import { apiClient } from '../../core/api-client';
import { ApiResponse } from '../../core/types';

// Mock axios
const mockAxios = axios as jest.Mocked<typeof axios>;

describe('ApiClient', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('setAuthToken', () => {
    it('should set auth token from string', () => {
      const token = 'test-token';
      apiClient.setAuthToken(token);
      
      expect(apiClient['axiosInstance'].defaults.headers.common['Authorization']).toBe(`Bearer ${token}`);
    });

    it('should set auth token from object', () => {
      const token = { token: 'test-token' };
      apiClient.setAuthToken(token);
      
      expect(apiClient['axiosInstance'].defaults.headers.common['Authorization']).toBe(`Bearer ${token.token}`);
    });
  });

  describe('clearAuthToken', () => {
    it('should clear auth token', () => {
      // First set a token
      apiClient.setAuthToken('test-token');
      expect(apiClient['axiosInstance'].defaults.headers.common['Authorization']).toBeDefined();
      
      // Then clear it
      apiClient.clearAuthToken();
      expect(apiClient['axiosInstance'].defaults.headers.common['Authorization']).toBeUndefined();
    });
  });

  describe('HTTP methods', () => {
    const mockResponse = { data: { id: 1, name: 'Test' } };
    
    beforeEach(() => {
      // Mock successful responses for all methods
      apiClient['axiosInstance'].get = jest.fn().mockResolvedValue(mockResponse);
      apiClient['axiosInstance'].post = jest.fn().mockResolvedValue(mockResponse);
      apiClient['axiosInstance'].put = jest.fn().mockResolvedValue(mockResponse);
      apiClient['axiosInstance'].delete = jest.fn().mockResolvedValue(mockResponse);
      apiClient['axiosInstance'].patch = jest.fn().mockResolvedValue(mockResponse);
    });

    it('should make GET request', async () => {
      const result = await apiClient.get('/test', { param: 'value' });
      
      expect(apiClient['axiosInstance'].get).toHaveBeenCalledWith('/test', {
        params: { param: 'value' },
      });
      expect(result).toEqual({ success: true, data: mockResponse.data });
    });

    it('should make POST request', async () => {
      const data = { name: 'Test' };
      const result = await apiClient.post('/test', data);
      
      expect(apiClient['axiosInstance'].post).toHaveBeenCalledWith('/test', data, undefined);
      expect(result).toEqual({ success: true, data: mockResponse.data });
    });

    it('should make PUT request', async () => {
      const data = { name: 'Test' };
      const result = await apiClient.put('/test', data);
      
      expect(apiClient['axiosInstance'].put).toHaveBeenCalledWith('/test', data, undefined);
      expect(result).toEqual({ success: true, data: mockResponse.data });
    });

    it('should make DELETE request', async () => {
      const result = await apiClient.delete('/test');
      
      expect(apiClient['axiosInstance'].delete).toHaveBeenCalledWith('/test', undefined);
      expect(result).toEqual({ success: true, data: mockResponse.data });
    });

    it('should make PATCH request', async () => {
      const data = { name: 'Test' };
      const result = await apiClient.patch('/test', data);
      
      expect(apiClient['axiosInstance'].patch).toHaveBeenCalledWith('/test', data, undefined);
      expect(result).toEqual({ success: true, data: mockResponse.data });
    });

    it('should handle errors', async () => {
      const error = new Error('Test error');
      apiClient['axiosInstance'].get = jest.fn().mockRejectedValue(error);
      
      const result = await apiClient.get('/test');
      
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });
}); 