'use client';

import Link from 'next/link';
import {Text, Badge, Tooltip, Checkbox, ActionIcon} from 'rizzui';
import {HeaderCell} from '@/components/lib/ui/table';
import EyeIcon from '@/components/lib/icons/eye';
import PencilIcon from '@/components/lib/icons/pencil';
import AvatarCard from '@/components/lib/ui/avatar-card';
import DateCell from '@/components/lib/ui/date-cell';
import DeletePopover from '@/app/shared/lib/delete-popover';
import {routes} from "@/config/routes";

type Columns = {
    data: any[];
    sortConfig?: any;
    handleSelectAll: any;
    checkedItems: string[];
    onDeleteItem: (id: string) => void;
    onHeaderCellClick: (value: string) => void;
    onChecked?: (id: string) => void;
};

export const getColumns = ({
                               data,
                               sortConfig,
                               checkedItems,
                               onDeleteItem,
                               onHeaderCellClick,
                               handleSelectAll,
                               onChecked,
                           }: Columns) => [
    {
        title: (
            <div className="ps-2">
                <Checkbox
                    title={'Select All'}
                    onChange={handleSelectAll}
                    checked={checkedItems.length === data.length}
                    className="cursor-pointer"
                />
            </div>
        ),
        dataIndex: 'checked',
        key: 'checked',
        width: 30,
        render: (_: any, row: any) => (
            <div className="inline-flex ps-2">
                <Checkbox
                    className="cursor-pointer"
                    checked={checkedItems.includes(row.id)}
                    {...(onChecked && { onChange: () => onChecked(row.id) })}
                />
            </div>
        ),
    },
    {
        title: (
            <HeaderCell
                title="First Name"
                sortable
                ascending={
                    sortConfig?.direction === 'asc' && sortConfig?.key === 'first_name'
                }
            />
        ),
        onHeaderCell: () => onHeaderCellClick('first_name'),
        dataIndex: 'first_name',
        key: 'first_name',
        width: 200,
        render: (value: string) => {
            return <Text className="font-medium text-gray-700 dark:text-gray-600 text-center">
                {value}
            </Text>
        },
    },
    {
        title: (
            <HeaderCell
                title="Last Name"
                sortable
                ascending={
                    sortConfig?.direction === 'asc' && sortConfig?.key === 'last_name'
                }
            />
        ),
        onHeaderCell: () => onHeaderCellClick('last_name'),
        dataIndex: 'last_name',
        key: 'last_name',
        width: 200,
        render: (value: string) => {
            return <Text className="font-medium text-gray-700 dark:text-gray-600 text-center">
                {value}
            </Text>
        },
    },
    {
        title: <HeaderCell title="Phone Number"/>,
        dataIndex: 'phone_number',
        key: 'phone_number',
        width: 120,
        render: (value: any) => `${value}`,
    },
    {
        title: (
            <HeaderCell
                title="Created"
                sortable
                ascending={
                    sortConfig?.direction === 'asc' && sortConfig?.key === 'created_at'
                }
            />
        ),
        onHeaderCell: () => onHeaderCellClick('created_at'),
        dataIndex: 'created_at',
        key: 'created_at',
        width: 200,
        render: (value: Date) => <DateCell date={value}/>,
    },
    {
        title: <></>,
        dataIndex: 'action',
        key: 'action',
        width: 140,
        render: (_: string, row: any) => (
            <div className="flex items-center justify-end gap-3 pe-3">
                <Tooltip
                    size="sm"
                    content={'Edit Customer'}
                    placement="top"
                    color="invert"
                >
                    <Link href={`${routes.customerEdit(row.id)}`}>
                        <ActionIcon
                            as="span"
                            size="sm"
                            variant="outline"
                            className="hover:!border-gray-900 hover:text-gray-700"
                        >
                            <PencilIcon className="h-4 w-4"/>
                        </ActionIcon>
                    </Link>
                </Tooltip>
                {/*<Tooltip*/}
                {/*    size="sm"*/}
                {/*    content={'View Customer'}*/}
                {/*    placement="top"*/}
                {/*    color="invert"*/}
                {/*>*/}
                {/*    <Link href={'/'}>*/}
                {/*        <ActionIcon*/}
                {/*            as="span"*/}
                {/*            size="sm"*/}
                {/*            variant="outline"*/}
                {/*            className="hover:!border-gray-900 hover:text-gray-700"*/}
                {/*        >*/}
                {/*            <EyeIcon className="h-4 w-4"/>*/}
                {/*        </ActionIcon>*/}
                {/*    </Link>*/}
                {/*</Tooltip>*/}
                <DeletePopover
                    title={`Delete Customer`}
                    description={`Are you sure you want to delete this #${row.id} customer ?`}
                    onDelete={() => onDeleteItem(row.id)}
                />
            </div>
        ),
    },
];
