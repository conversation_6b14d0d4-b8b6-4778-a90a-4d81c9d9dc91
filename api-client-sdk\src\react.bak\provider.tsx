import React, { createContext, useContext, ReactNode } from 'react';
import { QueryClient, QueryClientProvider } from 'react-query';
import { IRootStore, initializeStore } from '../state/root-store';

// Create context
const StoreContext = createContext<IRootStore | null>(null);

// Default query client config
const defaultQueryClientOptions = {
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
};

// Props interface
interface ApiProviderProps {
  children: ReactNode;
  store?: IRootStore;
  queryClient?: QueryClient;
}

/**
 * ApiProvider component that provides the store and query client to the app
 * @param props Component props
 * @returns Provider component
 */
export const ApiProvider: React.FC<ApiProviderProps> = ({
  children,
  store,
  queryClient = new QueryClient(defaultQueryClientOptions),
}) => {
  // Initialize store if not provided
  const storeInstance = store || React.useMemo(() => initializeStore(), []);

  return (
    <QueryClientProvider client={queryClient}>
      <StoreContext.Provider value={storeInstance}>
        {children}
      </StoreContext.Provider>
    </QueryClientProvider>
  );
};

/**
 * Hook to access the store instance
 * @returns The store instance
 * @throws Error if used outside of ApiProvider
 */
export function useStore(): IRootStore {
  const store = useContext(StoreContext);
  if (!store) {
    throw new Error('useStore must be used within an ApiProvider');
  }
  return store;
} 