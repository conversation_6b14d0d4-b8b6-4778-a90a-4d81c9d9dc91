import { routes } from '@/config/routes';
import PageHeader from '@/app/shared/lib/page-header';
import { metaObject } from '@/config/site.config';
import CreateEdit from '@/app/shared/app/zones/create-edit';

export const metadata = {
  ...metaObject('Create Zone'),
};

const pageHeader = {
  title: 'Create Zone',
  breadcrumb: [
    {
      href: routes.dashboard,
      name: 'Dashboard',
    },
    {
      href: routes.zones,
      name: 'Zones',
    },
    {
      name: 'Create Zone',
    },
  ],
};

export default function CreatePage() {
  return (
    <>
      <PageHeader title={pageHeader.title} breadcrumb={pageHeader.breadcrumb}>
      </PageHeader>

      <CreateEdit />
    </>
  );
}
