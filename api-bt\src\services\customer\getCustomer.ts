import { getRepository } from 'typeorm';
import { Customer } from '../../models/Customer'; // Update the path according to your project structure
import { interpretDatabaseError } from '../../utils/interpretDatabaseError'; // Adjust import path as needed

export const getCustomer = async (id: string) => {
  const customerRepository = getRepository(Customer);
  try {
    const customer = await customerRepository.findOne(id, { relations: ['user', 'tickets'] });
    if (!customer) {
      return { success: false, error: 'Customer not found' };
    }
    return { success: true, data: customer };
  } catch (error) {
    return { success: false, error: interpretDatabaseError(error) };
  }
};
