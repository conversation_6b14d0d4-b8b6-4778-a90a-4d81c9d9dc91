import * as z from 'zod';
import {CustomerSchema} from './createCustomerSchema';
export const createBookingSchema = z.object({
    trip_type: z.enum(['transfer', 'hourly']).default('transfer'), // Ensure it matches your TripType enum

    pickup_address: z.string().min(1, 'Pickup address is required'),
    pickup_lat: z.string(),
    pickup_long: z.string(),

    destination_address: z.string().optional(),
    destination_lat: z.string().optional(),
    destination_long: z.string().optional(),

    one_way_trip: z.boolean().default(true),
    return_trip_time: z.string().optional().nullable(),

    adults: z.number().min(1, 'At least one adult is required'),
    children: z.number().min(0).optional(),
    children_chairs_under_five: z.number().min(0).optional(),
    children_chairs_above_five: z.number().min(0).optional(),
    pets: z.number().min(0).optional(),

    flight_number: z.string(),

    status: z.enum(['pending', 'accepted', 'rejected' ,'cancelled', 'completed']).default('pending'), // Match your BookingStatus enum
    scheduled_at: z.string(),

    other_details: z.string().optional(),

    // Validation for numeric types
    recommended_amount_to_pay: z.number().min(0, 'Recommended amount cannot be negative'),
    estimated_service_hours: z.number().min(0, 'Estimated service hours cannot be negative').optional(),
    final_amount_to_pay: z.number().min(0, 'Final amount cannot be negative'),
    trip_distance: z.number().min(0, 'Trip distance cannot be negative').optional(),
    return_trip_distance: z.number().min(0, 'Trip distance cannot be negative').optional(),
    trip_duration: z.number().min(0, 'Trip duration cannot be negative').optional(),
    return_trip_duration: z.number().min(0, 'Trip duration cannot be negative').optional(),

    booking_finished_at: z.date().optional(),
    driver_observation: z.string().optional(),

    // Omit relationship and metadata fields since they are likely managed internally:
    // car_type, bookingBags, customer, payment, created_at, updated_at, updated_by
    customer_first_name: z.string(),
    customer_last_name: z.string().nullable().optional(),
    customer_phone_number: z.string(),
    customer_email: z.string().email(),
    bags: z.array(z.any()).optional().nullable(),
    payment_type: z.string().uuid().nullable().optional(),
    customer: CustomerSchema.or(z.string().uuid()).optional().nullable(),
    car_type: z.string().uuid(),

});

export type CreateBookingInput = z.infer<typeof createBookingSchema>;
