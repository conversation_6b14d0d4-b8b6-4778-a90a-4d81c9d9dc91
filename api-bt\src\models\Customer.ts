import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { User } from './User';
import { Booking } from './Booking';
// eslint-disable-next-line import/no-cycle
import { Ticket } from './Ticket'; // Ensure this import is correctly pointing to your Ticket entity

@Entity()
export class Customer {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ length: 50 })
  first_name: string;

  @Column({ length: 50 })
  last_name: string;

  @Column({ length: 50,})
  email: string;

  @Column({ length: 30, nullable: false, })
  phone_number: string;

  @Column({ default: false, })
  is_phone_validated: boolean;

  @OneToOne(() => User)
  @JoinColumn({ name: "user_id" })
  user: User;

  @Column('text', { nullable: true })
  stripe_customer_id: string;

  // Adding the OneToMany relationship for tickets
  @OneToMany(() => Ticket, ticket => ticket.customer)
  tickets: Ticket[];

  // Adding the OneToMany relationship for bookings
  @OneToMany(() => Booking, booking => booking.customer)
  orders: Booking[];

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ nullable: true })
  updated_by: number;
}
