import {MigrationInterface, QueryRunner, TableColumn} from "typeorm";

export class addIsphonevalidatedToCustomer1714011789819 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        const customerTable = await queryRunner.getTable('customer');
        if (customerTable) {
            if (!customerTable.columns.find(column => column.name === 'disabled')) {
                await queryRunner.addColumn('customer', new TableColumn({
                    name: 'is_phone_validated',
                    type: 'boolean',
                    default: false,
                }));
            }
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropColumn('customer', 'is_phone_validated');
    }

}
