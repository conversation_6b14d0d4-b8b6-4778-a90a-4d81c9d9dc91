import { getRepository } from 'typeorm';
import { User } from '../../models/User';
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const getUser = async (id: string) => {
  const userRepository = getRepository(User);
  try {
    const user = await userRepository.findOne(id, {
      relations: ["role", "driver"],
      select: ['id', 'first_name', 'last_name', 'email', 'created_at', 'role', 'driver', 'country', 'avatar_url', 'bio', 'username']
    });
    if (!user) {
      return { success: false, data: null, error: 'User not found' };
    }
    return { success: true, data: user, error: null };
  } catch (error) {
    const interpretedError = interpretDatabaseError(error);
    return { success: false, data: null, error: interpretedError };
  }
};
