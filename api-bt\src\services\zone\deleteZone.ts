import { getRepository } from 'typeorm';
import { Zone } from '../../models/Zone'; // Adjust the path as necessary
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const deleteZone = async (id: string) => {
  const zoneRepository = getRepository(Zone);
  try {
    const deleteResult = await zoneRepository.delete(id);
    if (deleteResult.affected === 0) {
      return { success: false, data: null, error: 'Zone not found' };
    }
    return { success: true, data: { id }, error: null };
  } catch (error) {
    const interpretedError = interpretDatabaseError(error);
    return { success: false, data: null, error: interpretedError };
  }
};
