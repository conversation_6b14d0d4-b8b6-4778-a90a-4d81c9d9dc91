import {MigrationInterface, QueryRunner} from "typeorm";

export class removeNameFromPayment1710114709043 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Remove the 'name' column from 'payment' table
        await queryRunner.query(`ALTER TABLE "payment" DROP COLUMN "name"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Create the 'name' column back to 'payment' table if rolling back
        await queryRunner.query(`ALTER TABLE "payment" ADD "name" varchar(50) NOT NULL`);
    }
}
