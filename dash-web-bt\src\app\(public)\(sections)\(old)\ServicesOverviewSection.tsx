import React from 'react';
import Container from '@/app/(public)/(sections)/(old)/container';
import { FaTaxi, FaBriefcase, FaAmilia, FaGolfBall } from 'react-icons/fa'; // Icons for taxi, business, family, and golf

const ServicesOverviewSection: React.FC = () => {
    return (
        <section className="py-16 text-white border-b-2-chillGold" id="services">
            <Container>
                <div className="text-center mb-12">
                    <h2 className="text-4xl font-semibold mb-6 text-chillGold">
                        A Service for Every Occasion
                    </h2>
                    <p className="text-lg">
                        Discover the flexibility and variety of our transfer services, designed to cater to all your travel needs.
                    </p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                    <ServiceItem icon={<FaTaxi className="mx-auto text-6xl text-red-500 mb-4" />} title="Airport Transfers" description="Punctual and reliable transfers to and from the airport, for a stress-free journey." />
                    <ServiceItem icon={<FaBriefcase className="mx-auto text-6xl text-blue-500 mb-4" />} title="Business Travel" description="Executive cars and professional drivers to make your business travel smooth and efficient." />
                    <ServiceItem icon={<FaAmilia className="mx-auto text-6xl text-green-500 mb-4" />} title="Family Vacations" description="Comfortable and spacious vehicles equipped to accommodate your entire family and luggage." />
                    <ServiceItem icon={<FaGolfBall className="mx-auto text-6xl text-yellow-500 mb-4" />} title="Golf Trips" description="Travel with ease to the finest golf courses, with ample room for you and your golf equipment." />
                </div>
            </Container>
        </section>
    );
};

interface ServiceItemProps {
    icon: JSX.Element;
    title: string;
    description: string;
}

const ServiceItem: React.FC<ServiceItemProps> = ({ icon, title, description }) => {
    return (
        <div className="text-center">
            {icon}
            <h3 className="text-xl font-semibold mb-2 text-chillGold">{title}</h3>
            <p className="">{description}</p>
        </div>
    );
};

export default ServicesOverviewSection;
