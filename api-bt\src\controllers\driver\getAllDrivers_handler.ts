// controllers/driver/getAllTickets_handler.ts
import { Request, Response } from 'express';
import { getAllDrivers } from '../../services/driver';

export const getAllDrivers_handler = async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string, 10) || 1;
    const limit = parseInt(req.query.limit as string, 10) || 10;
    const search = (req.query.search ? JSON.parse(req.query.search as string) : {}) as Record<string, any>;
    const filterWithCar = req.query.filterWithCar === 'true'; // Adjusted comparison for string 'true'
    const paginatedResult = await getAllDrivers(page, limit, search, filterWithCar);
    return res.json(paginatedResult);
  } catch (err: any) {
    return res.status(400).json({ error: err.message });
  }
};
