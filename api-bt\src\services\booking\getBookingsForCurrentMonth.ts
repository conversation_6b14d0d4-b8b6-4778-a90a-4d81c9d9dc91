import { getRepository, Between } from 'typeorm';
import { Booking } from '../../models/Booking';

export const getBookingsForMonth = async (monthOffset = 0) => {
    const bookingRepository = getRepository(Booking);

    // Get the current date and modify it based on the month offset
    const targetDate = new Date();
    targetDate.setMonth(targetDate.getMonth() + monthOffset);

    // Calculate the start and end dates for the target month
    const firstDayOfMonth = new Date(targetDate.getFullYear(), targetDate.getMonth(), 1);
    const lastDayOfMonth = new Date(targetDate.getFullYear(), targetDate.getMonth() + 1, 0);

    const options: any = {
        where: {
            created_at: Between(firstDayOfMonth.toISOString(), lastDayOfMonth.toISOString())
        },
        relations: ['car_type', 'customer', 'payment', 'car', 'car.type'],
    };

    const bookings = await bookingRepository.find(options);

    return bookings;
};
