import {
  <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, OneToMany,
} from 'typeorm';
import { CarType } from './CarType';
import { Driver } from './Driver';

@Entity()
export class Car {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ length: 50 })
  name: string;

  @Column('text', { nullable: true, default: 'https://via.placeholder.com/150' })
  image?: string;

  @ManyToOne(() => CarType)
  @JoinColumn({ name: "type" })
  type: CarType;

  @OneToMany(() => Driver, driver => driver.car)
  drivers: Driver[];

  @Column({ length: 50, nullable: true })
  brand: string;

  @Column({ length: 50, nullable: true })
  model: string;

  @Column('boolean', { default: true })
  allow_pets: boolean;

  @Column('text', { nullable: true, unique: true })
  licence_plate: string;

  @Column('int')
  bags_capacity: number;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ nullable: true })
  updated_by: number;
}
