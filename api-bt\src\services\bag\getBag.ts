import { getRepository } from 'typeorm';
import { Bag } from '../../models/Bag'; // Adjust the path as necessary
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const getBag = async (id: string) => {
  const bagRepository = getRepository(Bag);
  try {
    const bag = await bagRepository.findOne(id);
    if (!bag) {
      return { success: false, data: null, error: 'Bag not found' };
    }
    return { success: true, data: bag, error: null };
  } catch (error) {
    const interpretedError = interpretDatabaseError(error);
    return { success: false, data: null, error: interpretedError };
  }
};
