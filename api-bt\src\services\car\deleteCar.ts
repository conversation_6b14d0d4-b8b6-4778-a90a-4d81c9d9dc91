import { getRepository } from 'typeorm';
import { Car } from '../../models/Car';
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const deleteCar = async (id: string) => {
  const carRepository = getRepository(Car);
  try {
    const deleteResult = await carRepository.delete(id);
    if (deleteResult.affected === 0) {
      return { success: false, data: null, error: 'Car not found' };
    }
    return { success: true, data: { id }, error: null };
  } catch (error) {
    const interpretedError = interpretDatabaseError(error);
    return { success: false, data: null, error: interpretedError };
  }
};
