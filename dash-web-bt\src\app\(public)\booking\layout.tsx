'use client';

import Header from '@/app/(public)/booking/header';
// import {useStepperOne} from "@/app/shared/app/bookings/create-multi-step";
// import {useEffect, useState} from "react";
export default function MultiStepLayoutTwo({
  children,
}: {
  children: React.ReactNode;
}) {
  // const [currentGradientFrom, setCurrentGradientFrom] = useState('16222A'); // Set initial values to avoid null in style
  // const [currentGradientTo, setCurrentGradientTo] = useState('3A6073');
  // const {step, gotoPrevStep} = useStepperOne();
  //
  // useEffect(() => {
  //   console.log('!!! step', step);
  //
  //   switch (step) {
  //     case 0:
  //       setCurrentGradientFrom('2C5364');
  //       setCurrentGradientTo('0F2027');
  //       break;
  //     case 1:
  //       setCurrentGradientFrom('243B55');
  //       setCurrentGradientTo('141E30');
  //       break;
  //     case 2:
  //       setCurrentGradientFrom('203A43');
  //       setCurrentGradientTo('0F2027');
  //       break;
  //     case 3:
  //       setCurrentGradientFrom('4CA1AF');
  //       setCurrentGradientTo('2C3E50');
  //       break;
  //     case 4:
  //       setCurrentGradientFrom('C6426E');
  //       setCurrentGradientTo('642B73');
  //       break;
  //     case 5:
  //       setCurrentGradientFrom('3A6073');
  //       setCurrentGradientTo('16222A');
  //       break;
  //     case 6:
  //       setCurrentGradientFrom('4a4e69');
  //       setCurrentGradientTo('20232a');
  //       break;
  //     default:
  //       setCurrentGradientFrom('3A6073');
  //       setCurrentGradientTo('16222A');
  //       break;
  //   }
  // }, [step]);

  return (
      // <div className={`min-h-screen bg-gradient-to-r from-[#${currentGradientFrom}] to-[#${currentGradientTo}] @container`}>
      // <div className={`min-h-screen bg-gradient-to-r from-[#1B4F72] to-[#D35400] @container`}>
      <div className={`min-h-screen bg-[#333333] @container`}>
        <Header />
        {children}
      </div>
  );
}
