import { getRepository } from 'typeorm';
import { Role } from '../../models/Role';
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const getRole = async (id: string) => {
  const roleRepository = getRepository(Role);
  try {
    const role = await roleRepository.findOne(id, {
        relations: ["users"], // Uncomment if you need to load relations
    });
    if (!role) {
      return { success: false, data: null, error: 'Role not found' };
    }
    return { success: true, data: role, error: null };
  } catch (error) {
    const interpretedError = interpretDatabaseError(error);
    return { success: false, data: null, error: interpretedError };
  }
};
