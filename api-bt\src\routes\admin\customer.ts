import { Router } from 'express';
import {
  createCustomer_handler,
  deleteCustomer_handler,
  getCustomer_handler,
  getAllCustomers_handler,
  updateCustomer_handler,
} from '../../controllers/customer';

export const customerRouter = Router();

customerRouter.post('/customers', createCustomer_handler);
customerRouter.delete('/customers/:id', deleteCustomer_handler);
customerRouter.get('/customers/:id', getCustomer_handler);
customerRouter.get('/customers', getAllCustomers_handler);
customerRouter.put('/customers/:id', updateCustomer_handler);
