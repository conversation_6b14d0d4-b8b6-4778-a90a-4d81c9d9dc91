import {MigrationInterface, QueryRunner, TableColumn} from "typeorm";

export class addedHourlyRatePropToCartypepriceTable1711153890523 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        const carTypePriceTable = await queryRunner.getTable('car_type_price');
        if (carTypePriceTable) {
            if (!carTypePriceTable.columns.find(column => column.name === 'hourly_rate')) {
                await queryRunner.addColumn('car_type_price', new TableColumn({
                    name: 'hourly_rate',
                    type: 'integer',
                    isNullable: true, // Allow null values
                }));
            }
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropColumn('car_type_price', 'hourly_rate');
    }
}
