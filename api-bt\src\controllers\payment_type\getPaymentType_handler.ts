import { Request, Response } from 'express';
import {
    getPaymentType,
} from '../../services/payment-type'; // Adjust the import path as necessary

export const getPaymentTypeHandler = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        const result = await getPaymentType(id);
        if (!result.success) {
            return res.status(404).json(result);
        }
        return res.json(result);
    } catch (error: any) {
        return res.status(500).json({
            success: false,
            message: 'Failed to get payment type',
            error: error.message,
        });
    }
};
