import { getRepository } from 'typeorm';
import { CarTypePrice } from '../../models/CarTypePrice';
import { interpretDatabaseError } from '../../utils/interpretDatabaseError';

export const deleteCarTypePrice = async (id: string) => {
    const carTypePriceRepository = getRepository(CarTypePrice);
    try {
        const deleteResult = await carTypePriceRepository.delete(id);
        if (deleteResult.affected === 0) {
            return { success: false, data: null, error: 'CarTypePrice not found' };
        }
        return { success: true, data: { id }, error: null };
    } catch (error) {
        const interpretedError = interpretDatabaseError(error);
        return { success: false, data: null, error: interpretedError };
    }
};
